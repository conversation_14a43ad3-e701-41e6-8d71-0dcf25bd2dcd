require_relative 'boot'

require "rails"
# Pick the frameworks you want:
require "active_model/railtie"
require "active_job/railtie"
require "active_record/railtie"
require "active_storage/engine"
require "action_controller/railtie"
require "action_mailer/railtie"
require "action_view/railtie"
require "action_cable/engine"
require "sprockets/railtie"
require "rails/test_unit/railtie"
require "newrelic_rpm"
require_relative "../lib/middleware/sidekiq_custom_css"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module CircleApi
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.1

    ############################################################
    ### Override 7.1 defaults during migration from 7.0 ###
    ############################################################

    config.active_record.run_after_transaction_callbacks_in_order_defined = false
    config.add_autoload_paths_to_load_path = true
    config.eager_load_paths << Rails.root.join('app', 'errors')
    ############################################################
    ### Override END ###
    ############################################################

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true

    config.encoding = "utf-8"

    config.time_zone = "Chennai"

    config.active_storage.analyzers = []

    config.active_record.yaml_column_permitted_classes = [Symbol, Date, Time, ActiveSupport::TimeWithZone, ActiveSupport::TimeZone]

    config.middleware.insert_before 0, Rack::Cors do
      allow do
        origins /[a-z\-]+.thecircleapp.in/
        resource '/admin/*',
                 headers: :any,
                 methods: [:get, :post, :delete, :put, :patch, :options, :head],
                 credentials: true
        resource '/agent-partner/*',
                 headers: :any,
                 methods: [:get, :post, :delete, :put, :patch, :options, :head],
                 credentials: true

        resource '/media-service/poster-page',
                 headers: :any,
                 methods: [:get, :post, :delete, :put, :patch, :options, :head],
                 credentials: true
      end
      allow do
        origins '*'
        resource '*', headers: :any, methods: [:get, :post, :delete, :put, :patch, :options, :head]
      end
    end

    # don't generate RSpec tests for views and helpers.
    config.generators do |g|
      g.test_framework :rspec, fixture: true
      g.fixture_replacement :factory_bot, dir: 'spec/factories'

      g.view_specs false
      g.helper_specs false
    end

    # Middleware for ActiveAdmin
    config.middleware.use Rack::MethodOverride
    config.middleware.use ActionDispatch::Flash
    config.middleware.use ActionDispatch::Cookies
    config.middleware.use SidekiqCustomCss

    if Rails.env.production?
      config.session_store :cookie_store,
                           key: '__app_session',
                           expire_after: 1.day,
                           secure: true,
                           domain: '.thecircleapp.in',
                           same_site: :none,
                           httponly: false
      config.middleware.use config.session_store, config.session_options
    else
      config.middleware.use ActionDispatch::Session::CookieStore, key: '__app_session'
    end

    def load_console(app = self)
      super
      if File.exist?((project_specific_irbrc = File.join(Rails.root, ".irbrc")))
        puts "Loading project specific .irbrc ..."
        load(project_specific_irbrc)
      end
    end
  end
end

if Rails.env.development?
  Rails.application.config.hosts << /[a-z0-9\-\.]+\.ngrok\-free\.app/
  Rails.application.config.hosts << /[a-z]+\-dev\.thecircleapp\.in/
  Rails.application.config.hosts << /praja\-api:3000/
end

ActiveSupport.on_load(:active_record) do
  require Rails.root.join('app/modules/transliteration/model')
  extend Transliteration::Model
end
