require 'sidekiq/web'
require 'sidekiq-unique-jobs'
require "sidekiq/throttled"
require 'sidekiq-assured-jobs'
# require 'rack/protection'

rails_root = Rails.root || File.dirname(__FILE__) + '/../..'
rails_env = Rails.env || 'development'
redis_config = YAML.load(ERB.new(File.read("#{rails_root}/config/redis.yml")).result)
redis_config.merge! redis_config.fetch(rails_env, {})
redis_config.symbolize_keys!

Sidekiq.configure_server do |config|

  if redis_config.key?(:sentinels)
    config.redis = {
      url: "redis://mymaster",
      sentinels: redis_config[:sentinels],
      role: :master,
      db: redis_config[:sidekiq_db],
      namespace: "sidekiq_#{rails_env}"
    }
  else
    config.redis = { url: "redis://#{redis_config[:host]}:#{redis_config[:port]}/#{redis_config[:sidekiq_db]}", namespace: "sidekiq_#{rails_env}" }
  end

  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end

  config.server_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Server
  end

  SidekiqUniqueJobs::Server.configure(config)

  # Load schedule in production and development  environments to show crons in dashboard
  # But only enable actual execution in production
  if (Rails.env.production? && ENV['DEPLOYMENT'] != 'preprod') || Rails.env.development?
    config.on(:startup) do
      schedule = YAML.safe_load(File.read(Rails.root.join('config', 'sidekiq_scheduler.yml')))
      
      Sidekiq.schedule = schedule
      SidekiqScheduler::Scheduler.instance.reload_schedule!
      
      # Disable all cron jobs in development environment
      if Rails.env.development?
        schedule.each do |job_name, job_config|
          if Sidekiq.get_schedule(job_name)
            job_config_copy = job_config.dup
            job_config_copy['enabled'] = false
            Sidekiq.set_schedule(job_name, job_config_copy)
          end
        end
      end
    end
  end
end

Sidekiq.configure_client do |config|
  if redis_config.key?(:sentinels)
    config.redis = {
      url: "redis://mymaster",
      sentinels: redis_config[:sentinels],
      role: :master,
      db: redis_config[:sidekiq_db],
      namespace: "sidekiq_#{rails_env}"
    }
  else
    config.redis = { url: "redis://#{redis_config[:host]}:#{redis_config[:port]}/#{redis_config[:sidekiq_db]}", namespace: "sidekiq_#{rails_env}" }
  end
  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end
end

# prevent dying jobs from littering uniqueness keys
Sidekiq.configure_server do |config|
  config.death_handlers << ->(job, _ex) do
    digest = job['lock_digest']
    SidekiqUniqueJobs::Digests.new.delete_by_digest(digest) if digest
  end
end

Sidekiq::AssuredJobs.configure do |config|
  config.namespace = "praja_assured_jobs"
  config.heartbeat_interval = 10  # seconds
  config.heartbeat_ttl = 15       # seconds
  config.recovery_lock_ttl = 60  # seconds
  config.delayed_recovery_count = 3
  config.delayed_recovery_interval = 180  # seconds
end

# Sidekiq::Web.set :session_secret, Rails.application.credentials[:secret_key_base]
# Sidekiq::Web.set :sessions, Rails.application.config.session_options

# Sidekiq::Extensions.enable_delay!
# Sidekiq::Throttled.setup!
SidekiqUniqueJobs.configure do |config|
  config.lock_info = true
  config.lock_ttl = 1.hour.to_i
  config.lock_prefix = "praja_unique_jobs"
end
