class SupportRequestDeduplicationService
  # Default cooldown period in minutes (hardcoded)
  COOLDOWN_PERIOD_MINUTES = 90

  class << self
    # Check if a user can submit a new support request
    # Returns true if user can submit, false if in cooldown period
    def can_user_submit_request?(user_id)
      redis_key = support_request_key(user_id)
      $redis.exists(redis_key) == 0
    end

    # Record a new support request for a user
    # This sets a Redis key that expires in the cooldown period
    # Only sets the key if it doesn't already exist
    def record_request(user_id, reason, source)
      redis_key = support_request_key(user_id)
      
      # Only set the key if it doesn't exist (prevents extending expiry for duplicates)
      unless $redis.exists(redis_key) == 1
        # Set key with expiration in seconds
        expiration_seconds = COOLDOWN_PERIOD_MINUTES * 60
        
        # Store some metadata about the request
        request_data = {
          user_id: user_id,
          reason: reason,
          source: source,
          timestamp: Time.current.iso8601
        }.to_json
        
        $redis.setex(redis_key, expiration_seconds, request_data)
        
        Rails.logger.info("Support request recorded for user #{user_id}: #{reason} from #{source}")
        return true
      end
      
      false
    end

    # Get information about the last support request (if in cooldown)
    def get_request_info(user_id)
      redis_key = support_request_key(user_id)
      
      if $redis.exists(redis_key) == 1
        data = $redis.get(redis_key)
        begin
          JSON.parse(data, symbolize_names: true)
        rescue JSON::ParserError
          nil
        end
      else
        nil
      end
    end

    private

    def support_request_key(user_id)
      "support_request_cooldown:#{user_id}"
    end
  end
end
