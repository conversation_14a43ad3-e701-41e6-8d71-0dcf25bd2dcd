require 'csv'
ActiveAdmin.register UserRole do
  menu :parent => "Roles"
  includes :user, :role, :parent_circle, :purview_circle

  searchable_select_options(name: :user_roles,
                            scope: lambda do |params|
                              user_id = params["user_id"]
                              UserRole.where(user_id: user_id, active: true)
                            end,
                            display_text: ->(record) {
                              "#{record.id}, #{record.role.name}"
                            },
                            filter: lambda do |term, scope|
                              scope.ransack(id_eq: term).result
                            end
  )

  permit_params :user_id, :role_id, :badge_ring, :badge_color, :free_text,
                :grade_level, :primary_role, :active, :marketing_consent, :start_date, :end_date,
                :show_on_about_page, :parent_circle_id, :purview_circle_id, :badge_icon_id, :from_boe_workflow,
                :verification_status, :is_letter_pending, :is_self_claimed, :proof_link, :file, :delete_badge_free_text, display_name_order: []
  actions :all, except: [:destroy]

  # get required role data for user role form
  member_action :role_data_for_user_role_form, method: :get do
    role_id = params[:role_id].to_i

    role = Role.find_by(id: role_id)
    if role.present?
      role_has_parent_circle = role.parent_circle_id.present?
      role_has_purview = role.has_purview
      role_has_badge = role.has_badge
      role_has_free_text = role.has_free_text
    else
      role_has_parent_circle = false
      role_has_purview = false
      role_has_badge = false
      role_has_free_text = false
    end
    render json: { success: true, role_has_purview: role_has_purview, role_has_badge: role_has_badge,
                   role_has_parent_circle: role_has_parent_circle,
                   role_has_free_text: role_has_free_text }, status: :ok
  end

  filter :id, label: "User Role ID"
  filter :user_id, label: "User ID"
  # search role with name or role id
  filter :role_id_eq, label: "Role Search", as: :searchable_select,
         ajax: { resource: Role, collection_name: :roles },
         input_html: { "data-placeholder": "Search by Role ID or Role name.." }
  filter :grade_level, as: :select, collection: Role.grade_levels
  filter :primary_role
  filter :verification_status, as: :select, collection: UserRole.verification_statuses.keys
  filter :is_self_claimed
  filter :is_letter_pending
  filter :active
  filter :role_parent_circle_level_eq, label: "Parent Circle Level", as: :select, collection: Role.parent_circle_levels
  filter :user_marketing_consent_eq, label: "Marketing Consent", as: :select, collection: [true, false]
  filter :created_at
  filter :updated_at

  index do
    selectable_column

    column :id do |user_role|
      link_to(user_role.id, admin_user_role_path(user_role))
    end
    column :user
    column :role
    column :parent_circle do |user_role|
      user_role.parent_circle_id.present? ? user_role.parent_circle : user_role.role.parent_circle
    end
    column :purview_circle
    column :start_date
    column :end_date
    column :grade_level
    column :primary_role
    column :verification_status do |user_role|
      status_tag(user_role.verification_status)
    end
    column :creator do |user_role|
      first_creator_id = user_role.versions.first&.whodunnit
      creator = AdminUser.find_by(id: first_creator_id)
      creator ? link_to(creator.name, admin_admin_user_path(creator)) : '-'
    end
    column :active
    column :created_at
    actions name: "Actions"
  end

  show do |user_role|
    attributes_table do
      row :user
      row :role
      row :parent_circle do |user_role|
        user_role.parent_circle_id.present? ? user_role.parent_circle : user_role.role.parent_circle
      end
      row :purview_circle
      row :start_date
      row :end_date
      row :badge_ring do |user_role|
        !user_role.badge_ring.nil? ? user_role.badge_ring : user_role.role.badge_ring
      end
      row :badge_color do |user_role|
        user_role.badge_color || user_role.role.badge_color
      end
      # display image related to badge_icon_id if present based on getting from badge icon groups with admin medium id
      row :badge_icon do |user_role|
        if user_role.badge_icon_id.present?
          badge_icon = BadgeIcon.find(user_role.badge_icon_id)
          image_tag(badge_icon.admin_medium.url, size: "50x50") if badge_icon.present?
        end
      end
      row :grade_level do |user_role|
        user_role.grade_level || user_role.role.grade_level
      end
      row :display_name_order do |user_role|
        user_role.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) }
      end
      row :free_text
      row :creator do |user_role|
        first_creator = user_role.versions.first
        if first_creator.present?
          creator = AdminUser.find_by_id(first_creator.whodunnit)
          if creator.present?
            link_to(creator.email, admin_admin_user_path(creator))
          else
            '-'
          end
        else
          '-'
        end
      end
      row :primary_role
      row :verification_status do |user_role|
        status_tag(user_role.verification_status)
      end
      row :boe_user do |ur|
        boe_user = ur.user.get_boe_user
        if boe_user.present?
          link_to(boe_user.name, admin_admin_user_path(boe_user))
        else
          '-'
        end
      end
      if user_role.verification_status == 'verified'
        row :file do |ur|
          if ur.file.attached?
            link_to ur.file.filename.to_s, rails_blob_path(ur.file, disposition: "inline"), target: "_blank"
          end
        end
        row :proof_link do |pl|
          if pl.proof_link.present?
            links = pl.proof_link.split(",")
            safe_join(links.map { |link| link_to link.strip, link.strip, target: '_blank', rel: 'noopener' }, "<br>".html_safe)
          end
        end
      end
      row :is_celebrated
      attributes_table_for user_role.user do
        row :marketing_consent
      end
      row :show_on_about_page
      row :active
      row :is_self_claimed
      if user_role.verification_status == 'unverified'
        row :is_letter_pending
      end
      row :created_at
      row :updated_at
    end

    panel 'Edit History' do
      table_for PaperTrail::Version.where(item_type: 'UserRole', item_id: user_role.id).order(id: :desc).limit(10) do
        column('Item') { |v| v.item }
        column('Changes') do |v|
          if v.object_changes
            changes = YAML.safe_load(v.object_changes, permitted_classes: [Date, Time], aliases: true)
            filtered_changes = changes.reject { |field, _| field == "created_at" || field == "updated_at" }
            filtered_changes.map do |field, values|
              old_value = values[0].nil? ? 'nil' : values[0].to_s
              new_value = values[1].nil? ? 'nil' : values[1].to_s
              "#{field}: #{old_value} -> #{new_value}"
            end.join(', ')
          else
            'No changes recorded'
          end
        end
        column('Modified at') { |v| v.created_at }
        column('Admin') do |v|
          if v.whodunnit.nil?
            ''
          elsif v.whodunnit == 'unknown'
            "Unknown"
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end

    active_admin_comments
  end

  form do |f|
    f.semantic_errors
    f.inputs "User Role Details" do
      f.input :user_id, as: :searchable_select, ajax: { resource: User, collection_name: :users }, input_html: { id: 'user-role-user-id-select', disabled: !f.object.new_record?, "data-placeholder": "Search by User ID or User phone.." }
      f.input :role, as: :searchable_select, ajax: { resource: Role, collection_name: :roles }, input_html: { "data-placeholder": "Search by Role ID or Role name.." }
      f.input :parent_circle_id, label: 'Parent Circle ID', as: :searchable_select, ajax: { resource: Circle, collection_name: :user_role_supported_parent_circles }, input_html: { class: 'user_roles_parent_circle_id can_clear_selection', "data-placeholder": "Search by party ID or party name.." }
      f.input :purview_circle_id, label: 'Purview Circle ID', as: :searchable_select, ajax: { resource: Circle, collection_name: :purview_circles }, input_html: { class: 'user_roles_purview_circle_id can_clear_selection', "data-placeholder": "Search by circle ID or circle name.." }
      if f.object.new_record?
        f.input :display_name_order,
                label: 'Display Name Order (override only)',
                as: :searchable_select,
                multiple: true,
                collection: Role::DISPLAY_NAME.keys - Role::DISPLAY_NAME.keys.last(3),
                input_html: { class: 'user-role-display-name-select', "data-placeholder": "select them in order of how you want display name" },
                hint: "select display name keys in order of how you want display name"
      else
        # collection should be enum values of selected values of display_name_order followed by not selected values of display_name_order
        f.input :display_name_order,
                label: 'Display Name Order (override only)',
                as: :searchable_select,
                multiple: true,
                collection: f.object.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) } +
                  (Role::DISPLAY_NAME.keys - f.object.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) }),
                selected: f.object.display_name_order.split(',').map { |display_name| Role::DISPLAY_NAME.key(display_name) },
                input_html: { class: 'user-role-display-name-select', "data-placeholder": "select them in order of how you want display name" },
                hint: "select display name keys in order of how you want display name"
      end
      f.input :free_text, as: :text, input_html: { rows: 1 }, hint: "Free Text will be enabled only if the role has free text."
      if f.object.new_record?
        f.input :start_date, as: :date_picker, :input_html => { :value => Date.current }
        f.input :end_date, as: :date_picker, :input_html => { :value => nil }
      else
        f.input :start_date, as: :date_picker
        f.input :end_date, as: :date_picker
      end
      f.input :badge_icon_id, as: :searchable_select, ajax: { resource: BadgeIconGroup, collection_name: :badge_icons },
              input_html: { "data-placeholder": "Search by circle name.." }
      f.input :badge_color, as: :select, collection: UserRole.badge_colors.keys, label: 'Badge Color (override only)'
      f.input :badge_ring, as: :select, collection: [true, false], label: 'Badge ring (override only)'
      f.input :grade_level, as: :select, collection: UserRole.grade_levels.keys, label: 'Grade level (override only)'
      f.input :primary_role, as: :select, collection: [true, false], default: true, include_blank: false
      f.input :verification_status,
              as: :select,
              collection: UserRole.verification_statuses.keys,
              include_blank: false,
              required: true,
              selected: f.object.new_record? ? UserRole.verification_statuses[:unverified] : f.object.verification_status,
              label: 'Proofs Verification Status', input_html: { id: 'verification_status' }
      f.input :file, as: :file, label: 'Verification File', wrapper_html: { id: 'file', style: 'display:none;' }
      f.input :proof_link, wrapper_html: { id: 'proof_link', style: 'display:none;' }
      f.input :marketing_consent, as: :boolean, input_html: { checked: !f.object.new_record? && f.object.user.marketing_consent? }
      # Badge Free Text Deletion Checkbox - show when user has badge free text
      f.input :delete_badge_free_text, as: :boolean,
              label: "Delete Badge Text from RM",
              hint: "Check this box to delete the current badge text from RM when #{f.object.new_record? ? 'creating' : 'updating'} this user role. \"<span id='badge-free-text-label' style='font-weight:bold;'></span>\"".html_safe,
              input_html: { id: 'delete-badge-free-text-checkbox' },
              wrapper_html: { id: 'badge_free_text_deletion_wrapper', style: 'display:none;' }
      f.input :active
      f.input :is_self_claimed
      f.input :is_letter_pending, wrapper_html: { id: 'is_letter_pending', style: 'display:none;' }
      f.input :show_on_about_page
    end
    f.actions
  end

  controller do

    before_action :check_admin_user, only: [:new, :edit, :destroy]

    def current_user
      current_admin_user
    end

    def check_admin_user
      begin
        authorize current_admin_user, :can_manage_user_roles?
      rescue Pundit::NotAuthorizedError
        redirect_to admin_user_roles_path, alert: 'You are not authorized to perform this action.'
      end
    end

    def new
      # Store from_boe_workflow parameter in session
      # Normalize the value to a boolean
      if params[:from_boe_workflow].present?
        session[:from_boe_workflow] = (params[:from_boe_workflow].to_s.downcase == 'true')
      end
      super
    end

    def update
      attrs = permitted_params[:user_role]
      @user_role = UserRole.find(params[:id])

      @user_role.role_id = attrs[:role_id]
      @user_role.parent_circle_id = attrs[:parent_circle_id]
      @user_role.purview_circle_id = attrs[:purview_circle_id]
      @user_role.start_date = attrs[:start_date]
      @user_role.end_date = attrs[:end_date]
      @user_role.badge_icon_id = attrs[:badge_icon_id]
      @user_role.badge_color = attrs[:badge_color]
      @user_role.badge_ring = attrs[:badge_ring]
      @user_role.free_text = attrs[:free_text]
      @user_role.grade_level = attrs[:grade_level]
      @user_role.primary_role = attrs[:primary_role]
      @user_role.marketing_consent = attrs[:marketing_consent]
      @user_role.active = attrs[:active]
      @user_role.show_on_about_page = attrs[:show_on_about_page]
      @user_role.verification_status = attrs[:verification_status]
      @user_role.proof_link = attrs[:proof_link]
      @user_role.is_self_claimed = attrs[:is_self_claimed]
      @user_role.is_letter_pending = attrs[:is_letter_pending]
      @user_role.file = attrs[:file] if attrs[:file].present?

      display_name_order = attrs[:display_name_order].compact_blank

      if attrs[:file].present?
        key = "#{Rails.env}/#{@user_role.user_id}/proofs/#{attrs[:file].original_filename}"
        @user_role.file.attach(io: attrs[:file], filename: attrs[:file].original_filename, key: key)
      end

      display_name_order = display_name_order.present? ? display_name_order.map { |display_name| Role::DISPLAY_NAME[display_name.to_sym] }.join(",") : ''
      @user_role.display_name_order = display_name_order
      if @user_role.save
        # Handle badge free text deletion if checkbox was checked
        if attrs[:delete_badge_free_text] == '1' && @user_role.user.present?
          # @user_role.user.premium_pitch&.saved_badge
          @user_role.user.delete_badge_free_text_from_rm_layout_creation
        end
        redirect_to admin_user_role_path(@user_role), notice: "User role updated successfully."
      else
        flash[:errors] = @user_role.errors.full_messages.first
        render :edit
      end
    end

    def create
      attrs = permitted_params[:user_role]
      @user_role = UserRole.new(attrs)
      display_name_order = attrs[:display_name_order].compact_blank

      if attrs[:file].present?
        key = "#{Rails.env}/#{@user_role.user_id}/proofs/#{attrs[:file].original_filename}"
        @user_role.file.attach(io: attrs[:file], filename: attrs[:file].original_filename, key: key)
      end

      display_name_order = display_name_order.present? ? display_name_order.map { |display_name| Role::DISPLAY_NAME[display_name.to_sym] }.join(",") : ''
      attrs[:display_name_order] = display_name_order
      @user_role = UserRole.new(attrs)
      if @user_role.save
        # Handle badge free text deletion if checkbox was checked
        if @user_role.user.present? && ( attrs[:delete_badge_free_text] == '1' || session[:from_boe_workflow] )
          # @user_role.user.premium_pitch&.saved_badge
          @user_role.user.delete_badge_free_text_from_rm_layout_creation

        end

        Rails.logger.warn("[UserRole::Create] Creating UserRole for user_id=#{@user_role.user_id} | Source: #{session[:from_boe_workflow] ? 'BOE Workflow' : 'AdminDashboard'}")

        # Only add localStorage script if coming from BOE Work Flow
        if session[:from_boe_workflow]
          # Clear the session variable after using it
          session.delete(:from_boe_workflow)

          # Render a custom page that sets localStorage with current timestamp and then redirects
          render html: <<-HTML.html_safe
            <!DOCTYPE html>
            <html>
            <head>
              <title>Creating User Role</title>
              <script>
                localStorage.setItem('#{@user_role.user_id}_badge_created', 'true');
                window.location.href = '#{admin_user_role_path(@user_role)}';
              </script>
            </head>
            <body>
              <p>User role created successfully. Redirecting...</p>
            </body>
            </html>
          HTML
        else
          flash[:notice] = "User role created successfully."
          redirect_to admin_user_role_path(@user_role)
        end
      else
        flash[:errors] = @user_role.errors.full_messages.first
        render :new
      end
    end
  end
end
