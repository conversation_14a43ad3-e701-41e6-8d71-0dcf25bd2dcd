ActiveAdmin.register Poster do
  menu false
  permit_params :name, :start_time, :end_time, :active, :admin_user_id, circle_ids: [],
                poster_photos_attributes: [:photo, :leader_photo_ring_color]

  actions :all, except: [:destroy]

  batch_action :active do |ids|
    batch_action_collection.find(ids).each do |poster|
      poster.update(active: true)
    end
    redirect_to collection_path, notice: "Posters have been made active!"
  end

  batch_action :in_active do |ids|
    batch_action_collection.find(ids).each do |poster|
      poster.update(active: false)
    end
    redirect_to collection_path, notice: "Posters have been made inactive!"
  end

  show do
    attributes_table do
      row :id
      row :name
      row :start_time
      row :end_time
      row :poster_type
      row :photos do |p|
        p.poster_photos.map do |poster_photo|
          image_tag poster_photo.photo.url, class: 'thumb_size', style: "margin-right: 5px;" if poster_photo.present?
        end
      end
      row :active
      row :circle
      row :admin_user
      row :created_at
      row :updated_at
    end
  end

  form :html => { :multipart => true } do |f|
    f.semantic_errors
    f.inputs "Poster Details" do
      if f.object.new_record?
        f.input :name
        f.input :start_time, as: :datetime_picker
        f.input :end_time, as: :datetime_picker
        f.has_many :poster_photos do |cf|
          cf.input :photo, label: 'Photos (new versions only)', as: :file, hint: "aspect ratio - 680x912"
          cf.input :leader_photo_ring_color, as: :select, collection: PosterPhoto.leader_photo_ring_colors.keys
        end
        f.input :circle_ids, label: 'Circle/ Circles', as: :searchable_select, multiple: true, ajax: { resource: Circle, collection_name: :poster_circles },  input_html: { "data-placeholder": "Search by Circle Id or Name or Short name" }
        f.input :active
      else
        f.input :active
      end
    end
    f.actions name: "Actions"
  end

  controller do
    def update
      attrs = permitted_params[:poster]

      @poster = Poster.find(params[:id])
      @poster.active = attrs[:active]

      if @poster.save
        redirect_to admin_poster_path(@poster)
      else
        render :edit
      end
    end

    def create

      # to achieve multiple circles posters creation
      # we do create one poster for one circle with all validations and callbacks
      # use the created posters data and create the posters for remaining circles
      attrs = permitted_params[:poster]
      circle_ids = attrs[:circle_ids].compact_blank

      @poster = Poster.new(
        name: attrs[:name],
        start_time: attrs[:start_time],
        end_time: attrs[:end_time],
        poster_type: :normal,
        circle_id: circle_ids[0],
        admin_user_id: current_admin_user.id
      )

      poster_photos = []
      poster_photos_data = []
      if attrs[:poster_photos_attributes].present?
        attrs[:poster_photos_attributes].each do |x, y|
          poster_photos << [y[:photo], y[:leader_photo_ring_color]]
          poster_photos_data << y[:photo]
        end
      end

      poster_photos.each do |photo_data|
        @poster.poster_photos.build(blob_data: photo_data[0],
                                    photo: AdminMedium.new(blob_data: photo_data[0], admin_user_id: current_admin_user.id),
                                    leader_photo_ring_color: photo_data[1])
      end

      if @poster.save
        @all_posters = [@poster]
        #create remaining circles posters
        circle_ids.delete_at(0)

        circle_ids.each do |circle_id|
          other_poster = Poster.new(
            name: attrs[:name],
            start_time: attrs[:start_time],
            end_time: attrs[:end_time],
            poster_type: :normal,
            circle_id: circle_id,
            admin_user_id: current_admin_user.id
          )

          count = 0
          @poster.poster_photos.each do |poster_photo|
            other_poster.poster_photos.build(blob_data: poster_photos_data[count],
                                             photo_id: poster_photo.photo_id,
                                             photo_type: poster_photo.photo_type,
                                             leader_photo_ring_color: poster_photo.leader_photo_ring_color)
            count = count + 1
          end

          other_poster.save

          @all_posters << other_poster
        end

        check_for_slack_ping
        redirect_to admin_posters_path
      else
        flash[:error] = @poster.errors.full_messages
        render :new
      end
    end

    def check_for_slack_ping
      return unless Rails.env.production?

      if (Time.zone.now.hour >= 22 && @poster.start_time >= Time.zone.now && @poster.start_time <= Time.zone.tomorrow.end_of_day) ||
        (Time.zone.now.hour < 22 && @poster.start_time > Time.zone.today.beginning_of_day && @poster.start_time <= Time.zone.today.end_of_day)
        data = @all_posters.map do |res|
          "<a href='https://www.thecircleapp.in/admin/posters/#{res.id}'>#{res.name}</a> on <a href='https://www.thecircleapp.in/admin/circles/#{res.circle_id}'>#{res.circle.name_en}</a> (#{res.circle.level})"
        end

        return if data.nil? || data.empty?

        Poster.ping_to_slack("Posters enabled - #{data.join(', ')}")
      end
    end
  end

  filter :id
  filter :start_time
  filter :end_time
  filter :active
  filter :circle_id
  filter :admin_user_id
end
