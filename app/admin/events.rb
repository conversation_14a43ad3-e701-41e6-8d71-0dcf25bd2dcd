ActiveAdmin.register Event do
  menu priority: 2, :parent => "Posters V2"
  permit_params :name, :start_time, :end_time, :priority, :active, circle_ids: []

  actions :all, except: [:destroy]

  searchable_select_options(name: :events,
                            scope: -> { Event.order(id: :desc) },
                            display_text: ->(record) {
                              "#{record.id},#{record.name},"
                            },
                            filter: lambda do |term, scope|
                              scope.ransack(id_eq: term, name_cont: term, m: 'or').result
                            end
  )

  index do
    selectable_column
    id_column
    column :name do |e|
      link_to(e.name, admin_event_path(e))
    end
    column :circles do |e|
      e.event_circles.map do |event_circle|
        link_to(event_circle.circle.name, admin_circle_path(event_circle.circle)) if event_circle.present?
      end
    end
    column :start_time
    column :end_time
    column :creator do |e|
      link_to(e.creator.email, admin_admin_user_path(e.creator)) if e.creator.present?
    end
    column :active
    column :created_at
    column :updated_at
    actions name: "Actions"
  end

  show do
    attributes_table do
      row :id
      row :name
      row :singular_link do |e|
        "https://prajaapp.sng.link/A3x5b/3ir9?_dl=praja%3A%2F%2Fbuzz.praja.app/posters/layout%3Fid%3D#{e.id}&_ddl=praja%3A%2F%2Fbuzz.praja.app/posters/layout%3Fid%3D#{e.id}"
      end
      row :notification_deep_link do |e|
        "praja-app://buzz.praja.app/posters/layout?id=#{e.id}"
      end
      row :circles do |e|
        e.event_circles.map do |event_circle|
          link_to(event_circle.circle.name, admin_circle_path(event_circle.circle)) if event_circle.present?
        end
      end
      row :priority
      row :start_time
      row :end_time
      row :active
      row :designer do |e|
        if e.designers.present?
          e.designers.map { |d| link_to(d.name, admin_admin_user_path(d)) }.join(", ").html_safe
        end
      end
      row :creator do |e|
        link_to(e.creator.email, admin_admin_user_path(e.creator)) if e.creator.present?
      end
      row :created_at
      row :updated_at
    end

    panel "Poster Creatives" do
      table_for event.poster_creatives.order("poster_creatives.active DESC, id DESC") do
        column :photo_v2 do |pc|
          if pc.photo_v2.present?
            link_to((image_tag pc.photo_v2.url, width: '75px', class: 'thumb_size', style: "margin-right: 5px;"), admin_poster_creative_path(pc))
          end
        end
        column :photo_v3 do |pc|
          if pc.photo_v3.present?
            link_to((image_tag pc.photo_v3.url, width: '75px', class: 'thumb_size', style: "margin-right: 5px;"), admin_poster_creative_path(pc))
          end
        end
        column :creative_kind
        column :paid
        column :primary
        column :active
        column :duration do |pc|
          "#{pc.start_time.strftime("%d-%m-%Y %H:%M")} to #{pc.end_time.strftime("%d-%m-%Y %H:%M")}"
        end
        column :circles do |pc|
          pc.poster_creative_circles.map do |poster_creative_circle|
            link_to(poster_creative_circle.circle.name, admin_circle_path(poster_creative_circle.circle)) if poster_creative_circle.present?
          end
        end
        column :actions do |pc|
          links = []
          links << link_to("View", admin_poster_creative_path(pc))
          links << link_to("Edit", edit_admin_poster_creative_path(pc))
          links.join("&nbsp;&nbsp;&nbsp;").html_safe
        end
      end
    end

    panel "Video Creatives" do
      table_for event.video_creatives.order("video_creatives.active DESC, id DESC") do
        column :video do |vc|
          if vc.video.present? && vc.video.source_url.present?
            link_to((video_tag vc.video.source_url,
                      controls: true,
                      width: 200,
                      height: 130), admin_video_creative_path(vc))
          end
        end
        column :kind
        column :active
        column :duration do |vc|
          "#{vc.start_time.strftime("%d-%m-%Y %H:%M")} to #{vc.end_time.strftime("%d-%m-%Y %H:%M")}"
        end
        column :circles do |vc|
          vc.video_creative_circles.map do |video_creative_circle|
            link_to(video_creative_circle.circle.name, admin_circle_path(video_creative_circle.circle)) if video_creative_circle.present?
          end
        end
        column :actions do |vc|
          links = []
          links << link_to("View", admin_video_creative_path(vc))
          links << link_to("Edit", edit_admin_video_creative_path(vc))
          links.join("&nbsp;&nbsp;&nbsp;").html_safe
        end
      end
    end
  end

  form :html => { :multipart => true } do |f|
    f.semantic_errors
    f.inputs "Event Details" do
      f.input :name
      f.input :active
      f.input :start_time, as: :date_picker, input_html: { style: "width: 300px;" }
      f.input :end_time, as: :date_picker, input_html: { style: "width: 300px;" }
      f.input :priority, as: :select, collection: Event.priorities.keys
      f.input :circle_ids,
              label: 'Circle(s)' + (f.object.new_record? ? '' : ' (will override existing circles)'),
              as: :searchable_select,
              multiple: true,
              ajax: { resource: Circle, collection_name: :poster_circles },
              input_html: { "data-placeholder": "Search by Circle Id or Name or Short name" },
              hint: f.object.new_record? ? nil : "Existing circles - #{f.object.event_circles.map { |c| c.circle.name }.join(', ')}"
    end
    f.actions
  end

  controller do
    def scoped_collection
      Event.includes(:creator, event_circles: :circle, poster_creatives: { poster_creative_circles: :circle })
    end

    def update
      attrs = permitted_params[:event]

      @event = Event.find(params[:id])
      @event.name = attrs[:name]
      @event.active = attrs[:active]
      @event.priority = attrs[:priority]
      @event.start_time = Time.zone.parse(attrs[:start_time].to_s + " 00:00:00")
      @event.end_time = Time.zone.parse(attrs[:end_time].to_s + " 23:59:59")

      circle_ids = attrs[:circle_ids].compact_blank.map(&:to_i)

      deleting_circle_ids = []
      new_circle_ids = []
      if circle_ids.present?
        existing_circle_ids = @event.event_circles.map(&:circle_id)

        new_circle_ids = circle_ids - existing_circle_ids
        deleting_circle_ids = existing_circle_ids - circle_ids

        if new_circle_ids.present?
          new_circle_ids.each do |new_circle_id|
            @event.event_circles.build(circle_id: new_circle_id)
          end
        end
      end

      if @event.save
        if new_circle_ids.present?
          @event.poster_creatives.each do |pc|
            new_circle_ids.each do |new_circle_id|
              pc.poster_creative_circles.create(circle_id: new_circle_id)
            end
          end
        end
        if deleting_circle_ids.present?
          @event.event_circles.where(circle_id: deleting_circle_ids).destroy_all
          @event.poster_creatives.each do |pc|
            pc.poster_creative_circles.where(circle_id: deleting_circle_ids).destroy_all
          end
        end
        if new_circle_ids.present? || deleting_circle_ids.present?
          IndexCreativesForPostersFeed.perform_async("event_#{params[:id]}")
        end
        redirect_to admin_event_path(@event)
      else
        flash[:error] = @event.errors.full_messages.first
        render :edit
      end
    end

    def create
      attrs = permitted_params[:event]
      circle_ids = attrs[:circle_ids].compact_blank

      if circle_ids.present?
        @event = Event.new(name: attrs[:name],
                           start_time: Time.zone.parse(attrs[:start_time].to_s + " 00:00:00"),
                           end_time: Time.zone.parse(attrs[:end_time].to_s + " 23:59:59"),
                           priority: attrs[:priority],
                           active: attrs[:active],
                           creator: current_admin_user)

        circle_ids.each do |circle_id|
          circle = Circle.find_by(id: circle_id)
          if circle.present?
            @event.event_circles.build(circle_id: circle_id)
          end
        end

        if @event.save
          redirect_to admin_event_path(@event)
        else
          flash[:error] = @event.errors.full_messages.first
          render :new
        end
      else
        redirect_to new_admin_event_path, alert: "Please select atleast one circle"
      end
    end
  end

  filter :id, label: "Event ID"
  filter :name
  filter :priority, as: :select, collection: Event.priorities
  filter :active
  filter :event_circle, label: 'Circle ID', as: :numeric, filters: [:eq]
  filter :start_time
  filter :end_time
  filter :created_at
  filter :updated_at
end
