ActiveAdmin.register_page "OE Work Flow" do
  menu false

  # Helper methods for the OE workflow
  controller.helper_method :get_user_for_oe_workflow, :check_aspect_ratio

  controller.helper do
    def get_user_for_oe_workflow
      # Access the @user instance variable from the controller
      user = controller.instance_variable_get(:@user)

      # If user is nil, redirect to dashboard
      if user.nil?
        redirect_to admin_dashboard_path, alert: "User not found" and return nil
      end

      user
    end

    # Helper method to check if a photo has the correct dimensions (250px width and 280px height)
    # or the correct aspect ratio (25:28)
    def check_aspect_ratio(photo)
      return false if photo.nil? || !photo.respond_to?(:width) || !photo.respond_to?(:height)
      return false if photo.width.nil? || photo.height.nil? || photo.width == 0
      # Calculate the aspect ratio (width:height)
      ratio = (photo.width.to_f / photo.height.to_f).round(2)
      expected_ratio = (25.0 / 28.0).round(2)

      # Allow a small margin of error (0.01)
      (ratio - expected_ratio).abs < 0.01
    end

    def dummy_cutout_url
      AdminMedium.find_by(id: Constants.get_leader_dummy_cutout_id)&.url
    end

    # Helper method to check if we should show dummy photo info
    def show_dummy_photo_info?(user_photo, bg_removed_url)
      image_url = if user_photo.present? && user_photo.url.present?
                    user_photo.url
                  elsif bg_removed_url.present?
                    bg_removed_url
                  else
                    dummy_cutout_url
                  end

      image_url == dummy_cutout_url
    end
  end
  page_action :submit_form, method: :post do
    attrs = params[:oe_work_flow]

    user = User.active.find_by_hashid(params[:user_id])
    if user.blank?
      flash[:error] = "User not found"
      return redirect_to admin_dashboard_path
    end

    user_poster_layout = user.get_user_poster_layout_including_inactive
    if user_poster_layout.blank?
      flash[:error] = "User poster layout not found"
      return redirect_to admin_user_path(user)
    end

    unless user.oe_work_enabled?
      flash[:error] = "OE work flow is only enabled when premium pitch status is 'layout_setup'. Current status: #{user.premium_pitch&.status || 'not set'}."
      return redirect_to admin_users_oe_work_flow_path(user_id: user.hashid)
    end

    # Get the button action from the commit parameter
    button_action = params[:commit]
    # Map the button label to the action
    button_action = (button_action == "Incomplete") ? "Incomplete" : "Submit"
    # Only process photo uploads and circle selections if the Submit button was clicked
    # If Incomplete button was clicked, we'll only save remarks and update status
    if button_action == "Submit"
      # if user poster photo with background changed then update it
      if attrs[:user_poster_photo_with_background].present?
        user_poster_layout.entity.poster_photo_with_background = Photo.new(blob_data: attrs[:user_poster_photo_with_background],
                                                                           user: user,
                                                                           service: :aws)
      elsif attrs[:user_poster_photo_with_background_original].present?
        # if user poster photo with background is not changed then set the original photo
        user_poster_layout.entity.poster_photo_with_background = Photo.find_by(id: attrs[:user_poster_photo_with_background_original])
      end

      if attrs[:user_poster_photo].present?
        user_poster_layout.entity.poster_photo = Photo.new(blob_data: attrs[:user_poster_photo],
                                                           user: user,
                                                           service: :aws)
      elsif attrs[:bg_removed_user_poster_photo].present?
        # if user poster photo is not changed then set the bg removed poster photo
        user_poster_layout.entity.poster_photo = Photo.find_by(id: attrs[:bg_removed_user_poster_photo])
      end

      # if family frame photo changed then update it
      if attrs[:family_frame_photo].present?
        user_poster_layout.entity.family_frame_photo = Photo.new(blob_data: attrs[:family_frame_photo],
                                                                 user: user,
                                                                 service: :aws)
      elsif attrs[:bg_removed_family_frame_photo].present?
        # if family frame photo is not changed then set the bg removed family frame photo
        user_poster_layout.entity.family_frame_photo = Photo.find_by(id: attrs[:bg_removed_family_frame_photo])
      end

      # if hero frame photo changed then update it
      if attrs[:hero_frame_photo].present?
        user_poster_layout.entity.hero_frame_photo = Photo.new(blob_data: attrs[:hero_frame_photo],
                                                               user: user,
                                                               service: :aws)
      elsif attrs[:bg_removed_hero_frame_photo].present?
        # if hero frame photo is not changed then set the bg removed hero frame photo
        user_poster_layout.entity.hero_frame_photo = Photo.find_by(id: attrs[:bg_removed_hero_frame_photo])
      end

      # Process selected circles
      selected_circles = params[:selected_circles] || {}
      selected_circles.each do |req_leader_photo_id, json_value|
        data = JSON.parse(json_value)
        circle_id = data["circle_id"]
        photo_id = data["photo_id"]

        # Find the corresponding UserLeaderPhoto record using the req_leader_photo_id
        leader_photo = UserLeaderPhoto.find_by(id: req_leader_photo_id)

        if leader_photo
          # Update the record with the selected circle and photo IDs.
          leader_photo.update(circle_id: circle_id, photo_id: photo_id)
        end
      end
    end

    # save the remarks
    if attrs[:remarks].present?
      PosterLayoutRemark.create(user_poster_layout: user_poster_layout,
                                remarks: attrs[:remarks],
                                admin_user: current_admin_user)
    end

    # Handle status transitions based on the button action
    if button_action == "Submit"
      # if complete button was clicked, activate the layout
      user_poster_layout.update(active: true)
    else
      user.premium_pitch.layout_incomplete! if user.premium_pitch&.may_layout_incomplete?
    end

    if user_poster_layout.entity.save && user_poster_layout.save
      if user_poster_layout.active
        # call ProtocolJoinOfUserInCircles by fetching circle ids from user leader photos
        circle_ids = user_poster_layout.user_leader_photos.pluck(:circle_id).compact.uniq
        circle_ids << user.get_poster_affiliated_party_id if user.get_poster_affiliated_party_id.present?
        ProtocolJoinOfUserInCircles.perform_async(user.id, circle_ids) if circle_ids.present?
        return redirect_to "#{Constants.posters_preview_dashboard_base_url}/#/edit_layouts?user_poster_layout_id=#{user_poster_layout.id}",
                           notice: "Layout data set successfully", allow_other_host: true
      else
        return redirect_to admin_users_oe_work_flow_path(user_id: user.hashid), notice: "Saved remarks and marked layout as incomplete"
      end
    else
      flash[:error] = user_poster_layout.errors.full_messages.first if user_poster_layout.errors.present?
      flash[:error] = user_poster_layout.entity.errors.full_messages.first if user_poster_layout.entity.errors.present?
      return redirect_to admin_users_oe_work_flow_path(user_id: user.hashid), alert: "Failed to set layout data"
    end
  end

  controller do
    before_action :set_user, only: [:index]

    def index
      user_poster_layout = @user.get_user_poster_layout_including_inactive
      if user_poster_layout.blank?
        # redirect to user path
        redirect_to admin_user_path(@user), alert: "User poster layout not found" and return
      end

      unless @user.oe_work_enabled?
        redirect_to admin_user_path(@user), alert: "OE work is only enabled when premium pitch status is 'layout_setup'. Current status: #{@user.premium_pitch&.status || 'not set'}." and return
      end

      # Render the index template directly instead of calling super
      render 'active_admin/page/index'
    end

    private

    def set_user
      if params[:user_id].present?
        @user = User.active.find_by_hashid(params[:user_id])
        if @user.blank?
          redirect_to admin_dashboard_path, alert: "User not found" and return
        end
      else
        # If no user_id is provided, redirect to dashboard
        redirect_to admin_dashboard_path, alert: "User ID is required" and return
      end
    end
  end

  content title: 'OE Work Flow' do

    # We need to fetch the user again in the content block
    # Use a helper method to get the user to avoid accessing params directly
    user = helpers.get_user_for_oe_workflow
    user_poster_layout = user.get_user_poster_layout_including_inactive

    # Check if OE work is enabled for this user
    oe_work_enabled = user.oe_work_enabled?
    unless oe_work_enabled
      div class: "flash flash_alert" do
        text_node "OE work is only enabled when premium pitch status is 'layout_setup'. Current status: #{user.premium_pitch&.status || 'not set'}."
      end
    end

    # Only proceed with the rest of the content if OE work is enabled
    if oe_work_enabled
      original_poster_photos_json = user.get_layout_original_identity_photos
      # fetch photos from the json
      original_poster_photos = Photo.where(id: original_poster_photos_json.map { |photo| photo[:photo][:id] })
      bg_removed_poster_photos_json = user.get_layout_bg_removed_identity_photos
      bg_removed_poster_photos = Photo.where(id: bg_removed_poster_photos_json.map { |photo| photo[:photo][:id] })
      active_admin_form_for :oe_work_flow,
                            url: admin_oe_work_flow_submit_form_path(user_id: user.hashid),
                            method: :post,
                            html: { multipart: true } do |f|
        f.inputs do
          panel "Party Information" do
            columns do
              column do
                attributes_table_for user do
                  row :affiliated_party do |u|
                    affiliated_circle = Circle.find_by(id: u.get_poster_affiliated_party_id)
                    affiliated_circle ? link_to(affiliated_circle.name, admin_circle_path(affiliated_circle), target: "_blank") : '-'
                  end
                end
              end
            end
          end

          panel 'Set Layout Data' do

            f.input :user_id, as: :hidden, input_html: { value: user.id }
            div class: 'poster-photos-div' do
              div class: 'poster-photo-section' do

                # Left Column
                div class: 'poster-photo-label poster-photo-with-bg-label' do
                  label 'Poster Photo With Background'
                end

                div class: 'photos-section' do
                  # Center Column (read-only image & its download button)
                  div class: 'image-preview-parent' do
                    # Show the original or placeholder image
                    div class: 'image-preview' do
                      image_tag(
                        original_poster_photos_json
                          .select { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }
                          .map { |photo| photo[:photo][:url] }
                          .first
                      )
                    end
                    # Download button for the center column
                    div class: 'download-btn-parent' do
                      photo_id = original_poster_photos_json.select { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }.map { |photo| photo[:photo][:id] }.first
                      link_to 'Download',
                              original_poster_photos.find { |photo| photo.id == photo_id }.photo_download_url,
                              class: 'download-btn'
                    end
                  end

                  div class: 'arrow-symbol' do
                    span '➡️'
                  end

                  # Right Column (editable upload + second image + second download)
                  div class: 'image-preview-parent' do
                    # Image preview
                    div class: 'image-preview' do
                      if user.poster_photo_with_background.present? && user.poster_photo_with_background.url.present?
                        image_tag(user.poster_photo_with_background.url)
                      elsif original_poster_photos_json.find { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }&.dig(:photo, :url).present?
                        image_tag(original_poster_photos_json.find { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }&.dig(:photo, :url))
                      else
                        image_tag(dummy_cutout_url)
                      end
                    end
                    # Download button for the right column
                    div class: 'download-btn-parent' do
                      if user.poster_photo_with_background.present?
                        link_to 'Download', user.poster_photo_with_background.photo_download_url, class: 'download-btn'
                      else
                        photo_id = original_poster_photos_json.select { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }.map { |photo| photo[:photo][:id] }.first
                        photo_obj = original_poster_photos.find { |photo| photo.id == photo_id }
                        if photo_obj && photo_obj.respond_to?(:photo_download_url)
                          link_to 'Download', photo_obj.photo_download_url, class: 'download-btn'
                        else
                          content_tag(:span, 'Download', class: 'download-btn disabled', style: 'pointer-events: none; opacity: 0.5;')
                        end
                      end
                    end

                    div class: 'photo-file' do
                      # File input
                      f.input :user_poster_photo_with_background, as: :file, required: false, label: false, input_html: { class: 'upload-btn' }
                      # hidden input to store the accepted value for the first time if user don't have user_poster_photo_with_background
                      if user.poster_photo_with_background.blank?
                        f.hidden_field :user_poster_photo_with_background_original,
                                       value: original_poster_photos_json
                                                .select { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }
                                                .map { |photo| photo[:photo][:id] }
                                                .first

                      end
                    end
                  end
                end
              end

              poster_photo_bg_removed_url = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.poster_photo_without_background_key }&.dig(:photo, :url)
              div class: 'poster-photo-section' do
                # Left Column
                div class: 'poster-photo-label poster-photo-without-bg-label' do
                  label 'Poster Photo Without Background'
                end
                # Check aspect ratio and show warning if needed
                bg_removed_photo_id = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.poster_photo_without_background_key }&.dig(:photo, :id)
                if bg_removed_photo_id.present? && user.poster_photo.blank?
                  bg_removed_photo = bg_removed_poster_photos.find { |photo| photo.id == bg_removed_photo_id }
                  unless helpers.check_aspect_ratio(bg_removed_photo)
                    div class: 'aspect-ratio-info' do
                      span "Info: The image doesn't have the correct dimensions of 250px width and 280px height. Please resize the image to match these dimensions."
                    end
                  end
                end

                if helpers.show_dummy_photo_info?(user.poster_photo, poster_photo_bg_removed_url)
                  div class: 'dummy-photo-info' do
                    span 'Background removal for this image failed. Please upload a new image with the background already removed.'
                  end
                end
                div class: 'photos-section' do
                  # Center Column (read-only image & its download button)
                  div class: 'image-preview-parent' do
                    # Show the original or placeholder image
                    div class: 'image-preview' do
                      image_tag(
                        original_poster_photos_json
                          .select { |photo| photo[:type] == Constants.poster_photo_without_background_original_key }
                          .map { |photo| photo[:photo][:url] }
                          .first
                      )
                    end
                    # Download button for the center column
                    div class: 'download-btn-parent' do
                      photo_id = original_poster_photos_json
                                   .select { |photo| photo[:type] == Constants.
                                     poster_photo_without_background_original_key }.map { |photo| photo[:photo][:id] }
                                   .first
                      link_to 'Download',
                              original_poster_photos.find { |photo| photo.id == photo_id }.photo_download_url,
                              class: 'download-btn', download: true
                    end
                  end

                  div class: 'arrow-symbol' do
                    span '➡️'
                  end

                  # Right Column (editable upload + second image + second download)
                  div class: 'image-preview-parent' do
                    # Image preview
                    div class: 'image-preview', id: 'bg_removed_poster_photo' do
                      image_url = if user.poster_photo.present? && user.poster_photo.url.present?
                                    user.poster_photo.url
                                  elsif poster_photo_bg_removed_url.present?
                                    poster_photo_bg_removed_url
                                  else
                                    dummy_cutout_url
                                  end
                      image_tag(image_url)
                    end
                    # Download button for the right column
                    div class: 'download-btn-parent' do
                      if user.poster_photo.present?
                        link_to 'Download', user.poster_photo.photo_download_url, class: 'download-btn'
                      else
                        photo_id = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.poster_photo_without_background_key }&.dig(:photo, :id)
                        photo_obj = bg_removed_poster_photos.find { |photo| photo.id == photo_id }
                        if photo_obj && photo_obj.respond_to?(:photo_download_url)
                          link_to 'Download', photo_obj.photo_download_url, class: 'download-btn', download: true
                        else
                          content_tag(:span, 'Download', class: 'download-btn disabled', style: 'pointer-events: none; opacity: 0.5;')
                        end
                      end
                    end

                    div class: 'photo-file' do
                      # File input
                      f.input :user_poster_photo, as: :file, required: false, label: false, input_html: { class: 'upload-btn' }
                      # hidden input to store the accepted value for the first time if user don't have user_poster_photo
                      if user.poster_photo.blank?
                        bg_removed_photo_id = bg_removed_poster_photos_json.
                          find { |photo| photo[:type] == Constants.poster_photo_without_background_key }&.
                          dig(:photo, :id)
                        f.hidden_field :bg_removed_user_poster_photo, value: bg_removed_photo_id
                      end
                    end
                  end
                end
              end
              # ---------------------------
              # Section 3: Family Frame Original (if available)
              # ---------------------------
              if (family_photo_url = original_poster_photos_json.find { |photo| photo[:type] == Constants.family_frame_photo_original_key }&.dig(:photo, :url)).present?
                family_photo_id = original_poster_photos_json.find { |photo| photo[:type] == Constants.family_frame_photo_original_key }&.dig(:photo, :id)
                family_photo_bg_removed_url = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.family_frame_photo_key }&.dig(:photo, :url)
                bg_removed_family_photo_id = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.family_frame_photo_key }&.dig(:photo, :id)

                div class: 'poster-photo-section' do
                  # Left Column
                  div class: 'poster-photo-label family-frame-photo-label' do
                    label 'Family Frame Photo'
                  end

                  # Check aspect ratio and show warning if needed
                  if bg_removed_family_photo_id.present? && user.family_frame_photo.blank?
                    bg_removed_photo = bg_removed_poster_photos.find { |photo| photo.id == bg_removed_family_photo_id }
                    unless helpers.check_aspect_ratio(bg_removed_photo)
                      div class: 'aspect-ratio-info' do
                        span "Info: The image doesn't have the correct dimensions of 250px width and 280px height. Please resize the image to match these dimensions."
                      end
                    end
                  end

                  if helpers.show_dummy_photo_info?(user.family_frame_photo, family_photo_bg_removed_url)
                    div class: 'dummy-photo-info' do
                      span 'Background removal for this image failed. Please upload a new image with the background already removed.'
                    end
                  end

                  div class: 'photos-section' do
                    # Center Column (read-only image & its download button)
                    div class: 'image-preview-parent' do
                      # Show the original or placeholder image
                      div class: 'image-preview' do
                        image_tag(
                          family_photo_url,
                        )
                      end
                      # Download button for the center column
                      div class: 'download-btn-parent' do
                        link_to 'Download', original_poster_photos.find { |photo| photo.id == family_photo_id }
                                                                  .photo_download_url, class: 'download-btn'

                      end
                    end

                    div class: 'arrow-symbol' do
                      span '➡️'
                    end

                    # Right Column (editable upload + second image + second download)
                    div class: 'image-preview-parent' do
                      # Image preview
                      div class: 'image-preview', id: 'bg_removed_poster_photo' do
                        image_url = if user.family_frame_photo.present? && user.family_frame_photo.url.present?
                                      user.family_frame_photo.url
                                    elsif family_photo_bg_removed_url.present?
                                      family_photo_bg_removed_url
                                    else
                                      dummy_cutout_url
                                    end
                        image_tag(image_url)
                      end
                      # Download button for the right column
                      div class: 'download-btn-parent' do
                        if user.family_frame_photo.present?
                          link_to 'Download', user.family_frame_photo.photo_download_url, class: 'download-btn'
                        else
                          photo_id = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.family_frame_photo_key }&.dig(:photo, :id)
                          photo_obj = bg_removed_poster_photos.find { |photo| photo.id == photo_id }
                          # Check if photo_obj is present and has a photo_download_url method
                          if photo_obj && photo_obj.respond_to?(:photo_download_url)
                            link_to 'Download', photo_obj.photo_download_url, class: 'download-btn', download: true
                          else
                            content_tag(:span, 'Download', class: 'download-btn disabled', style: 'pointer-events: none; opacity: 0.5;')
                          end
                        end
                      end

                      div class: 'photo-file' do
                        # File input
                        f.input :family_frame_photo, as: :file, required: false, label: false, input_html: { class: 'upload-btn' }
                        # hidden input to store the accepted value for the first time if user don't have family_frame_photo
                        if user.family_frame_photo.blank?
                          f.hidden_field :bg_removed_family_frame_photo, value: bg_removed_family_photo_id
                        end
                      end
                    end
                  end
                end
              end

              # ---------------------------
              # Section 4: Hero Frame Original (if available)
              # ---------------------------
              if (hero_frame_photo_url = original_poster_photos_json.find { |photo| photo[:type] == Constants.hero_frame_photo_original_key }&.dig(:photo, :url)).present?
                hero_frame_photo_id = original_poster_photos_json.find { |photo| photo[:type] == Constants.hero_frame_photo_original_key }&.dig(:photo, :id)
                hero_frame_photo_bg_removed_url = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.hero_frame_photo_key }&.dig(:photo, :url)
                bg_removed_hero_frame_photo_id = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.hero_frame_photo_key }&.dig(:photo, :id)

                div class: 'poster-photo-section' do

                  # Left Column
                  div class: 'poster-photo-label hero-frame-photo-label' do
                    label 'Hero Frame Photo'
                  end

                  # Check aspect ratio and show warning if needed
                  if bg_removed_hero_frame_photo_id.present? && user.hero_frame_photo.blank?
                    bg_removed_photo = bg_removed_poster_photos.find { |photo| photo.id == bg_removed_hero_frame_photo_id }
                    unless helpers.check_aspect_ratio(bg_removed_photo)
                      div class: 'aspect-ratio-info' do
                        span "Info: The image doesn't have the correct dimensions of 250px width and 280px height. Please resize the image to match these dimensions."
                      end
                    end
                  end

                  if helpers.show_dummy_photo_info?(user.hero_frame_photo, hero_frame_photo_bg_removed_url)
                    div class: 'dummy-photo-info' do
                      span 'Background removal for this image failed. Please upload a new image with the background already removed.'
                    end
                  end

                  div class: 'photos-section' do
                    # Center Column (read-only image & its download button)
                    div class: 'image-preview-parent' do
                      # Show the original or placeholder image
                      div class: 'image-preview' do
                        image_tag(
                          hero_frame_photo_url,
                        )
                      end
                      # Download button for the center column
                      div class: 'download-btn-parent' do
                        link_to 'Download',
                                original_poster_photos.find { |photo| photo.id == hero_frame_photo_id }.photo_download_url,
                                class: 'download-btn', download: true
                      end
                    end

                    div class: 'arrow-symbol' do
                      span '➡️'
                    end

                    # Right Column (editable upload + second image + second download)
                    div class: 'image-preview-parent' do
                      # Image preview
                      div class: 'image-preview', id: 'bg_removed_poster_photo' do
                        image_url = if user.hero_frame_photo.present? && user.hero_frame_photo.url.present?
                                      user.hero_frame_photo.url
                                    elsif hero_frame_photo_bg_removed_url.present?
                                      hero_frame_photo_bg_removed_url
                                    else
                                      dummy_cutout_url
                                    end
                        image_tag(image_url)
                      end
                      # Download button for the right column
                      div class: 'download-btn-parent' do
                        if user.hero_frame_photo.present?
                          link_to 'Download', user.hero_frame_photo.photo_download_url, class: 'download-btn'
                        else
                          # Find the bg removed hero frame photo by id
                          photo_id = bg_removed_poster_photos_json.find { |photo| photo[:type] == Constants.hero_frame_photo_key }&.dig(:photo, :id)
                          # Check if the photo exists and has a photo_download_url method
                          photo_obj = bg_removed_poster_photos.find { |photo| photo.id == photo_id }
                          if photo_obj && photo_obj.respond_to?(:photo_download_url)
                            link_to 'Download', photo_obj.photo_download_url, class: 'download-btn', download: true
                          else
                            content_tag(:span, 'Download', class: 'download-btn disabled', style: 'pointer-events: none; opacity: 0.5;')
                          end
                        end
                      end

                      div class: 'photo-file' do
                        # File input
                        f.input :hero_frame_photo, as: :file, required: false, label: false, input_html: { class: 'upload-btn' }
                        # hidden input to store the accepted value for the first time if user don't have hero_frame_photo
                        if user.hero_frame_photo.blank?
                          f.hidden_field :bg_removed_hero_frame_photo, value: bg_removed_hero_frame_photo_id
                        end
                      end
                    end
                  end
                end
              end
            end

          end
        end
        # ---------------------
        # Panel: Existing Circles
        # ---------------------
        # Get records where a circle is already assigned
        existing_circles = UserLeaderPhoto.where(user_poster_layout: user_poster_layout).where.not(circle_id: nil)
        panel "Existing Circles" do
          if existing_circles.present?
            existing_circles.each do |record|
              circle_name = record.circle&.name
              circle_photo = record.photo

              div class: "existing-circle-row", id: "existing_circle_#{record.id}" do
                # First Column: Current Circle Info
                div class: "circle-info" do
                  h2 do
                    link_to circle_name, admin_circle_path(record.circle_id), target: "_blank"
                  end
                  text_node image_tag(circle_photo.url, alt: circle_name, class: "oe-image") if circle_photo.present?
                end

                div class: "circle-search-parent" do
                  div class: 'circle-search-arrow-symbol', id: "circle_search_arrow_#{record.id.to_s}" do
                    span '➡️'
                  end
                  div class: "circle-search-section" do
                    # Third Column: Search field to reassign a new circle
                    div class: "circle-search" do
                      select_tag "existing_circle_#{record.id}", options_for_select([]),
                                 prompt: "Search new circle",
                                 class: "existing-circle-change-select",
                                 data: { layout_user_id: user.id.to_s, req_circle_id: record.id.to_s }
                    end
                    # Container for showing the selected poster photo
                    div class: "selected-image-container", id: "selected_image_#{record.id.to_s}"
                  end
                end
              end
            end
          else
            div "No existing circles."
          end
        end
        # Hidden container for storing dynamically added hidden inputs for existing circles
        div id: "existing_circles_hidden_inputs"

        # ---------------------
        # Panel: Requested Circles
        # ---------------------
        # Get records where a circle is not assigned yet
        requested_circles = UserLeaderPhoto.where(user_poster_layout: user_poster_layout, circle_id: nil)
        panel "Requested Circles" do
          if requested_circles.present?
            requested_circles.each do |req_circle|
              circle_name = req_circle.draft_data&.dig("circle_name")
              circle_photo = req_circle.photo

              div class: "requested-circle-row", id: "requested_circle_#{req_circle.id}" do
                # First Column: Circle name and image
                div class: "circle-info" do
                  h2 circle_name
                  text_node image_tag(circle_photo.url, alt: circle_name, class: "oe-image") if circle_photo.present?
                  div class: 'requested-circle-download-btn' do
                    link_to 'Download', circle_photo.photo_download_url, class: 'download-btn'
                  end
                end

                div class: "circle-search-parent" do
                  div class: 'circle-search-arrow-symbol', id: "circle_search_arrow_#{req_circle.id}" do
                    span '➡️'
                  end
                  div class: "circle-search-section" do
                    div class: "circle-search" do
                      select_tag "existing_circle_#{req_circle.id}", options_for_select([]),
                                 prompt: "Search existing circles",
                                 class: "existing-circle-select",
                                 data: { layout_user_id: user.id.to_s, req_circle_id: req_circle.id.to_s }
                    end
                    # Container for showing the selected poster photo
                    div class: "selected-image-container", id: "selected_image_#{req_circle.id}"
                  end
                end

                # Second Column: Create Circle link and search field
                div class: "circle-actions" do
                  text_node link_to("Create Circle", new_admin_circle_path(name: circle_name, name_en: circle_name,
                                                                           circle_type: :interest,
                                                                           level: :political_leader,
                                                                           entity_type: 'User',
                                                                           entity_id: user.id), class: "button",
                                    target: "_blank", onclick: "window.open(this.href, '_blank', 'noopener,noreferrer'); return false;")
                  # Note: The photo selection modal will trigger JS to create/update the hidden input.
                end
              end
            end
          else
            div "No circles requested."
          end
        end
        # Hidden container for storing dynamically added hidden inputs for each requested circle.
        div id: "requested_circles_hidden_inputs"

        # remarks field as text area
        f.input :remarks, as: :text, label: 'Remarks',
                input_html: { class: 'remarks-textarea', rows: 1, placeholder: 'Enter remarks here...' },
                wrapper_html: { class: 'remarks-parent' }, required: false

        div class: "submit-warning", style: "display:none; color: red; margin-top: 10px;" do
          text_node ""
        end

        f.actions do
          f.action :submit, label: 'Submit', button_html: {
            class: 'activate-button'
          }
          # on remarks field change, enable the submit button else disable it
          f.action :submit, label: 'Incomplete', button_html: {
            class: 'incomplete-btn'
          }
        end
      end
    end
  end
end
