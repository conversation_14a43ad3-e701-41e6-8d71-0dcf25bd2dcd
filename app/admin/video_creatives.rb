ActiveAdmin.register VideoCreative do

  controller do
    include MediaServiceUpload
  end

  menu priority: 6, :parent => 'Posters V2',
       if: proc{ role = current_admin_user.role.to_sym.in?([:admin, :graphic_designer]) }

  scope :all, default: true

  scope :scheduled do |vc|
    vc.where('active = ? AND start_time > ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
  end

  scope :live do |vc|
    vc.where('active = ? AND start_time <= ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
  end

  scope :expired do |vc|
    vc.where('active = ? AND end_time < ?', true, Time.zone.now)
  end

  scope :closed do |vc|
    vc.where(active: false)
  end

  actions :all, except: [:destroy]

  index do
    selectable_column
    id_column
    column :event do |vc|
      link_to(vc.event.name, admin_event_path(vc.event)) if vc.event.present?
    end

    column :circles do |vc|
      vc.video_creative_circles.map do |video_creative_circle|
        link_to(video_creative_circle.circle.name, admin_circle_path(video_creative_circle.circle)) if video_creative_circle.present?
      end
    end

    column :start_time
    column :end_time
    column :status do |vcs|
      status = ''
      if vcs.active == false
        status = "<span style='color: #FF0000; font-weight: bold;'>Closed</span>"
      elsif vcs.active == true && vcs.start_time.present? && vcs.end_time.present?
        if vcs.start_time > Time.zone.now && vcs.end_time > Time.zone.now
          status = "<span style='color: #008000; font-weight: bold;'>Scheduled</span>"
        elsif vcs.start_time < Time.zone.now && vcs.end_time > Time.zone.now
          status = "<span style='color: #008000; font-weight: bold;'>Live</span>"
        elsif vcs.end_time < Time.zone.now
          status = "<span style='color: #FFA500; font-weight: bold;'>Expired</span>"
        else
          status = 'Unknown'
        end
      end
      status.html_safe
    end

    column :designer
    actions name: "Actions"
  end

  show do
    attributes_table do
      row :id
      row :event do |vc|
        vc.event.present? ? link_to(vc.event.name, admin_event_path(vc.event)) : '-'
      end
      row "Event Priority" do |vc|
        vc.event.present? ? vc.event.priority.upcase : '-'
      end
      row :kind
      row :video do |vc|
        if vc.video.present? && vc.video.source_url.present?
          video_tag vc.video.source_url,
                    controls: true,
                    width: 280,
                    height: 150
        else
          "Video not available"
        end
      end

      row "video bitrate" do |vc|
        vc.video.bitrate
      end

      row "video duration" do |vc|
        "#{vc.video.duration} sec"
      end

      row "video aspect ratio" do |vc|
        "#{vc.video.width}x#{vc.video.height}"
      end

      row "video size" do |vc|
        video_size = (vc.video.duration * vc.video.bitrate)/8.0
        "#{video_size.round(2)} MB"
      end

      row :circles do |vc|
        vc.video_creative_circles.map do |video_creative_circle|
          link_to(video_creative_circle.circle.name, admin_circle_path(video_creative_circle.circle)) if video_creative_circle.present?
        end
      end
      row :start_time
      row :end_time
      row :designer
      row :active
      row :caption
      row :creator do |vc|
        link_to(vc.creator.email, admin_admin_user_path(vc.creator)) if vc.creator.present?
      end
    end

    if resource.video_creative_circles.present?
      panel 'Creative deep link for individual circle' do
        table_for resource.video_creative_circles do
          column :circle do |vcc|
            link_to(vcc.circle.name, admin_circle_path(vcc.circle))
          end
          column :deep_link do |vcc|
            div do
              link = "praja-app://buzz.praja.app/posters/layout?video_creative_id=#{resource.id}&circle_id=#{vcc.circle.id}"
              span link
              button class: 'copy-link-button', "data-link": link do
                icon('fa-regular', 'copy') + " Copy"
              end
            end
          end
        end
      end
    end
  end


  form :html => {:multipart => true} do |f|
    f.semantic_errors
    f.inputs "Video Creative Details" do
      if f.object.new_record?
        f.input :has_event, as: :boolean, label: 'Has Event', input_html: { checked: true, id: 'has_event_checkbox', data: { if: 'changed', then: 'callback has_event_toggled' } }
        f.input :event_id, as: :searchable_select, ajax: { resource: Event, collection_name: :events }, wrapper_html: { class: 'has_event' }, label: 'Event ID', input_html: { "data-placeholder": "Search by ID or Name.." }
        f.input :kind, as: :select, collection: VideoCreative.kinds.keys
        f.input :video_file, as: :file, label: 'Video File', hint: '<span style="color: #f0ad4e;">*Video aspect ratio - <b>720x1100</b></span>'.html_safe, input_html: { id: 'check_video_dimensions' }
        f.input :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers }, input_html: { "data-placeholder": "Search by ID or email.." }
        f.input :start_time, as: :datetime_picker, hint: "If blank & event is present, event's start time will be used"
        f.input :end_time, as: :datetime_picker, hint: "If blank & event is present, event's end time will be used"

        f.input :circle_ids,
                label: 'Circle(s)',
                as: :searchable_select,
                multiple: true, ajax: { resource: Circle, collection_name: :poster_circles },
                input_html: { "data-placeholder": 'Circle id or Name or Short name' },
                wrapper_html: { class: 'no_event' }

        f.input :active
        f.input :caption, as: :text, input_html: { rows: 5, cols: 50 }
      else
        if f.object.event_id.present?
          f.input :event, input_html: { disabled: true, value: f.object.event&.name }
        end
        f.input :kind, as: :select, collection: VideoCreative.kinds.keys
        f.input :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers }, input_html: { "data-placeholder": "Search by ID or email.." }
        f.input :start_time, as: :datetime_picker, input_html: { id: 'video_creative_start_time' }
        f.input :end_time, as: :datetime_picker
        f.input :active, input_html: { id: 'video_creative_active_checkbox' }
        f.input :caption, as: :text, input_html: { rows: 5, cols: 50 }
        if f.object.event_id.blank?
          f.input :circle_ids,
                  label: 'Circle(s)',
                  as: :searchable_select,
                  multiple: true,
                  ajax: {resource: Circle, collection_name: :poster_circles},
                  input_html: { "data-placeholder": 'Circle id or Name or Short name' },
                  hint: "Existing circles #{f.object.video_creative_circles.map { |vcc| vcc.circle.id }.join(', ')}"
        end
      end
    end
    f.actions
  end


  controller do
    def create
      attrs = params[:video_creative]
      create_another = params[:create_another]
      has_event = attrs[:has_event].to_i == 1
      attrs[:event_id] = nil unless has_event

      circle_ids = []
      if attrs[:event_id].present?
        circle_ids = EventCircle.where(event_id: attrs[:event_id]).pluck(:circle_id)
      end

      circle_ids = attrs[:circle_ids].compact_blank unless circle_ids.present?

      if attrs[:video_file].blank?
        return redirect_back fallback_location: new_admin_video_creative_path,
                      alert: 'Video file is required'
      end

      if attrs[:video_file].present?
        attrs[:media_service_videos] = upload_video(attrs[:video_file], current_admin_user.id)
        video_data = attrs[:media_service_videos]

        unless video_data.present?
          return redirect_back fallback_location: new_admin_video_creative_path, alert: "Video upload to media service failed"
        end

        unless video_data[:height].present? && video_data[:width].present?
          return redirect_back fallback_location: new_admin_video_creative_path, alert: "Invalid video metadata returned from media service"
        end

        video_creative_height = video_data[:height].to_f
        video_creative_width = video_data[:width].to_f

        uploaded_aspect_ratio = video_creative_width / video_creative_height
        expected_aspect_ratio = 720.0 / 1100.0

        #The allowed margin is 0.01
        if (uploaded_aspect_ratio - expected_aspect_ratio).abs > 0.01
          return redirect_back fallback_location: new_admin_video_creative_path, alert: "Please check the video aspect ratio. Uploaded dimensions: #{video_creative_width}x#{video_creative_height}."
        end
        video = Video.new(ms_data: video_data, user: current_admin_user, service: video_data[:service])
        video.save
      end

      if circle_ids.present?
        @video_creative = VideoCreative.new(kind: attrs[:kind],
                                            designer_id: attrs[:designer_id],
                                            start_time: attrs[:start_time],
                                            end_time: attrs[:end_time],
                                            active: attrs[:active],
                                            creator: current_admin_user,
                                            )

        @video_creative.video_id = video.id if video.present?
        @video_creative.event_id = attrs[:event_id] if has_event && attrs[:event_id].present?
        @video_creative.caption = attrs[:caption] if attrs[:caption].present?

        circle_ids.each do |circle_id|
          @video_creative.video_creative_circles.build(circle_id: circle_id)
        end

        if @video_creative.save
          flash[:notice] = 'Video Creative created successfully'
          if create_another == 'on'
            return redirect_to new_admin_video_creative_path
          else
            return redirect_to admin_video_creative_path(@video_creative)
          end
        else
          flash[:error] = @video_creative.errors.full_messages.first
          return render :new
        end
      else
        message = 'Please select the event' if attrs[:has_event].to_i == 1 && attrs[:event_id].blank?
        message = 'Please select the circle' if attrs[:has_event].to_i == 0 && circle_ids.blank?
        return redirect_to new_admin_video_creative_path, alert: message
      end
    end

    def update
      attrs = params[:video_creative]

      @video_creative = VideoCreative.find(params[:id])
      update_video_creative_attributes(attrs)

      deleting_circle_ids = update_circles(attrs) if @video_creative.event_id.blank?

      if @video_creative.save
        @video_creative.video_creative_circles.where(circle_id: deleting_circle_ids).destroy_all if @video_creative.event_id.blank? && deleting_circle_ids.present?
        redirect_to admin_video_creative_path(@video_creative)
      else
        flash[:error] = @video_creative.errors.full_messages.first
        render :edit
      end
    end

    def upload_video(video, user_id)
      form_data = [["video", video]]
      upload_to_media_service(form_data, user_id, "video")
    end

    private

    def update_video_creative_attributes(attrs)
      @video_creative.assign_attributes(
        kind: attrs[:kind],
        active: attrs[:active],
        start_time: attrs[:start_time],
        end_time: attrs[:end_time],
        designer_id: attrs[:designer_id],
        caption: attrs[:caption]
      )
    end

    def update_circles(attrs)
      circle_ids = attrs[:circle_ids].compact_blank.map(&:to_i)
      return [] unless circle_ids.present?
      existing_circle_ids = @video_creative.video_creative_circles.map(&:circle_id)

      new_circle_ids = circle_ids - existing_circle_ids
      deleting_circle_ids = existing_circle_ids - circle_ids

      if new_circle_ids.present?
        new_circle_ids.each do |new_circle_id|
          @video_creative.video_creative_circles.build(circle_id: new_circle_id)
        end
      end

      if new_circle_ids.present? || deleting_circle_ids.present?
        IndexCreativesForPostersFeed.perform_async("video_creative_#{params[:id]}")
      end
      deleting_circle_ids
    end

  end

  filter :kind, as: :select, collection: VideoCreative.kinds
  filter :id, label: 'Video Creative ID'
  filter :event_priority,
         as: :select,
         collection: Event.priorities.map { |k, v| [k.titleize, v] },
         label: 'Event Priority'
  filter :active
  filter :video_creative_circle, label: 'Circle ID (use \'00\' for filtering \'public\' circle)', as: :numeric, filters: [:eq]
  filter :event_id, label: 'Event ID'
  filter :circle_ids, label: 'Circle ID'
  filter :creator_id, label: 'Creator ID'
  filter :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers },
         input_html: { "data-placeholder": "Search by ID or email.." }
  filter :start_time
  filter :end_time
  filter :created_at
  filter :updated_at
end
