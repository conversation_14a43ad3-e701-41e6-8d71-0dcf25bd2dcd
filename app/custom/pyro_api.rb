# frozen_string_literal: true

class PyroApi
  BASE_URL = "https://hihrftwrriygnbrsvlrr.supabase.co/functions/v1"
  WEBHOOK_ENDPOINT = "/ticket-webhook"
  TENANT_ID = "d6db1158-2212-4d94-bb01-2c28b971d9a9"

  # @!method send_api_request(payload)
  # This method is used to send API request to Pyro
  # @param payload [Hash] Request payload
  def self.send_api_request(payload)
    bearer_token = Rails.application.credentials[:pyro_bearer_token]
    webhook_secret = Rails.application.credentials[:pyro_webhook_secret]
    
    raise "Pyro bearer token is missing from Rails credentials" if bearer_token.blank?
    raise "Pyro webhook secret is missing from Rails credentials" if webhook_secret.blank?

    uri = URI.parse("#{BASE_URL}#{WEBHOOK_ENDPOINT}")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri.request_uri)
    request["Authorization"] = "Bearer #{bearer_token}"
    request["Content-Type"] = "application/json"
    request["x-WEBHOOK-SECRET"] = webhook_secret

    request.body = payload.to_json

    response = http.request(request)
    response_body = response.body.present? ? JSON.parse(response.body) : {}

    if response.code == "200" || response.code == "201"
      response_body
    else
      Rails.logger.error("Pyro API request failed with response code: #{response.code} :: body: #{response_body} :: payload: #{payload}")
      raise "Pyro API request failed with response code: #{response.code}"
    end
  rescue JSON::ParserError => e
    Rails.logger.error("Pyro API response parsing failed: #{e.message} :: response body: #{response.body}")
    raise "Pyro API response parsing failed"
  end
end
