require 'open-uri'

class ApplicationController < ActionController::Base
  include Pundit::Authorization
  protect_from_forgery with: :exception, prepend: true
  before_action :set_paper_trail_whodunnit

  protected

  def restrict_agent_partner_admin_access
    # Only apply this restriction to admin routes
    return unless request.path.start_with?('/admin')
    
    # Skip if user is not logged in (let authenticate_admin_user! handle it)
    return unless current_admin_user.present?
    
    # Skip if user is not an agent partner
    return unless current_admin_user.agent_partner_role?
    
    # Allow access to login/logout (Devise controllers)
    return if devise_controller?
    
    # Allow access to agent partners controller
    return if controller_name == 'agent_partners'
    
    # Allow access to specific admin controller actions
    if controller_name == 'admin' && action_name.in?(%w[validate_admin_session external_redirect get_dashboard_link])
      return
    end
    
    # Block everything else
    render json: { error: 'Forbidden' }, status: :forbidden
  end

  def user_for_paper_trail
    admin_user_signed_in? ? current_admin_user.try(:id) : 'unknown'
  end

  # Override the default authenticate_admin_user! method
  def authenticate_admin_user!
    if admin_user_signed_in?
      true
    else
      if request.get? && request.fullpath.start_with?('/admin') && !request.xhr?
        redirect_url = request.url
        redirect_to "#{new_admin_user_session_path}?redirect_url=#{redirect_url}"
      else
        redirect_to new_admin_user_session_path
      end
      false
    end
  end
end
