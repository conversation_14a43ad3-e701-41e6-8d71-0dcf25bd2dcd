# extend this class with ServiceApiController
class MediaServiceController < ServiceApiController
  before_action :validate_service
  def index
    render json: { message: 'Hello from RoR Service' }
  end

  def update_video_process
    if params.present?
      metadata = params[:metadata]
      processed_url = params[:processed_url]

      if metadata.present? && processed_url.present?
        video_id = params[:metadata][:video_id]

        if video_id.present?
          video = Video.find_by(id: video_id)

          video.url = processed_url
          video.status = :processed
          video.save!

          post = PostVideo.where(video: video).last.post
          post.flush_cache

          render json: { success: true }, status: :ok
          return
        end
      end
    end
    render json: { success: false }, status: :unprocessable_entity
  end

  # Poster Page End point (This Page Displays Single Poster)
  # @note: Only for Posters Preview Dashboard [Poster Campaign Purpose]
  def my_poster_page
    creative_id = params[:creative_id]&.to_i
    frame_id = params[:frame_id]
    user_id = params[:user_id]&.to_i

    # @note: This request will always be made from the Poster Preview Dashboard. Assuming latest version.
    AppVersionSupport.new('2410.05.01')

    if user_id.blank?
      return render json: { success: false, message: 'User not found' }, status: :not_found
    end

    if creative_id.blank?
      return render json: { success: false, message: 'Creative not found' }, status: :not_found
    end

    user = User.find_by_id(user_id)

    if user.blank?
      return render json: { success: false, message: 'User not found' }, status: :not_found
    end

    layout = user.get_user_layout(frame_id: frame_id)

    # when layout is not found as we are using pitch premium layout for free users
    # So, this method needs to return layout even for free users
    if layout.blank?
      return render json: { success: false, message: 'Layout not found' }, status: :not_found
    end

    creative_hash = PosterCreative.find_by(id: creative_id)
    creative = creative_hash.build_creative_json(user: user, is_layout_locked: false,
                                                 is_eligible_for_premium_creatives: true)

    render json: { layout: layout, creative: creative }, status: :ok
  end

  # Protocol Page End point (Returns protocol photos for a user)
  # Combines header_1_photos and header_2_photos into protocol_photos
  # @note: Only for Protocol Page Preview Dashboard [Video Posters Purpose]
  def protocol_page
    user_id = params[:user_id]&.to_i

    if user_id.blank?
      Honeybadger.notify('Protocol page: User ID not provided', context: { params: })
      return render json: { success: false, message: 'User not found' }, status: :not_found
    end

    user = User.find_by_id(user_id)

    if user.blank?
      Honeybadger.notify('Protocol page: User not found', context: { user_id:, params: })
      return render json: { success: false, message: 'User not found' }, status: :not_found
    end

    # Set context once for all subsequent Honeybadger notifications
    Honeybadger.context({ user_id:, params: })

    user_poster_layout = user.get_user_poster_layout_including_inactive

    if user_poster_layout.blank?
      render json: { error: "User poster layout not found" }, status: :not_found
      return
    end

    layout, header_1_photos, header_2_photos = user.get_layout_and_header_photos(user_poster_layout.id, entity: user)

    # Combine both header_1_photos and header_2_photos into protocol_photos
    protocol_photos = (header_1_photos || []) + (header_2_photos || [])

    if protocol_photos.empty? && layout != 'layout_0_0'
      Honeybadger.notify('Protocol page: No protocol photos found for user')
    end

    render json: { success: true, protocol_photos: protocol_photos }, status: :ok
  end

  private
  def validate_service
    if @service != 'media'
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end
end
