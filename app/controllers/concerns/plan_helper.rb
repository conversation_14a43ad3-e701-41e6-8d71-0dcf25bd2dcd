# app/controllers/concerns/plan_helper.rb
module PlanHelper
  extend ActiveSupport::Concern

  def build_title(info_toast)
    [info_toast[:label], info_toast[:text]].compact.join(' ')
  end

  def premium_plans_json(user:, plans:)
    plans_json = plans.map { |plan| build_plan_hash(plan) }
    select_plan_for_premium_plans_json(user: user, plans_json: plans_json)
  end

  # We need to refactor this once single step paywall succeeds
  # right now this method is being used in both annual paywall and single step paywall
  def premium_plans_json_for_annual_recharge_paywall(user:, plans:)
    plans_json = plans.map { |plan| build_plan_hash_for_annual_paywall(plan) }
    select_plan_for_premium_plans_json_v2(user: user, plans_json: plans_json)
  end

  def premium_plans_json_for_downgrade(user:, plans:)
    plans_json = plans.map { |plan| build_plan_hash_for_downgrade(plan) }
    select_plan_for_premium_plans_json(user: user, plans_json: plans_json)
  end

  def select_plan_for_premium_plans_json_v2(user:, plans_json:)
    if user.should_pitch_yearly_package?
      yearly_plan = plans_json.find { |plan| plan[:duration] == 12 }
      yearly_plan[:selected] = true if yearly_plan.present?
    end

    if plans_json.none? { |plan| plan[:selected] }
      plan_ids = plans_json.map { |plan| plan[:id] }
      last_activated_subscription = Subscription.where(plan_id: plan_ids,
                                                       user_id: user.id,
                                                       status: [:active, :cancelled, :on_hold, :paused])
                                                .last
      last_activated_plan = plans_json.find { |plan| plan[:id] == last_activated_subscription&.plan_id }
      last_activated_plan[:selected] = true if last_activated_plan.present?
    end

    if plans_json.none? { |plan| plan[:selected] }
      min_amount_plan = plans_json.min_by { |plan| plan[:amount] }
      min_amount_plan[:selected] = true if min_amount_plan.present?
    end

    if plans_json.none? { |plan| plan[:selected] }
      first_plan = plans_json.first
      first_plan[:selected] = true if first_plan.present?
    end

    plans_json
  end

  def select_plan_for_premium_plans_json(user:, plans_json:)
    plan_ids = plans_json.map { |plan| plan[:id] }
    default_plan_id = user.default_plan.id

    if default_plan_id.positive?
      default_plan = plans_json.find { |plan| plan[:id] == default_plan_id }
      default_plan[:selected] = true if default_plan
    else
      # last plan value is selected if it is not set
      open_plan_subscriptions = Subscription.where(plan_id: plan_ids, user_id: user.id).open
      last_open_plan = open_plan_subscriptions.last
      if last_open_plan.present?
        last_plan = plans_json.find { |plan| plan[:id] == last_open_plan.plan_id }
        last_plan[:selected] = true
      end
    end

    if plans_json.none? { |plan| plan[:selected] }
      min_amount_plan = plans_json.min_by { |plan| plan[:amount] }
      min_amount_plan[:selected] = true if min_amount_plan
    end

    if plans_json.none? { |plan| plan[:selected] }
      first_plan = plans_json.first
      first_plan[:selected] = true
    end

    plans_json
  end

  def build_plan_hash(plan)
    duration = plan.duration_in_months
    discount_percentage = calculate_discount_percentage(plan)

    {
      id: plan.id,
      duration: duration,
      duration_text: I18n.t('premium_bottom_sheet.plans.duration_text', count: duration, duration: duration),
      per_month_amount: plan.amount / duration,
      per_month_text: duration > 1 ? I18n.t('premium_bottom_sheet.plans.per_month_text', amount: plan.amount / duration) : nil,
      amount: plan.amount,
      discount_percentage: discount_percentage.positive? ? discount_percentage : 0,
      discount_text: discount_percentage.positive? ? I18n.t('premium_bottom_sheet.plans.discount_text', discount: discount_percentage) : nil,
      discount_text_color: 0xffFFFFFF,
      discount_text_bg_color: 0xff6ACE13,
    }
  end

  def build_plan_hash_for_annual_paywall(plan)
    duration = plan.duration_in_months
    discount_percentage = calculate_discount_percentage(plan)

    {
      id: plan.id,
      duration: duration,
      duration_text: I18n.t('annual_recharge_paywall.plans.duration_text', count: duration, duration: duration),
      per_month_amount: plan.amount / duration,
      per_month_text: I18n.t('annual_recharge_paywall.per_month_text', count: duration, amount: plan.amount / duration),
      amount: plan.amount,
      discount_percentage: discount_percentage.positive? ? discount_percentage : 0,
      discount_text: discount_percentage.positive? ? I18n.t('annual_recharge_paywall.plans.discount_text', discount: discount_percentage) : nil,
      discount_text_color: 0xffFFFFFF,
      discount_text_bg_color: 0xff6ACE13,
    }
  end

  def build_plan_hash_for_downgrade(plan)
    duration = plan.duration_in_months
    discount_percentage = calculate_discount_percentage(plan)

    {
      id: plan.id,
      duration: duration,
      duration_text: I18n.t('downgrade_bottom_sheet.duration_text', count: duration, duration: duration),
      per_month_amount: plan.amount / duration,
      per_month_text: duration == 12 ? I18n.t('downgrade_bottom_sheet.per_year_text', count: duration, amount: plan.amount / duration) : I18n.t('downgrade_bottom_sheet.per_month_text', count: duration, amount: plan.amount / duration),
      amount: plan.amount,
      discount_percentage: discount_percentage.positive? ? discount_percentage : 0,
      discount_text: "",
      discount_text_color: 0xffFFFFFF,
      discount_text_bg_color: 0xff6ACE13,
    }

  end

  def calculate_discount_percentage(plan)
    ((plan.discount_amount.to_f / plan.total_amount.to_f) * 100.0).round(0)
  end

  def build_response_json(user:, title:, title_text_color:, terms:, plans:, sub_title: nil, existing_premium_users: nil)
    rm_user = user.relation_manager_feed_item(user: user)&.dig(:rm_user)
    {
      title: title,
      title_text_color: title_text_color,
      sub_title: sub_title,
      sub_title_text_color: 0xff222222,
      rm_user: rm_user,
      auto_recharge_text: I18n.t('premium_bottom_sheet.auto_recharge_text'),
      auto_recharge_cancel_text: I18n.t('premium_bottom_sheet.auto_recharge_cancel_text'),
      plans: plans,
      existing_premium_users: existing_premium_users,
      payment_share_text: I18n.t('premium_bottom_sheet.payment_share_text'),
      terms: terms,
      use_juspay_sdk: AppVersionSupport.juspay_sdk_enabled?,
      payment_gateway: AppVersionSupport.juspay_sdk_enabled? ? 'juspay' : 'url',
      button_details: {
        text_suffix: I18n.t('premium_bottom_sheet.button_text_suffix'),
      },
      analytics_params: {
        title: title,
        plans_count: plans.count,
        plan_ids: plans.map { |plan| plan[:id] },
        default_plan_id: plans.map { |plan| plan[:selected] ? plan[:id] : nil }.compact.first,
        default_plan_amount: plans.map { |plan| plan[:selected] ? plan[:amount] : nil }.compact.first,
        plans_shown: plans.map { |plan| plan.slice(:id, :duration, :amount, :per_month_amount, :discount_percentage) },
        rm_user_id: rm_user&.dig(:id),
      }.compact,
    }
  end
end
