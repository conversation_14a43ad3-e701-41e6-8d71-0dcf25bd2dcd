module UsersConcern
  extend ActiveSupport::Concern
  include ProfileViewHelper
  include LayoutsForDemo

  included do

    def deactivate_existing_user_video_posters
      UserVideoPoster.where(user_id: id, active: true).update_all(active: false)
    end

    # check if there is any poster share today from PosterShare
    def has_poster_share_today
      PosterShare.where(user_id: self.id).where('created_at >= ?', Time.zone.now.beginning_of_day).exists?
    end

    # check if user saw frames twice today
    def has_seen_frames_twice
      FrameView.where(user_id: self.id).where('created_at >= ?', Time.zone.now.beginning_of_day).count >= 2
    end

    def subscription_info_toast(user:)
      subscription = user.active_subscription
      if subscription.blank? and SubscriptionUtils.is_user_in_grace_period?(user.id)
        subscription = user.on_hold_subscription
      end
      if user.is_poster_subscribed
        poster_premium_end_date = SubscriptionUtils.active_poster_premium_end_date(user.id)
        label = I18n.t('subscription_info_toast.premium_duration_label')
        remaining_poster_premium_period_in_days = poster_premium_end_date.present? ? (poster_premium_end_date.to_date - Time.zone.today).to_i + 1 : 0
        type, text = if remaining_poster_premium_period_in_days >= 30
                       ['info', poster_premium_end_date.strftime('%d/%m/%Y')]
                     else
                       label = nil
                       if subscription.blank?
                         ['alert', I18n.t('subscription_info_toast.set_auto_recharge')]
                       elsif subscription.last_recharge_failed?
                         next_retry_date = subscription.get_next_retry_charge_date
                         ['alert', next_retry_date.blank? ? I18n.t('subscription_info_toast.recharge_stopped') : I18n.t('premium_experience_subscription_retry_string.text', charge: SubscriptionCharge.where(user_id: user.id).last.charge_amount, retry_date: next_retry_date.strftime('%d-%b'))]
                       else
                         ['quiet', I18n.t('subscription_info_toast.next_recharge', date: poster_premium_end_date.strftime('%d/%m/%Y'))]
                       end
                     end
      elsif SubscriptionUtils.has_user_ever_subscribed?(user.id)
        if subscription.blank?
          type = 'alert'
          text = I18n.t('subscription_info_toast.set_auto_recharge')
        elsif subscription.last_recharge_failed?
          next_retry_date = subscription.get_next_retry_charge_date
          type = 'alert'
          text = next_retry_date.blank? ? I18n.t('subscription_info_toast.recharge_stopped') : I18n.t('premium_experience_subscription_retry_string.text', charge: SubscriptionCharge.where(user_id: user.id).last.charge_amount, retry_date: next_retry_date.strftime('%d-%b'))
        else
          type = 'alert'
          text = I18n.t('subscription_info_toast.premium_subscription_expired')
        end
      elsif (user.is_eligible_for_start_trial? && user.has_premium_layout?) || user.eligible_for_self_trial?
        trial_duration = Metadatum.get_user_trail_duration(user_id: user.id)
        duration = trial_duration.positive? ? trial_duration : Constants.poster_trial_default_duration
        type = 'info'
        text = I18n.t('subscription_info_toast.trial_duration', duration: duration)
      elsif user.is_in_waitlist?
        type = 'quiet'
        text = I18n.t('subscription_info_toast.waitlist_request')
      else
        type = 'info'
        text = I18n.t('subscription_info_toast.join_waitlist')
      end

      {
        feed_type: "subscription_info_toast",
        feed_item_id: "subscription_info_toast",
        type: type,
        label: label,
        text: text
      }.compact
    end
  end

  # usage_counts_feed_item
  def usage_counts_feed_item(user:)
    share_and_download_count = PosterShare.joins(:frame)
                                          .where(user: user)
                                          .where.not(frame_id: nil)
                                          .where(frames: { frame_type: %i[premium status hero_frame_premium family_frame_premium] }).count
    {
      feed_type: "usage_counts",
      feed_item_id: "usage_counts",
      items: [
        {
          count: share_and_download_count,
          text: I18n.t('usage_counts_feed_item.share_and_download_count_text')
        }
      ],
      analytics_params: {
        share_and_download_count: share_and_download_count
      }
    }
  end

  def profile_views_feed_item(user:, deeplink: nil)
    count = 3
    loaded_user_ids = []
    views_data = fetch_views_data(user, loaded_user_ids, count)

    title, views_count = generate_title(user, loaded_user_ids)
    see_more_text, see_more_text_color = generate_see_more_text(views_count, count)
    locked = views_data[:locked]
    {
      feed_type: "profile_views",
      feed_item_id: "profile_views",
      title: title,
      locked: locked,
      see_more_locked: locked,
      deeplink: locked && deeplink.present? ? "#{deeplink}?source=profile_views" : nil,
      viewers: views_data[:users],
      see_more_text: see_more_text,
      see_more_text_color: see_more_text_color,
      analytics_params: {
        views_count: views_count,
        locked: locked,
      }
    }.compact
  end

  def premium_members_feed_item(user:, deeplink: nil)
    premium_users = premium_users_list_for_premium_experience
    # locked should be true if user is not subscribed to premium
    locked = !user.is_poster_subscribed
    {
      feed_type: "premium_members",
      feed_item_id: "premium_members",
      members: premium_users,
      locked: locked,
      deeplink: locked && deeplink.present? ? "#{deeplink}?source=premium_members" : nil,
      title: I18n.t('premium_members_feed_item.title'),
      additional_users_count: 0,
      analytics_params: {
        locked: locked,
        premium_users_count: premium_users.size
      }
    }
  end

  def upcoming_events_feed_item(user:, deeplink: nil)
    locked = !(user.is_poster_subscribed || user.is_trial_user?)
    upcoming_event_creatives = PosterCreative.upcoming_event_creatives(user: user, is_layout_locked: locked)
    return nil if upcoming_event_creatives.blank?

    {
      feed_type: "upcoming_events",
      feed_item_id: "upcoming_events",
      locked: locked,
      deeplink: locked && deeplink.present? ? "#{deeplink}?source=upcoming_events" : nil,
      creatives: upcoming_event_creatives,
      title: I18n.t('upcoming_events_feed_item.title'),
      analytics_params: {
        locked: locked,
        creatives_count: upcoming_event_creatives.size,
      }
    }
  end

  def my_poster_styles_feed_item(user:, deeplink: nil)
    # locked should be true if user is not subscribed to premium or user is not in trial
    locked = !(user.is_poster_subscribed || user.is_trial_user?)
    poster_creative = PosterCreative.find_by(id: Constants.dummy_plain_creative_id)
    circle_id = user.affiliated_party_circle_id

    creative_json = poster_creative.build_creative_json(user: user, posters_tab_v3_enabled: true,
                                                        is_layout_locked: locked,
                                                        is_eligible_for_premium_creatives: true)
    return nil if poster_creative.blank?
    layouts = user.get_user_layouts(circle_id: circle_id, category_kind: poster_creative.creative_kind,
                                    creative_id: poster_creative.id)
    # reject basic type layouts
    layouts = layouts.reject { |layout| layout[:layout_type] == "basic" }
    layouts_count = layouts.size
    {
      feed_type: "my_poster_styles",
      feed_item_id: "my_poster_styles",
      title: I18n.t('my_poster_styles_feed_item.title', count: layouts_count),
      locked: locked,
      creative: creative_json,
      layouts: layouts,
      deeplink: locked && deeplink.present? ? "#{deeplink}?source=my_poster_styles" : nil,
      analytics_params: {
        locked: locked,
        creative_id: poster_creative.id,
        layouts_count: layouts_count,
        circle_id: circle_id
      }
    }
  end

  def premium_experience_media_carousel(user:, user_role: nil, original: false, ignore_title: false, deeplink: nil)
    locked = !(user.is_poster_subscribed || user.is_trial_user?)
    layouts_json = original ? user.demo_layouts_with_user_data(user_role:) : user.demo_layouts_for_premium_experience(user_role:)
    media = generate_media_carousel(layouts_json: layouts_json, user: user, locked: locked)
    return nil if media.blank?

    feed_item_id = original ? "events_media_carousel_for_trial_eligible_user" : "events_media_carousel"
    title = (original || ignore_title) ? "" : I18n.t('premium_experience_media_carousel.title')

    {
      feed_type: "events_media_carousel",
      feed_item_id: feed_item_id,
      title: title,
      locked: locked,
      media: media,
      deeplink: locked && deeplink.present? ? "#{deeplink}?source=events_media_carousel" : nil,
      analytics_params: {
        locked: locked,
        media_count: media.size,
        feed_item_id: feed_item_id,
      }
    }
  end

  def generate_media_carousel(layouts_json:, user:, locked:, event_id: nil)
    media = []
    return nil if layouts_json.blank?

    creatives_count = layouts_json.size
    upcoming_creatives = PosterCreative.upcoming_event_creatives_including_current(
      user: user,
      is_layout_locked: locked,
      event_id: event_id
    ).first(creatives_count)

    if upcoming_creatives.size < creatives_count
      upcoming_creatives += PosterCreative.where(active: true, primary: true)
                                          .last(creatives_count - upcoming_creatives.size).map do |poster_creative|
        poster_creative.build_creative_json(user: user, posters_tab_v3_enabled: true,
                                            is_layout_locked: locked,
                                            is_eligible_for_premium_creatives: true)
      end
    end

    return nil if upcoming_creatives.blank?

    layouts_json.each do |layout|
      media << {
        type: "poster",
        creative: upcoming_creatives.shift,
        layout: layout
      }
    end

    media << { type: "image", url: Constants.events_count_image_url }
    media << { type: "image", url: Constants.unlimited_downloads_image_url }

    media
  end

  def relation_manager_feed_item(user:)
    rm_user = user.rm_user_json
    return nil if rm_user.blank?
    # Check for missing required fields
    if rm_user[:name].blank? || rm_user[:phone].blank?
      Honeybadger.notify(
        "Missing required fields for relation manager",
        context: {
          user_id: user.id,
          relation_manager_id: rm_user[:id],
          name: rm_user[:name],
          photo_url: rm_user[:photo_url],
          phone: rm_user[:phone]
        }
      )
      return nil
    end

    {
      feed_type: "relation_manager",
      feed_item_id: "relation_manager",
      title: I18n.t('rm_feed_item.title'),
      rm_name_text_color: 0xff8F8F8F,
      rm_user: rm_user,
      analytics_params: {
        rm_user_id: rm_user[:id],
        rm_user_name: rm_user[:name],
        rm_user_phone: rm_user[:phone],
        rm_user_email: rm_user[:email]
      }
    }
  end

  def rm_user_json
    relation_manager = self.get_rm_user
    if relation_manager.present?
      {
        id: relation_manager.id,
        name: relation_manager.name,
        photo_url: relation_manager.admin_medium&.placeholder_url || Constants.default_rm_user_image_url,
        phone: relation_manager.phone,
        whatsapp_link: "https://api.whatsapp.com/send/?phone=+91#{relation_manager.phone}",
        email: relation_manager.email
      }
    else
      {
        id: 0,
        name: "కస్టమర్ కేర్",
        photo_url: Constants.default_rm_user_image_url,
        phone: Constants.praja_customer_care_phone_number,
        whatsapp_link: "https://api.whatsapp.com/send/?phone=+91#{Constants.praja_customer_care_phone_number}",
        email: Constants.praja_customer_care_email
      }
    end
  end

  def json_for_premium_experience(following_user_ids:)
    {
      id: id,
      name: name,
      badge: get_badge_role&.get_json,
      photo: photo,
      follows: following_user_ids.include?(id),
      avatar_color: avatar_color
    }
  end

  def get_cancellation_deeplink(subscription:, source:)
    return "/cancel-membership?source=#{source}" unless AppVersionSupport.cancellation_flow_v2_supported?
    return "/cancel-membership?source=#{source}" unless has_premium_layout?
    if subscription.plan.annual?
      subscription.subscription_charges.where("charge_amount > 1").where(status: :success).present? ? "/cancel-membership?source=#{source}" : "/cancel-flow-downgrade-sheet?source=#{source}"
    else
      "/premium-benefits-loss-screen?source=#{source}"
    end
  end

  def faqs_feed_item(user:)
    cancellable_latest_subscription = user.cancellable_latest_subscription
    cancel_button_present = user.is_poster_subscribed && cancellable_latest_subscription&.may_cancel?
    faqs = (1..10).map do |i|
      question = I18n.t("faqs_feed_item.question_#{i}")
      answer = I18n.t("faqs_feed_item.answer_#{i}")

      if i == 10 && cancel_button_present
        button_text = I18n.t('faqs_feed_item.button_text_10')
        button_deeplink = get_cancellation_deeplink(subscription: cancellable_latest_subscription, source: "faqs")
        button = { text: button_text, deeplink: button_deeplink }
      else
        button_text = nil
        button_deeplink = nil
        button = nil
      end
      {
        question: question,
        answer: answer,
        button: button,
        analytics_params: {
          question: question,
          has_button: button != nil,
          button_text: button_text,
          button_deeplink: button_deeplink
        }.compact
      }
    end

    {
      feed_type: "faqs",
      feed_item_id: "faqs",
      title: I18n.t('faqs_feed_item.title'),
      faqs: faqs,
      analytics_params: {
        faqs_count: faqs.size,
      }
    }
  end

  ActiveRecord::Base.connected_to(role: :reading) do
    def not_yet_followed_signed_up_user_ids_of_user_contacts
      # select user id,user name and order by grade level exclude already followed users
      user_contacts = UserContactSuggestion.where(user_id: self.id)
                                           .joins("LEFT JOIN user_roles ON user_roles.user_id = user_contact_suggestions.phone_user_id
                                            and user_roles.primary_role = 1 and user_roles.active = 1")
                                           .joins('LEFT JOIN roles ON roles.id = user_roles.role_id and roles.active = 1')
                                           .joins("LEFT JOIN user_followers ON
                                            user_followers.user_id = user_contact_suggestions.phone_user_id
                                            and user_followers.follower_id = user_contact_suggestions.user_id")
                                           .joins(:phone_user)
                                           .merge(User.active)
                                           .where(user_followers: { id: nil })
                                           .select("user_contact_suggestions.phone_user_id,user_contact_suggestions.name,
                                           IF(user_roles.grade_level IS NULL, IF(roles.grade_level IS NULL, 1000, roles.grade_level),
                                           user_roles.grade_level)
    AS derived_grade_level").order('derived_grade_level ASC, user_contact_suggestions.id DESC')

      user_ids = []
      # filter out users whose names have un_subscribed_words
      un_subscribed_words = UnSubscribedWord.pluck(:word).join('|')

      user_contacts.each do |user_contact|
        name = user_contact.name.downcase
        has_un_subscribed_word = name.scan(/\b(?:#{un_subscribed_words})\b/i) if un_subscribed_words.present?
        user_ids << user_contact.phone_user_id if has_un_subscribed_word.blank?
      end
      user_ids
    end
  end

  # to get the user ids of the contacts of the user
  def premium_user_ids_of_user_contacts(duration_in_months: nil)
    user_plans_query = UserPlan.where("user_plans.end_date >= ?", Time.zone.now)
    user_plans_query = user_plans_query.where(plan_id: Plan.where(duration_in_months: 12).select(:id)) if duration_in_months.present?

    user_ids = UserContactSuggestion.where(user_id: self.id).joins(:phone_user)
                                    .joins("INNER JOIN (#{user_plans_query.to_sql}) AS user_plans ON user_plans.user_id = user_contact_suggestions.phone_user_id")
                                    .merge(User.active)
                                    .distinct.pluck(:phone_user_id)

    # sort user ids based on their grade level where grade level is on user role and role
    User.where(id: user_ids)
        .joins("LEFT JOIN user_roles ON user_roles.user_id = users.id AND user_roles.primary_role = 1 AND user_roles.active = 1 AND user_roles.verification_status = 'verified'")
        .joins('LEFT JOIN roles ON roles.id = user_roles.role_id')
        .order(Arel.sql('IF(user_roles.grade_level IS NULL, IF(roles.grade_level IS NULL, 1000, roles.grade_level), user_roles.grade_level) ASC'))
        .distinct.pluck(:id)
  end

  # need to change the logic from user product subscriptions to user plans accordingly
  def premium_users_list_for_premium_experience(duration_in_months: nil)
    contact_premium_user_ids = premium_user_ids_of_user_contacts(duration_in_months:)
    count = 20
    district_premium_user_ids = []

    if contact_premium_user_ids.size < count
      user_plans_query = UserPlan.where("user_plans.end_date >= ?", Time.zone.now)
      user_plans_query = user_plans_query.where(plan_id: Plan.where(duration_in_months: duration_in_months).select(:id)) if duration_in_months.present?

      district_premium_user_ids = User.joins("INNER JOIN (#{user_plans_query.to_sql}) AS user_plans ON user_plans.user_id = users.id")
                                      .joins("LEFT JOIN user_roles ON user_roles.user_id = users.id AND user_roles.primary_role = 1 AND user_roles.active = 1 AND user_roles.verification_status = 'verified'")
                                      .joins("LEFT JOIN roles ON roles.id = user_roles.role_id AND roles.active = 1")
                                      .where(users: { district_id: self.district_id })
                                      .where.not(id: contact_premium_user_ids + [self.id])
                                      .limit(count - contact_premium_user_ids.size)
                                      .order(Arel.sql('IF(user_roles.grade_level IS NULL, IF(roles.grade_level IS NULL, 1000, roles.grade_level), user_roles.grade_level) ASC'))
                                      .distinct.pluck(:id)
    end

    user_ids = contact_premium_user_ids + district_premium_user_ids
    following_user_ids = UserFollower.where(user_id: user_ids, follower_id: id).pluck(:user_id)
    User.where(id: user_ids.first(count)).order(Arel.sql("FIELD(id, #{user_ids.join(',')})")).map do |u|
      u.json_for_premium_experience(following_user_ids:)
    end
  end

  def waiting_list_number
    wait_list_id = PremiumPitch.where(status: %i[wait_list interested])
                               .order(id: :asc)
                               .pluck(:user_id)
                               .index(id)
    wait_list_id.present? ? wait_list_id + 1 : 1
  end

  def terms_json
    {
      before_link_text: I18n.t('old_terms.before_link_text'),
      link_text: I18n.t('old_terms.link_text'),
      link_url: 'https://m.praja.buzz/purchaser-terms-of-service',
      after_link_text: I18n.t('old_terms.after_link_text'),
    }
  end

  # this is new terms and conditions for the user who is in premium experience
  def terms_json_v1
    {
      before_link_text: I18n.t('auto_pay_terms.before_link_text'),
      link_text: I18n.t('auto_pay_terms.link_text'),
      link_url: 'https://m.praja.buzz/purchaser-terms-of-service',
      after_link_text: I18n.t('auto_pay_terms.after_link_text'),
    }
  end

  def show_trial_start?
    UserMetadatum.exists?(user_id: id, key: Constants.yet_to_start_trial_key)
  end

  # using this key to show the popup of recharge paywall
  def set_user_metadatum(key)
    # create the key if key already not exists
    # if exists then it will raise an error
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: key)
    if metadatum.new_record?
      metadatum.value = 0 if key == Constants.trial_activation_wati_campaign_count
      metadatum.save!
    else
      Honeybadger.notify("UserMetadatum is not a new record", context: { user_id: id })
    end
  end

  def delete_yet_to_start_trial_key
    UserMetadatum.find_by(user_id: id, key: Constants.yet_to_start_trial_key)&.destroy
  end

  def default_plan
    plan = Plan.find_by(id: UserMetadatum.find_by(user_id: id, key: Constants.default_plan_key)&.value)
    return plan if plan.present?

    plans = Plan.get_premium_plans(user: self)
    should_pitch_yearly_package? ? plans.find(&:annual?) : plans.find(&:monthly?)
  end

  def set_default_plan(plan_id)
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: Constants.default_plan_key)
    metadatum.value = plan_id
    metadatum.save!
  end

  def get_rm_user
    rm_user_id = UserMetadatum.find_by(user_id: id, key: Constants.rm_user_id_key)&.value.to_i
    AdminUser.find_by(id: rm_user_id) if rm_user_id.present?
  end

  def get_oe_user
    oe_user_id = UserMetadatum.find_by(user_id: id, key: Constants.oe_user_id_key)&.value.to_i
    AdminUser.find_by(id: oe_user_id) if oe_user_id.present?
  end

  def get_boe_user
    boe_user_id = UserMetadatum.find_by(user_id: id, key: Constants.boe_user_id_key)&.value.to_i
    AdminUser.find_by(id: boe_user_id) if boe_user_id.present?
  end

  def save_rm_user(rm_user_id:)
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: Constants.rm_user_id_key)
    metadatum.value = rm_user_id
    metadatum.save!

    SyncMixpanelUser.perform_async(id)
  end

  def save_oe_user(oe_user_id:)
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: Constants.oe_user_id_key)
    metadatum.value = oe_user_id
    metadatum.save!

    SyncMixpanelUser.perform_async(id)
  end

  def save_boe_user(boe_user_id:)
    metadatum = UserMetadatum.find_or_initialize_by(user_id: id, key: Constants.boe_user_id_key)
    metadatum.value = boe_user_id
    metadatum.save!

    SyncMixpanelUser.perform_async(id)
  end

  def get_paywall_deeplink(source: nil)
    if AppVersionSupport.single_step_paywall_enabled?
      deeplink = Constants.single_step_paywall_deeplink
      deeplink += "?source=#{source}" if source.present?
      return deeplink
    end

    # Use annual paywall only if should_pitch_yearly_package? returns true
    deeplink = AppVersionSupport.is_annual_recharge_paywall_supported? && should_pitch_yearly_package? ? Constants.annual_paywall_deeplink : Constants.paywall_deeplink
    deeplink += "?source=#{source}" if source.present?
    deeplink
  end

  # either trial or paid users are eligible for the premium features
  def eligible_for_premium_features?
    is_poster_subscribed || is_trial_user?
  end

  def active_subscription
    Subscription.where(user_id: id).active.last
  end

  def on_hold_subscription
    Subscription.where(user_id: id).on_hold.last
  end

  def annual_premium_user_with_successful_payment?(current_subscription: nil)
    current_active_subscription = current_subscription || active_subscription
    return false if current_active_subscription.blank?
    current_active_subscription.plan.annual? && current_active_subscription.subscription_charges.where("charge_amount > 1").where(status: :success).present?
  end

  def get_party_specific_mla_for_user(affiliated_party_circle_id)
    return if affiliated_party_circle_id.blank?
    mla_relation = CirclesRelation.joins("INNER JOIN circles_relations as cr2 ON circles_relations.second_circle_id = cr2.first_circle_id")
                                  .where(circles_relations: { relation: [:MLA, :MLA_Contestant], active: true, first_circle_id: mla_constituency_id })
                                  .where(cr2: { relation: :Leader2Party, second_circle_id: affiliated_party_circle_id }).last
    mla_relation&.second_circle_id
  end

  def get_party_specific_mp_for_user(affiliated_party_circle_id)
    return if affiliated_party_circle_id.blank?
    mp_relation = CirclesRelation.joins("INNER JOIN circles_relations as cr2 ON circles_relations.second_circle_id = cr2.first_circle_id")
                                 .where(circles_relations: { relation: [:MP, :MP_Contestant], active: true, first_circle_id: mp_constituency_id })
                                 .where(cr2: { relation: :Leader2Party, second_circle_id: affiliated_party_circle_id }).last
    mp_relation&.second_circle_id
  end

  def get_default_h1_and_h2_leader_circle_ids(affiliated_party_circle_id)
    # Get MLA and MP circle IDs specific to the affiliated party
    mla_circle_id = get_party_specific_mla_for_user(affiliated_party_circle_id)
    mp_circle_id = get_party_specific_mp_for_user(affiliated_party_circle_id)

    h1_circle_ids = [mla_circle_id].compact

    leader_circle_ids = Circle.get_default_leader_circle_ids_for_party(affiliated_party_circle_id)
    leader_circle_ids = leader_circle_ids.present? ? (leader_circle_ids + [mp_circle_id]) : [mp_circle_id]
    h2_circle_ids = leader_circle_ids - h1_circle_ids

    h1_circle_ids = h1_circle_ids.uniq.compact
    h2_circle_ids = h2_circle_ids.uniq.compact

    # Determine h2 photos max count based on h1 photos availability
    h2_photos_max_count = h1_circle_ids.count == 1 ? 6 : 10
    [h1_circle_ids, h2_circle_ids.first(h2_photos_max_count)]
  end

  # Generic method to check if user is eligible for plan extension
  # @return [Array<Boolean, String>] A tuple of [is_eligible, error_message]
  def eligible_for_user_plan_extension_in_cancellation_flow?
    current_active_subscription = active_subscription
    if current_active_subscription.blank?
      [false, I18n.t('plan_extension.not_eligible_for_extension')]
    elsif current_active_subscription.user_plan.blank?
      [false, I18n.t('plan_extension.not_eligible_for_extension')]
    elsif annual_premium_user_with_successful_payment?(current_subscription: current_active_subscription)
      [false, I18n.t('plan_extension.not_eligible_for_extension')]
      # Check if user has already used extension with cancellation_flow source in the last one year
    elsif used_extension_with_cancellation_flow_source?
      [false, I18n.t('plan_extension.already_used_extension')]
      # Check if user is in subscription extension
    elsif in_subscription_extension?
      [false, I18n.t('plan_extension.already_in_extension')]
    else
      [true, nil]
    end
  end

  # Check if the user has used a plan extension with cancellation_flow source within the last year
  # @return [Boolean] true if such a record exists, false otherwise
  def used_extension_with_cancellation_flow_source?
    one_year_ago = Time.zone.now.advance(years: -1)
    UserPlanLog.joins("LEFT JOIN user_plan_extensions ON user_plan_extensions.id = user_plan_logs.entity_id")
               .where(user_id: id)
               .where(entity_type: 'UserPlanExtension')
               .where('user_plan_logs.start_date >= ?', one_year_ago)
               .where("user_plan_extensions.reason = 'cancellation_flow'")
               .exists?
  end

  # Check if user is currently in a subscription extension period
  # @return [Boolean] Whether the user is currently in an extension period
  def in_subscription_extension?
    UserPlanLog.where(user_id: id)
               .where(entity_type: 'UserPlanExtension')
               .where(active: true)
               .where('end_date > ?', Time.zone.now)
               .exists?
  end

  def cancellable_latest_subscription
    Subscription.where(user_id: id).cancellable.last
  end

  def eligible_for_downgrade_in_cancellation_flow?
    current_user_plan = get_active_user_plan
    return false if current_user_plan.blank?
    current_user_plan.plan.annual? && is_poster_subscribed
  end

  def show_downgrade_consent_sheet?
    return false unless AppVersionSupport.supports_downgrade_sheet?
    (active_subscription || on_hold_subscription)&.need_to_get_consent_for_downgrade? || false
  end

  def show_offer_amount_upgrade_sheet?
    return false unless app_version_supports_upgrade_package_sheet?
    campaign = user_eligible_1_year_campaign
    return false if campaign.blank?
    return false if has_seen_offer_campaign_already?(campaign_id: campaign.id)
    true
  end

  def show_upgrade_package_sheet?
    return false unless app_version_supports_upgrade_package_sheet?
    return false if active_subscription.blank?
    return false unless SubscriptionUtils.has_user_subscribed?(id, allow_grace_period: false)
    return false unless upgrade_package_sheet_metadata_to_be_shown?
    true
  end

  def show_upgrade_package_nudge?
    return false unless app_version_supports_upgrade_package_sheet?
    return false unless should_show_upgrade_package_nudge?
    return false unless common_upgrade_package_conditions_met?
    true
  end

  def show_upgrade_package_feed_item_in_posters_feed?
    return false unless AppVersionSupport.supports_upgrade_feed_item_in_posters_feed?
    return false unless common_upgrade_package_conditions_met?
    true
  end

  def show_upgrade_package_using_offer_feed_item_in_posters_feed?
    return false unless AppVersionSupport.supports_upgrade_feed_item_in_posters_feed?
    return false unless upgrade_package_using_offer_conditions_met?
    true
  end

  def app_version_supports_upgrade_package_sheet?
    AppVersionSupport.supports_upgrade_package_sheet?
  end

  def cancel_subscription(subscription_id, reason)
    subscription = Subscription.find_by_id(subscription_id)
    subscription.cancel(reason)
  end

  def active_premium_plan
    plan = UserPlan.where(user_id: id).where("end_date >= ?", Time.zone.now).last.try(:plan)
    if plan.blank?
      grace_period_given_date = Metadatum.where(entity_type: "User", entity_id: id, key: Constants.grace_period_given_string).last&.value
      if grace_period_given_date.present?
        time_frame_to_check_plan_in_grace_period = Time.parse(grace_period_given_date) - 1.day
        plan = UserPlan.where(user_id: id).where("end_date >= ?", time_frame_to_check_plan_in_grace_period).last.try(:plan)
      end
    end
    plan
  end

  def get_active_user_plan
    UserPlan.where(user_id: id).where("end_date >= ?", Time.zone.now).last
  end

  def is_in_waitlist?
    PremiumPitch.where(user_id: id, status: %i[wait_list interested]).exists?
  end

  def last_subscription
    Subscription.where(user_id: id).last
  end

  def has_user_frames?
    UserFrame.where(user_id: id).exists?
  end

  def cashfree_supported_name?
    pattern = /^[a-zA-Z0-9. ]+$/
    name.match?(pattern)
  end

  # if record is created in last two days return true else false
  def is_eligible_for_basic_frames_at_last?
    return false unless has_premium_layout?
    return false if SubscriptionUtils.has_user_ever_subscribed?(self.id)

    duration_record = Metadatum.where(entity: self, key: Constants.user_poster_trial_duration_key).last
    return false if duration_record.blank?
    # created in last two days or not
    duration_record.created_at >= 2.days.ago
  end

  def viewed_poster_feed_doc_ids
    # Fetch all entries from the poster creative views queue and video creatives views queue
    photo_creatives_redis_entries = $redis.smembers(Constants.poster_creative_views_queue_redis_key)
    video_creatives_redis_entries = $redis.smembers(Constants.video_creative_views_queue_redis_key)

    poster_viewed_ids = photo_creatives_redis_entries.map do |entry|
      data = JSON.parse(entry)
      if data["user_id"] == id
        data["poster_creative_id"]
      end
    end.compact

    video_viewed_ids = video_creatives_redis_entries.map do |entry|
      data = JSON.parse(entry)
      if data['user_id'] == id
        data["video_creative_id"]
      end
    end.compact

    # fetch last 500 viewed creative ids from the poster creative views and video creative views
    # force index need to use to avoid scanning just on user_id
    # use index index_poster_creative_views_on_user_id_and_id_desc and index_video_creative_views_on_user_id_and_id_desc
    poster_viewed_ids += PosterCreativeView
                           .from('poster_creative_views FORCE INDEX (index_poster_creative_views_on_user_id_and_id_desc)')
                           .where(user_id: id).order(id: :desc).limit(500).pluck(:poster_creative_id)

    video_viewed_ids += VideoCreativeView.from('video_creative_views FORCE INDEX (index_video_creative_views_on_user_id_and_id_desc)')
                                         .where(user_id: id).order(id: :desc).limit(500).pluck(:video_creative_id)

    poster_viewed_ids = poster_viewed_ids.uniq
    video_viewed_ids = video_viewed_ids.uniq

    doc_ids = []

    creatives_with_event_ids = []
    event_ids = []
    PosterCreative.where(id: poster_viewed_ids).where.not(event_id: nil).pluck(:id, :event_id).each do |cid, eid|
      creatives_with_event_ids << cid
      event_ids << eid
    end
    creatives_without_event = poster_viewed_ids - creatives_with_event_ids
    doc_ids += event_ids.map { |eid| "event_#{eid}" }
    doc_ids += creatives_without_event.map { |id| "creative_#{id}" }
    doc_ids += video_viewed_ids.map { |vid| "video_creative_#{vid}" }

    doc_ids.uniq
  end

  def self_trial_nudge_seen_count
    $redis.get(Constants.self_trial_nudge_seen_count_key(self.id)).to_i
  end

  def increment_self_trial_nudge_seen_count
    key = Constants.self_trial_nudge_seen_count_key(self.id)
    $redis.incr(key)
    $redis.expire(key, 90.days.to_i)
  end

  def self_trial_nudge_seen_count_within_limit?
    self_trial_nudge_seen_count < Constants.max_no_of_times_to_show_self_trial_nudge_for_user
  end

  def deeplink_url_in_poster_share(share_method, creative_id:, format_type: "image")
    if eligible_for_self_trial?
      redis_key = Constants.poster_share_deeplink_redis_key(id)
      current_date = Time.zone.today.to_s

      # Get all dates from the Redis set
      sent_dates = $redis.smembers(redis_key)

      if sent_dates.size < 2 && sent_dates.exclude?(current_date)

        # Add the current date to the Redis set
        $redis.sadd?(redis_key, current_date)
        $redis.expire(redis_key, 90.days.to_i)

        # Ensure the deeplink is sent a maximum of 2 times in 90 days, on different days
        # Send the deeplink URL
        return "/premium-experience?source=#{share_method}"
      end
    elsif app_version_supports_upgrade_package_sheet? && common_upgrade_package_conditions_met? && format_type == 'image'
      event = PosterCreative.find_by(id: creative_id)&.event

      # skip if event is blank or not high priority event
      return nil if event.blank? || !event.high_priority?

      redis_key = Constants.upgrade_deeplink_in_poster_share_redis_key(id)
      current_date = Time.zone.today.to_s
      # Get all dates from the Redis set
      sent_dates = $redis.smembers(redis_key)
      if sent_dates.size < 3 && sent_dates.exclude?(current_date)
        # Add the current date to the Redis set
        # Ensure the deeplink is sent a maximum of 3 times in 30 days, on different days
        # Send the deeplink URL
        $redis.sadd?(redis_key, current_date)
        $redis.expire(redis_key, 30.days.to_i)

        return "/upgrade?source=poster-#{share_method}"
      end
    elsif app_version_supports_upgrade_package_sheet? && common_upgrade_package_conditions_met? && format_type == 'video'
      # TODO: Need to change the video poster share logic
      return nil unless is_test_user_for_floww?

      event = VideoCreative.find_by(id: creative_id)&.event

      # skip if event is blank or not high priority event
      return nil if event.blank? || !event.high_priority?

      redis_key = Constants.video_poster_share_deeplink_redis_key(id)
      current_date = Time.zone.today.to_s
      # Get all dates from the Redis set
      sent_dates = $redis.smembers(redis_key)
      if sent_dates.size < 3 && sent_dates.exclude?(current_date)
        # Add the current date to the Redis set
        # Ensure the deeplink is sent a maximum of 3 times in 30 days, on different days
        # Send the deeplink URL
        $redis.sadd?(redis_key, current_date)
        $redis.expire(redis_key, 30.days.to_i)

        return "/premium-experience"
      end
    end
  end

  def signed_up_today?
    # store it in redis for exp of 24 hours
    key = Constants.signed_up_today_key(id)
    $redis.get(key).present?
  end

  def set_signed_up_today
    key = Constants.signed_up_today_key(id)
    $redis.set(key, true)
    $redis.expire(key, 1.day.to_i)
  end

  def seen_premium_experience_popup?
    key = Constants.seen_premium_experience_popup_key(id)
    $redis.get(key).present?
  end

  def set_seen_premium_experience_popup?
    key = Constants.seen_premium_experience_popup_key(id)
    $redis.set(key, true)
    $redis.expire(key, 90.days.to_i)
  end

  def self_trial_user?
    first_successful_recharge = get_first_successful_recharge
    return false unless first_successful_recharge

    # get layout created_at
    layout = get_user_poster_layout
    return true if layout.blank?

    # check if layout created after the first successful recharge
    layout.created_at > first_successful_recharge.success_at
  end

  def is_yearly_self_trial_user?
    first_successful_recharge = get_first_successful_recharge
    return false unless first_successful_recharge

    layout = get_user_poster_layout
    return false if layout.present? && layout.created_at <= first_successful_recharge.success_at

    subscription = first_successful_recharge.subscription
    subscription.present? && subscription.plan.duration_in_months == 12
  end

  def get_first_successful_recharge
    # get the updated_at of first successful recharge which is trial charge
    SubscriptionCharge.where(user_id: id, charge_amount: 1).success_ever.first
  end

  def get_user_trial_start_date
    Metadatum.where(entity: self, key: Constants.user_poster_trial_start_date_key).last&.value&.to_date
  end

  def user_last_session_time
    UserTokenUsage.where(user_id: id).last&.created_at
  end

  def user_last_trial_setup_attempt_time
    SubscriptionCharge.where(user_id: id, charge_amount: 1).last&.created_at
  end

  def calculate_lead_score
    score = 0
    score += leader_profession? ? 4 : 1

    if self_trial_user?
      self_trial_score = is_yearly_self_trial_user? ? 25 : 20
      trial_start_date = get_user_trial_start_date
      if trial_start_date.present?
        days_diff = (Time.zone.now.to_date - trial_start_date).to_i
        score += days_diff > 10 ? 5 : (self_trial_score - days_diff)
      end
    end

    score += has_badge_role? ? 4 : 1
    score += affiliated_party_circle_id.present? ? 2 : 1
    score += has_premium_layout? ? 5 : 1
    score += should_pitch_yearly_package? ? 3 : 1

    poster_share_count_for_floww = PosterShare.where(user_id: id).limit(25).count
    # If P.S > 20 then 4 P.S >5 then 2 Else: 1
    score += if poster_share_count_for_floww > 20
               4
             elsif poster_share_count_for_floww > 5
               2
             else
               1
             end

    last_session_time = user_last_session_time
    # If < 1hr then 3, < 7 days then 2, > 7 days then 1
    if last_session_time.present?
      score += if Time.zone.now.to_i - last_session_time.to_i < 1.hour.to_i
                 3
               elsif Time.zone.now.to_i - last_session_time.to_i < 7.days.to_i
                 2
               else
                 1
               end
    end

    # If < 1hr then 4, < 7 days then 3, > 7 days then 2
    last_trial_setup_attempt_time = user_last_trial_setup_attempt_time
    if last_trial_setup_attempt_time.present?
      score += if Time.zone.now.to_i - last_trial_setup_attempt_time.to_i < 1.hour.to_i
                 4
               elsif Time.zone.now.to_i - last_trial_setup_attempt_time.to_i < 7.days.to_i
                 3
               else
                 2
               end
    end

    badge_network_density = UserContactSuggestion
                              .where(user_id: id)
                              .joins("INNER JOIN user_roles ON user_roles.user_id =
                                                                         user_contact_suggestions.phone_user_id
                                              and user_roles.primary_role = 1 and user_roles.active = 1").count
    # if BND > 5 then 2, else 1
    score + if badge_network_density > 5
              2
            else
              1
            end
  end

  def set_send_wati_msg_key
    key = Constants.send_wati_msg_key
    UserMetadatum.create!(user_id: id, key: key, value: 'to_be_sent')
  end

  def send_wati_msg_on_layout_creation
    hash_variables_list = [
      'number' => phone.to_s,
      'name' => name
    ]

    # send wati msg on layout creation
    WatiIntegration.perform_async('free_premium_posters', hash_variables_list)
  end

  def get_plan_amount_based_on_duration(plan:, duration:)
    case duration
    when 1
      return 1 if eligible_for_1_rs_campaign?
      return 29 if eligible_for_29_rs_campaign?
      return 59 if eligible_for_59_rs_campaign?
    when 12
      return 1799 if eligible_for_half_price_discount?
      campaign = user_eligible_1_year_campaign
      return get_amount_after_campaign_offer(campaign: campaign, user_cohort: user_cohort, plan: plan) if campaign.present?
    else
      return plan.amount
    end
    plan.amount
  end

  def remove_special_offer_for_user
    # remove user id from redis set if present to avoid showing special offer again and again
    $redis.zrem(Constants.premium_1_rs_user_redis_key, id)
    $redis.zrem(Constants.premium_29_rs_campaign_redis_key, id)
    $redis.zrem(Constants.premium_59_rs_campaign_redis_key, id)
    $redis.zrem(Constants.premium_half_price_campaign_redis_key, id)
    $redis.zrem(Constants.monthly_campaign_for_yearly_users_key, id)
  end

  def should_pitch_yearly_package?
    # Introduced from Single Step Paywall,
    # We will be showing single plan for Self Trial Users (even though user satisfies yearly pitch criteria)
    # and existing flow for Sales led users
    # Self Trial Condition
    return false if (!SubscriptionUtils.has_user_ever_subscribed?(self.id) && !has_premium_layout?)
    # Grade 3 users are eligible for yearly packages regardless of device type
    return true if has_grade_3_user_role?
    # Check poster photo total score
    return true if poster_photo_total_score > 12
    # Other users need both eligible source/profession AND high-end device
    (premium_pitch&.yearly_pitch_eligible_source? || leader_profession?) && high_end_device_user?
  end

  # get the poster photo total score for the user that is calculated using AI
  def poster_photo_total_score
    UserMetadatum.find_by(user_id: id, key: Constants.poster_photo_total_score_key)&.value&.to_i || 0
  end

  def has_grade_3_user_role?
    user_roles_including_unverified.any? { |user_role| user_role.get_grade_level == "grade_3" }
  end

  def high_end_device_user?
    latest_device&.price.to_i >= 49900
  end

  # Get the referrer_id for the current user if it exists
  def get_user_referrer_id
    UserReferral.find_by(referred_user_id: id)&.user_id
  end

  def update_user_stage_in_premium_pitch(stage:)
    pitch = self.premium_pitch
    if pitch.blank?
      Honeybadger.notify("Premium pitch not found for user", context: { user_id: id })
      return
    end
    pitch.update!(crm_stage: stage)
  end

  def is_ivr_experiment_user?
    $redis.sismember(Constants.charge_ivr_experiment_redis_key, id)
  end

  def get_user_creative_data(campaign_name)
    upl_first_activated_at = get_upl_first_activated_date
    return nil unless upl_first_activated_at

    upl_age = (Time.zone.now - upl_first_activated_at) / 1.day
    only_high_priority = upl_age > 2
    order_by_priority = upl_age <= 2

    high_priority_event = Event.upcoming_events_including_current(user: self, only_high_priority: only_high_priority, order_by_priority: order_by_priority)
                               .first
    return nil unless high_priority_event

    high_priority_event_creative = high_priority_event.get_creative_for_wati_campaign
    return nil unless high_priority_event_creative

    UserMetadatum.new(user_id: self.id,
                      key: campaign_name + "_creative_id",
                      value: high_priority_event_creative.id)
  end

  def get_upl_first_activated_date
    upl = UserPosterLayout.where(entity: self).order(id: :desc).first
    upl&.first_activated_at
  end

  def today_most_shared_poster_related_to_user_circles
    key = "poster_creative_shares_counts_#{Time.zone.today.strftime('%d%m%Y')}"
    today_poster_creatives = $redis.zrevrange(key, 0, -1, with_scores: true)
    return nil if today_poster_creatives.empty?

    today_poster_creative_ids = today_poster_creatives.map { |id, _| id.to_i }

    valid_ids = PosterCreative
                  .joins(:poster_creative_circles)
                  .where(id: today_poster_creative_ids, active: true)
                  .where(poster_creative_circles: { circle_id: self.affiliated_circle_ids_for_posters_tab })
                  .where('start_time <= ? AND end_time > ?', Time.zone.now, Time.zone.now)
                  .pluck(:id)

    today_poster_creative_ids.find { |id| valid_ids.include?(id) }
  end

  def toggle_default_plan!
    current = default_plan
    new_plan = Plan.get_premium_plans(user: self).find do |p|
      current.annual? ? p.monthly? : p.annual?
    end
    set_default_plan(new_plan.id) if new_plan
    new_plan
  end
end
