# frozen_string_literal: true

# Which popup to show on app open
module Popup
  extend ActiveSupport::Concern

  def get_popup_path
    if should_show_trial_start_popup? || should_show_premium_experience_popup_for_self_trial_eligible?
      # Note: whenever we are changing the path for should_show_trial_start_popup? then we should be cautious about
      # logic of deleting the `yet_to_start_trial_key` from the user metadata
      if AppVersionSupport.single_step_paywall_enabled?
        return Constants.single_step_paywall_deeplink + "?source=app_open_popup"
      end
      return '/premium-experience?source=app_open_popup'
    end
    return '/offer-reveal-sheet' if @user.show_offer_amount_upgrade_sheet?
    return '/upgrade' if @user.show_upgrade_package_sheet?
    return '/downgrade-sheet' if @user.show_downgrade_consent_sheet?
    return '/professions?source=app_open_popup' if should_show_profession_popup?
    return '/fan-requests?source=app_open_popup' if @user.show_fan_poster_request_prompt?
    return '/contacts?source=app_open_popup' if @user.should_show_follow_contacts
  end

  private

  # it's not required for now, we are going to use it for only one time payments
  def should_show_trial_start_popup?
    return false unless AppVersionSupport.supports_autopay?
    return false unless @user.show_trial_start?

    true
  end

  def should_show_premium_experience_popup_for_self_trial_eligible?
    return false if @user.seen_premium_experience_popup?
    return false unless @user.eligible_for_self_trial?
    # Show Single Step Paywall on app open from first session
    # @note: We will be showing Single Step Paywall for 25% of users in First Session itself (Experiment)
    return false unless UserTokenUsage.where(user: @user).count >= 2 || @user.is_single_step_paywall_experiment_user?

    @user.set_seen_premium_experience_popup?
    true
  end

  def should_show_profession_popup?
    return false unless AppVersionSupport.supports_profession_selection?
    return false if @user.has_badge_role?
    return false if UserProfession.find_by(user: @user).present?
    return false if UserMetadatum.find_by(user: @user, key: Constants.profession_selection_skipped_key).present?

    true
  end
end
