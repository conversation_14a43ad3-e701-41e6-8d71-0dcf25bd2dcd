module MediaServiceUpload
  extend ActiveSupport::Concern

  def upload_to_media_service(form_data, user_id, type_of_media)
    url = if type_of_media == "photos"
            Constants.get_user_post_media_upload_urls[:photo]
          elsif type_of_media == "video"
            Constants.get_user_post_media_upload_urls[:video]
          end

    url = URI(url)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true

    request = Net::HTTP::Post.new(url)

    #TODO: Need to change the user ID for token to AdminUser ID

    token = JsonWebToken.encode({ user_id: Constants.praja_account_user_id, created_at: Time.zone.now.to_i })
    request["Authorization"] = "Bearer #{token}"
    request["X-Extract-Metadata"] = true
    request["X-Media-Expire"] = '-1'
    request.set_form form_data, 'multipart/form-data'
    response = https.request(request)

    JSON.parse(response.body) if response.code == "201"
  end

end
