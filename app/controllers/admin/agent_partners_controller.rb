class Admin::AgentPartnersController < Admin::AdminController
  include PhoneNormalizer
  
  before_action :ensure_agent_partner_role
  skip_before_action :verify_authenticity_token

  # POST /admin/agent-partners/start-lead-process
  def start_lead_process
    phone_number = params[:phone]
    
    # Validate phone number
    if phone_number.blank?
      render json: { error: 'Phone number is required' }, status: :bad_request
      return
    end

    # Normalize phone number (remove any spaces, country codes if present)
    normalized_phone = normalize_phone_number(phone_number)
    
    if normalized_phone.blank?
      render json: { error: 'Invalid phone number format' }, status: :bad_request
      return
    end

    # Look up or create user
    user = User.find_by(phone: normalized_phone)
    
    if user.nil?
      # Create new user with minimal required fields
      user = User.create!(
        phone: normalized_phone,
        status: :pre_signup
      )
    end

    # Check for existing PremiumPitch
    premium_pitch = PremiumPitch.find_by(user: user)
    
    if premium_pitch.nil?
      # Create new PremiumPitch
      premium_pitch = PremiumPitch.new(
        user: user,
        status: :interested,
        lead_type: :OABLT_APOutbound
      )
      premium_pitch.skip_user_status_validation = true
      premium_pitch.save!

      EventTracker.perform_async(user.id, "ap_lead_process_started_backend", {
        "rm_user_id" => current_admin_user.id,
        "user_status" => user.status,
      })
    else
      # Check if user already has an RM assigned
      existing_rm = user.get_rm_user

      if existing_rm.present? && existing_rm.id != current_admin_user.id
        render json: {
          error: 'Lead process already started by another RM'
        }, status: :conflict
        return
      end

      # If RM is not present, update premium pitch lead_type and status
      if existing_rm.nil?
        premium_pitch.lead_type = :OABLT_APOutbound
        premium_pitch.status = :interested
        premium_pitch.skip_user_status_validation = true
        premium_pitch.save!

        EventTracker.perform_async(user.id, "ap_lead_process_started_backend", {
          "rm_user_id" => current_admin_user.id,
          "user_status" => user.status,
        })
      end
    end

    # Assign current admin user as RM if not already assigned
    if user.get_rm_user.nil?
      user.save_rm_user(rm_user_id: current_admin_user.id)
    end

    # Return success response
    render json: {
      success: true,
      user_id: user.id,
      premium_pitch_status: premium_pitch.status
    }, status: :ok

  rescue ActiveRecord::RecordInvalid => e
    render json: { error: e.message }, status: :unprocessable_entity
  rescue StandardError => e
    Rails.logger.error "Error starting lead process: #{e.message}"
    render json: { error: 'An error occurred while starting the lead process' }, status: :internal_server_error
  end

  private

  def ensure_agent_partner_role
    unless current_admin_user.agent_partner_role?
      render json: { error: 'Forbidden' }, status: :forbidden
    end
  end


end 
