module WebApp
  class PostersController < WebAppController

    def get_poster_web_tool_frames
      circle_hash_id = params[:circle_hash_id]
      category_kind = params[:category_kind]
      creative_id = params[:creative_id]
      circle = Circle.find_by_hashid(circle_hash_id)

      # byebug

      if circle.blank?
        render json: { success: false, message: "సర్కిల్ కనుగొనబడలేదు" }, status: :not_found
        return
      end

      circle_details = circle.get_short_json
      circle_has_package_enabled = circle.circle_package&.enable_fan_posters || false

      unless circle_has_package_enabled
        render json: { success: true, layouts: [], creatives: [],
                       praja_download_link: get_download_link, enable_fan_posters: circle_has_package_enabled, }, status: :ok
        return
      end

      frames = CircleFrame.get_circle_frames
      basic_frame = frames.find { |frame| frame[:frame_type] == "basic" && frame[:identity_type] == "flat_user" }

      if basic_frame.blank?
        render json: { success: false, message: "పోస్టర్ కనుగొనబడలేదు" }, status: :not_found
        return
      end

      gradient_circle_id = circle.id

      if circle.political_leader_level?
        related_circle_id = CirclesRelation.where(first_circle_id: circle.id, relation: 'Leader2Party', active: true).first&.second_circle_id
        gradient_circle_id = related_circle_id if related_circle_id.present?
      end

      gradient_circle = circle

      if gradient_circle_id != circle.id
        gradient_circle = Circle.find(gradient_circle_id)
      end


      sponsorship_json = circle.sponsorship_json
      gradients_hash = gradient_circle.get_gradients_for_poster_web_tool(false, basic_frame[:identity_type])
      _, circle_common_hash_for_layout = circle.get_common_hash_for_layout_and_layout_variation_circle(entity: circle)
      user_hash = nil

      if @user.present?
        photo_url = @user.poster_photo_with_background&.url || @user.photo&.url || Constants.dummy_cutout_url
        user_hash = {
          id: @user.id,
          name: @user.name,
          photo_url: photo_url,
          badge: @user.get_badge_role_including_unverified
        }
      end

      basic_layout = {
        layout_type: "basic",
        identity: {
          type: basic_frame[:identity_type],
          user: user_hash,
          is_user_position_back: false
        },
        gradients: gradients_hash,
        sponsorship: sponsorship_json,
        share_url: get_share_url(circle: circle),
        text_color: circle.get_text_color,
        analytics_params: {
          layout_type: "basic",
          identity_type: basic_frame[:identity_type],
          is_sponsored: sponsorship_json.present?,
          circle_id: circle.id,
          circle_hash_id: circle.hashid,
          circle_name: circle.name,
          circle_name_en: circle.name_en,
          circle_type: circle.circle_type,
          circle_level: circle.level,
          category_kind: category_kind,
        }.compact
      }

      basic_layout = basic_layout.merge(circle_common_hash_for_layout)

      layouts = [basic_layout]

      creatives_hash = PosterCreative.of_circle(circle_id: circle.id, kind: category_kind, creative_id: creative_id,
                                                include_expired: false)
      if creatives_hash.blank?
        render json: { success: false, message: "క్రియేటివ్స్ కనుగొనబడలేదు" }, status: :not_found
        return
      end

      creatives = build_creatives_hash(creatives_hash: creatives_hash, circle: circle, category_kind: category_kind)

      render json: { success: true, layouts: layouts, creatives: creatives,
                     praja_download_link: get_download_link, circle_details: circle_details,
                     enable_fan_posters: circle_has_package_enabled }, status: :ok
    end

    def get_metadata_for_poster_web_tool
      circle_hash_id = params[:circle_hash_id]
      creative_id = params[:creative_id]
      category_kind = params[:category_kind]

      circle = Circle.find_by_hashid(circle_hash_id)
      if circle.blank?
        render json: { success: false, message: "సర్కిల్ కనుగొనబడలేదు" }, status: :not_found
        return
      end

      creatives_hash = PosterCreative.of_circle(circle_id: circle.id, kind: category_kind, creative_id: creative_id,
                                                include_expired: false, limit: 1)

      if creatives_hash.blank?
        render json: { success: false, message: "క్రియేటివ్స్ కనుగొనబడలేదు" }, status: :not_found
        return
      end

      creatives = build_creatives_hash(creatives_hash: creatives_hash, circle: circle, category_kind: category_kind)

      metadata = circle.get_creative_metadata(creative_id: creative_id || creatives.first[:id])

      render json: { success: true, metadata: metadata }, status: :ok
    end

    def submit_agent_partner_interest
      user_hash_id = params[:user_hash_id]
      Honeybadger.context(user_hash_id: user_hash_id)
      
      if user_hash_id.blank?
        return render json: { success: false, message: 'యూసర్ హాష్ ఐడి అవసరం' }, status: :bad_request
      end

      begin
        @user = User.find_by_hashid(user_hash_id)
        
        if @user.blank?
          return render json: { success: false, message: 'ఈ హాష్ ఐడి కి సంబంధించిన యూసర్ కనుగొనబడలేదు' }, status: :not_found
        end

        Honeybadger.context(user_id: @user.id)

        # Basic validation - check if user is active
        unless @user.active?
          return render json: { success: false, message: 'యూసర్ యాక్టివ్ కాదు' }, status: :bad_request
        end
        # Prepare data for Google Sheets export
        user_id = @user.id
        user_phone = @user.phone
        user_name = @user.name
        time_stamp = Time.zone.now.strftime('%Y-%m-%d %H:%M:%S')
        
        data = [
          [user_id, user_phone, user_name, time_stamp]
        ]
        
        # Use a specific spreadsheet ID for this action - you may need to update this
        spreadsheet_id = "118fD9mXTWAp6O823eCk1IHZVnAIOcfy5LnSSdNzoUXA"
        
        ExportDataToGoogleSheets.perform_async(user_id, data, spreadsheet_id)

        EventTracker.perform_async(@user.id, 'agent_partner_interest_submitted_backend', {})

        render json: { success: true, message: 'డేటా విజయవంతంగా సబ్మిట్ చేయబడింది' }, status: :ok
      rescue => e
        Honeybadger.notify(e)
        render json: { success: false, message: 'డేటా సబ్మిట్ చేయబడలేదు. కాసేపాగి ప్రయత్నించండి' }, status: :unprocessable_entity
      end
    end

    private

    def get_download_link
      deeplink_uri = URI.parse("praja://buzz.praja.app/posters")
      link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/qna3')
      query_params = { _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s }
      query_params[:paffid] = @user.id if @user.present?
      link_uri.query = URI.encode_www_form(query_params)
      link_uri.to_s
    end

    def get_share_url(circle:)
      deeplink_uri = URI.parse("praja://buzz.praja.app/circles/#{circle.id}")

      link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/5oqr')

      posters_web_url = "https://praja.app/circles/#{circle.hashid}/posters"

      query_params = { _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s, _android_redirect: posters_web_url,
                       _ios_redirect: posters_web_url, _samsung_redirect: posters_web_url,
                       _fallback_redirect: posters_web_url }

      query_params[:paffid] = @user.id if @user.present?

      link_uri.query = URI.encode_www_form(query_params)

      Singular.shorten_link(link_uri.to_s)
    end

    def build_creatives_hash(creatives_hash:, circle:, category_kind:)
      creatives_hash.map do |creative|
        {
          id: creative.id,
          v2_url: creative.photo_v3.url,
          h1_background_type: creative.h1_leader_photo_ring_type,
          h2_background_type: creative.h2_leader_photo_ring_type,
          analytics_params: {
            creative_id: creative.id,
            h1_background_type: creative.h1_leader_photo_ring_type,
            h2_background_type: creative.h2_leader_photo_ring_type,
            circle_id: circle.id,
            circle_hash_id: circle.hashid,
            circle_name: circle.name,
            circle_name_en: circle.name_en,
            circle_type: circle.circle_type,
            circle_level: circle.level,
            category_kind: category_kind,
          }.compact
        }
      end
    end

  end
end
