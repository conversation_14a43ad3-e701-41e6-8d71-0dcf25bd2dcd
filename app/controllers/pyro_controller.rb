# frozen_string_literal: true

class PyroController < ExternalServiceController
  before_action :set_user
  before_action :set_event_params, only: [:send_to_mixpanel]

  def send_to_mixpanel
    if params[:event_name].nil?
      return render json: { success: false, message: 'Event name not present' }, status: :bad_request
    end

    EventTracker.perform_async(@user.id, params[:event_name], @event_properties)

    render json: { success: true }, status: :ok
  end

  private

  def set_user
    user_id = params[:user_id]
    return render json: { success: false, message: 'User ID not present' }, status: :bad_request if user_id.nil?

    @user = User.find_by(id: user_id)

    render json: { success: false, message: 'User not found' }, status: :not_found if @user.nil?
  end

  def set_event_params
    @event_properties = params[:properties]&.permit!&.to_h || {}
  end
end
