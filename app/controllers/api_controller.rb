# frozen_string_literal: true

class ApiController < ActionController::API
  include Popup

  before_action :set_locale
  before_action :set_app_version
  before_action :set_app_build_number
  before_action :check_api_key
  before_action :set_logged_in_user,
                only: [:refresh_token, :get_initial_data, :get_refer_text]
  before_action :set_logged_in_user_optional, only: [:save_fcm_token]
  before_action :set_pagination_params

  newrelic_ignore :only => [:index]

  def set_app_version
    app_version_string = if request.headers['X-App-Version'].present?
                           request.headers['X-App-Version']
                                  .gsub('-alpha', '.a')
                                  .gsub('-beta', '.b')
                                  .gsub('+', '.')
                         else
                           '0.3.6' # Version till when app version was not being sent
                         end

    # initialise app version controller object & currect attributes data via AppVersionSupport class
    @app_version = Gem::Version.new(app_version_string)
    AppVersionSupport.new(app_version_string)

    @pwa_version = if request.headers['X-PWA-Version'].present?
                     Gem::Version.new(request.headers['X-PWA-Version'].gsub('-alpha', '.a').gsub('-beta', '.b').gsub(
                       '+', '.'
                     ))
                   else
                     Gem::Version.new('0.0.1') # Version till when app version was not being sent
                   end

    @app_os = if @app_version >= Gem::Version.new('1.5.1')
                if request.headers['X-App-OS'].present?
                  request.headers['X-App-OS'].strip.downcase
                else
                  'unknown'
                end
              end

    Honeybadger.context({
                          app_version: @app_version,
                          app_os: @app_os
                        })
  end

  def set_app_build_number
    @app_build_number = if request.headers['X-App-BuildNumber'].present?
                          request.headers['X-App-BuildNumber'].to_i
                        else
                          User.get_build_number_from_app_version(@app_version.to_s)
                        end
    Honeybadger.context({ app_build_number: @app_build_number })
  end

  def check_api_key
    if AppVersionSupport.should_check_for_api_key? && (!request.headers['X-Api-Key'].present? || request.headers['X-Api-Key'] != Rails.application.credentials[:api_key])
      render json: {
        message: I18n.t('errors.permission_denied_text') # unauthorized
      }, status: :unauthorized
    end
  end

  def set_logged_in_user
    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')

      @user, send_jwt_token_header = get_user_and_send_jwt_token_header(token)

      if @user.nil?
        render json: {
          message: I18n.t('errors.permission_denied_text') # unauthorized
        }, status: :unauthorized
      else
        Current.logged_in_user = @user
        Honeybadger.context({ user_id: @user.id })
        ::NewRelic::Agent.add_custom_attributes({ user_id: @user.id })

        user_token_key = "user_token_usage_#{@user.id}"
        utu_id = $redis.get(user_token_key).to_i
        if utu_id <= 0
          utu = UserTokenUsage.create(app_version: @app_version, user: @user)
          $redis.set(user_token_key, utu.id.to_s, ex: 15.minutes)
        end

        # if app_version is not nil from app
        # update the app version and app build number if there is mismatch of values
        if @app_version != Gem::Version.new('0.3.6') &&
          (@user.app_version != @app_version.to_s || @user.app_build_number != @app_build_number)
          UpdateAppVersionAndAppBuildNumber.perform_async(@user.id, @app_version.to_s, @app_build_number)
        end

        # Set device token
        device_token = request.headers['X-Device-Token']
        device_id = request.headers['X-Device-Id']
        device_make = request.headers['X-Device-Make']
        device_model = request.headers['X-Device-Model']
        UpdateDeviceToken.perform_async(device_token, user_id, @app_version.to_s, @app_os,
                                        device_id, device_make, device_model) if device_token.present?

        response.set_header(Constants.jwt_access_token_header, @user.generate_jwt_token) if send_jwt_token_header
      end
    else
      render json: { message: 'Unauthorized' }, status: :unauthorized
    end
  end

  def current_user
    @user
  end

  def set_logged_in_user_optional
    @user = nil

    if request.headers['Authorization'].present?
      token = request.headers['Authorization'].sub('Bearer ', '')

      @user, send_jwt_token_header = get_user_and_send_jwt_token_header(token)

      response.set_header(Constants.jwt_access_token_header, @user.generate_jwt_token) if @user.present? && send_jwt_token_header
      ::NewRelic::Agent.add_custom_attributes({ user_id: @user.id }) if @user.present?
      Honeybadger.context({
                            user_id: @user&.id,
                            access_token: token,
                            app_version: @app_version,
                            app_os: @app_os
                          })

    end
  end

  def get_user_and_send_jwt_token_header(token)
    send_jwt_token_header = false
    if token.length == 32
      user, _ = UserToken.get_user_from_token(token)

      # Set New JWT token header if a request is using old auth token, irrespective of app version
      send_jwt_token_header = true
    else
      payload = JsonWebToken.decode(token)
      if payload.present? && payload['user_id'].present?
        user = User.active.find_by(id: payload['user_id'])
      end
    end
    [user, send_jwt_token_header]
  end

  def refresh_token
    render json: { success: true }, status: :ok
  end

  def save_fcm_token
    if params[:device_token].present?
      device_token = params[:device_token]
      device_id = request.headers['X-Device-Id']
      device_model = request.headers['X-Device-Model']
      device_make = request.headers['X-Device-Make']

      UpdateDeviceToken.perform_async(device_token, @user&.id, @app_version.to_s, @app_os,
                                      device_id, device_make, device_model) if device_token.present?
    end
    render json: { success: true }, status: :ok
  end

  def index
    # revision = 'dev'
    revision = ENV['GIT_SHA'][0..6] # ENV['GIT_SHA'].present? ? ENV['GIT_SHA'][0..6] : 'unknown' if Rails.env.production?

    db_status = 'disabled'
    # db_status = 'down'
    # begin
    #   ActiveRecord::Base.connection.execute('SELECT 1')
    #   db_status = 'up'
    # end

    redis_status = 'down'
    begin
      redis_return = $redis.ping
      redis_status = 'up' if redis_return == 'PONG'
    end

    es_status = 'down'
    begin
      es_return = ES_CLIENT.ping
      es_status = 'up' if es_return == true
    end

    render json: {
      env: Rails.env,
      revision: revision,
      app_status: 'up',
      db_status: db_status,
      redis_status: redis_status,
      es_status: es_status,
      lang: I18n.locale,
      message: I18n.t('hello'),
    }, status: :ok
  end

  def get_poster
    return if AppVersionSupport.poster_carousel_in_feed_supported?

    poster = @user.user_active_poster(@app_version) unless @user.has_premium_layout?
    return poster.get_poster_hash(@user, @app_version) if poster.present?
  end

  def get_initial_data
    trend_training_eligible = @user.is_eligible_for_trend_feedback?

    internet_connection = if request.headers['X-Internet-Connection'].present?
                            request.headers['X-Internet-Connection'].strip.downcase
                          else
                            'unknown'
                          end
    video_auto_play = false # internet_connection == 'wifi' ? true : false

    # get poster data of user
    poster_data = get_poster

    # check if user should see follow contacts screen or not if poster data is blank
    show_follow_contacts_screen = @user.should_show_follow_contacts && poster_data.blank?

    is_test_user = @user.is_test_user?

    permissions = []
    if is_test_user || @user.internal?
      permissions = InternalPermission.joins(internal_roles: :users).where(users: { id: @user.id }).pluck(:identifier)
    end

    is_feed_experiment_user = @user.my_feed_enabled?
    enable_poster_category_share = is_test_user ? true : false

    feeds = if is_feed_experiment_user
              [{
                 "id": 'my_feed',
                 "title": I18n.t('my_feed_text'),
                 "type": 'my_feed',
                 "path": 'my_feed',
                 "query_params": nil,
               }, {
                 "id": 'trending_feed',
                 "title": I18n.t('trending_feed_text'),
                 "type": 'trending_feed',
                 "path": 'trending',
                 "query_params": nil,
               }]
            else
              [{
                 "id": 'trending_feed',
                 "title": I18n.t('trending_feed_text'),
                 "type": 'trending_feed',
                 "path": 'trending',
                 "query_params": nil,
               }, {
                 "id": 'my_feed',
                 "title": I18n.t('my_feed_text'),
                 "type": 'my_feed',
                 "path": 'my_feed',
                 "query_params": nil,
               }]
            end

    # singular events
    singular_events = %w[add_trend create_post_saved join_circle share default_feed_loaded create_comment clicked_submit_poster_premium_pitch]

    show_fan_poster_request_prompt = @user.show_fan_poster_request_prompt?

    cutout_placeholder_url = "https://a-cdn.thecircleapp.in/production/admin-media/40/13a44389-91e2-46f6-81dc-67f18922ee58.png"
    family_cutout_placeholder_url = "https://a-cdn.thecircleapp.in/production/admin-media/40/90567feb-227e-43c8-88cb-e90bad25abdc.png"
    hero_cutout_placeholder_url = "https://a-cdn.thecircleapp.in/production/admin-media/40/838269b7-bd5f-489f-8834-55249346e896.png"
    with_background_cutout_placeholder_url = "https://a-cdn.thecircleapp.in/production/admin-media/40/13a44389-91e2-46f6-81dc-67f18922ee58.png"
    video_poster_cutout_placeholder_url = "https://a-cdn.thecircleapp.in/production/admin-media/40/13a44389-91e2-46f6-81dc-67f18922ee58.png"

    firebase_app_instance_id = params[:firebase_app_instance_id]
    device_id = request.headers['X-Device-Id']
    if firebase_app_instance_id.present? && device_id.present?
      UserDeviceToken.find_by(user_id: @user.id, device_id: device_id)&.update(firebase_app_instance_id: firebase_app_instance_id)
    end

    # Floww::UserOnline.perform_async(@user.id) if @user.get_floww_contact_id.present?

    render json: {
      notifications_count: 0,
      notifications_unread_count: @user.get_unread_notifications_count,
      permissions: permissions,
      can_create_circle: false,
      is_feed_experiment_user: is_feed_experiment_user,
      posts_page_count: 15,
      users_page_count: 15,
      notifications_page_count: 15,
      enable_trend_tutorial: @user.should_show_trend_tutorial?, # && !trend_training_eligible,
      enable_trend_feedback: trend_training_eligible,
      trend_tutorial_trigger_posts: 5,
      trend_tutorial_time_in_seconds: 3,
      trend_tutorial_text: I18n.t('trend_tutorial_text'),
      trend_feedback_text: I18n.t('trend_feedback_text'),
      video_auto_play: video_auto_play,
      video_loop_after_completion: false,
      poster: poster_data,
      connect_timeout: 5000,
      receive_timeout: 15000,
      show_follow_contacts_screen: show_follow_contacts_screen,
      popup_path: get_popup_path,
      dm_enabled: @user.dm_enabled?,
      group_dm_enabled: false,
      dm_channels_enabled: AppVersionSupport.dm_channels_enabled?,
      dm_private_groups_enabled: AppVersionSupport.dm_private_groups_enabled? && is_test_user,
      max_dm_forward_count: Constants.get_max_dm_forward_count,
      dm_media_upload_url: Constants.get_user_post_media_upload_urls,
      logged_in_user: @user.get_user_response_hash(@app_version, ignore_posts_count: true, include_unverified_badge: true),
      force_update: AppVersionSupport.should_force_update?,
      min_creative_width: 800.0,
      min_creative_height: 1000.0,
      creative_aspect_ratio: 0.8,
      enable_poster_category_share: enable_poster_category_share,
      dm_fallback_group_photo_url: Constants.default_dm_private_group_icon,
      dm_fallback_channel_photo_url: Constants.default_dm_channel_icon,
      customer_care_user_id: @user.get_badge_role_including_unverified.present? ? Constants.customer_care_user_id : 0,
      preload_fonts_config: Font.get_preload_fonts_config,
      verification_pitch: I18n.t('verification_pitch'),
      get_verified_cta: I18n.t('badge_verified_cta'),
      poster_disable_screen_capture: is_test_user ? false : true,
      feeds: feeds,
      singular_events: singular_events,
      show_fan_poster_request_prompt: show_fan_poster_request_prompt,
      preload_banner_ad_sizes: [],
      preferred_upi_apps: @app_os == 'ios' ? Constants.preferred_upi_apps_ios : Constants.preferred_upi_apps_android,
      poster_photo_bg_removal_url: Constants.get_photo_bg_removal_url,
      poster_photo_placeholder_urls: {
        cutout: cutout_placeholder_url,
        family_cutout: family_cutout_placeholder_url,
        hero_cutout: hero_cutout_placeholder_url,
        with_background: with_background_cutout_placeholder_url,
        video_poster_cutout: video_poster_cutout_placeholder_url
      },
      app_open_analytics_params: AppVersionSupport.is_app_open_analytics_params_supported? ?
                                   @user.get_app_open_analytics_params : nil,
    }, status: :ok
  end

  def get_refer_text
    link = if @app_version >= Gem::Version.new('1.11.3')
             "https://prajaapp.sng.link/A3x5b/7lmr?paffid=#{@user.id}"
           else
             @user.get_dynamic_link
           end

    if @user.internal_journalist?
      district = @user.district
      msg = I18n.t('referral_texts.user_internal_journalist_refer.message', district_name: district.name, members_count: district.get_members_count, badge_users_count: district.get_badge_users_count, link: link)
    elsif @user.get_badge_role.present?
      user_role = @user.get_badge_role
      circle_name = user_role.get_primary_circle.name
      msg = I18n.t('referral_texts.user_badge_role_present_refer.message', circle_name: circle_name, user_role: user_role.get_role_name, link: link)
    else
      msg = I18n.t('referral_texts.user_default_refer.message', link: link)
    end

    render json: { success: true, text: msg, generate_invite_card: !@user.internal_journalist?, invite_card_data: @user.get_badge_notification_data }, status: :ok
  end

  def get_referrals_data
    private_key = OpenSSL::PKey::RSA.new(File.read(Rails.root.join('config/credentials/rsa_private_key.pem')), 'password')

    data = private_key.private_decrypt(Base64.decode64(referral_params[:signature]))

    if data.nil?
      render json: { success: false, message: 'Invalid request' }, status: :bad_request
      return
    end

    data = ActiveSupport::JSON.decode(data)
    if data['user_id'].nil?
      render json: { success: false, message: 'Invalid request' }, status: :bad_request
      return
    end

    @user = User.find data['user_id']

    response_data = @user.get_referral_data
    response_summary = @user.get_referral_summary(response_data)

    render json: {
      success: true,
      total_points: response_data.map { |h| h[:points] }.sum,
      summary_data: response_summary,
      data: response_data
    }
  end

  private

  def referral_params
    params.require([:signature])
    params.permit(:signature)
  end

  def set_pagination_params
    @offset = 0
    @offset = params[:offset].to_i if params[:offset].present?

    @count = 10
    @count = params[:count].to_i if params[:count].present?
  end

  def set_locale
    locale = request.headers['Accept-Language'] || 'te'
    I18n.locale = locale if I18n.available_locales.include?(locale.to_sym)
  end
end
