class PostersController < ApiController
  before_action :set_logged_in_user
  before_action :set_poster, only: [:mark_as_seen_poster_card, :get_share_text_with_short_link, :index]

  def mark_as_seen_poster_card
    $redis.sadd("poster_share_card_#{@poster.id}", @user.id.to_s)
    render json: { status: true }, status: :ok
  end

  def get_share_text_with_short_link
    render json: { success: true, text: @poster.get_share_text(@user) }, status: :ok
  end

  def share
    if params[:method].blank? || params[:creative_id].blank?
      render json: { message: "Missing required parameters" }, status: :bad_request
      return
    end

    unless PosterShare.methods.values.include?(params[:method])
      render json: { message: "Invalid share method" }, status: :bad_request
      return
    end

    frame_id = params[:frame_id].to_i
    creative_id = params[:creative_id].to_i

    today_key = "poster_creative_shares_counts_#{Time.zone.today.strftime('%d%m%Y')}"
    $redis.zincrby(today_key, 1, creative_id)
    $redis.expire(today_key, 24.hours.to_i) if $redis.ttl(today_key) == -1

    PosterShare.create!(
      user: @user,
      poster_creative_id: creative_id,
      frame_id: frame_id,
      method: params[:method],
      actioned_at: Time.zone.now
    )

    if @user.leader_profession? && Frame.find(frame_id)&.is_user_free_frame?
      premium_pitch = @user.find_or_initialize_premium_pitch(source: :LEADER_POSTER_SHARE)
      premium_pitch.shown_interest! if premium_pitch.may_shown_interest?
    end

    deeplink_url = @user.deeplink_url_in_poster_share(params[:method], creative_id:)

    render json: { success: true, deeplink_url: deeplink_url }.compact, status: :ok
  rescue StandardError => e
    render json: { success: false, message: "Failed to log share", error: "#{e}" }, status: :internal_server_error
  end

  def frames_mark_as_seen_bulk
    unless params[:frame_ids].present?
      render json: { success: false }, status: :bad_request
      return
    end
    logger.debug "Marking as seen for user: #{@user.id} and frames: #{params[:frame_ids]}"

    params[:frame_ids].keys.each_slice(50) do |frame_ids|
      frame_with_timestamps = frame_ids.map { |frame_id| [frame_id, params[:frame_ids][frame_id]] }.to_h
      logger.debug "Marking as seen for user: #{@user.id} and frames: #{frame_with_timestamps}"
      QueueFrameViews.perform_async(@user.id, frame_with_timestamps)
    end
    render json: { success: true }, status: :ok
  end

  def poster_creatives_mark_as_seen_bulk
    unless params[:poster_creative_ids].present?
      render json: { success: false }, status: :bad_request
      return
    end
    logger.debug "Marking as seen for user: #{@user.id} and creatives: #{params[:poster_creative_ids]}"

    all_poster_creative_ids = params[:poster_creative_ids].keys
    pp = @user.premium_pitch
    if pp&.may_reached_milestone_1?
      passed_milestone_1_criteria = PosterCreative
                                      .where(id: all_poster_creative_ids,
                                             creative_kind: [:wishes, :quotes, :schemes, :info])
                                      .exists?

      if passed_milestone_1_criteria
        if @user.premium_poster_usage_count_after_trial_enabled >= 1
          pp.reached_milestone_1!
        else
          $redis.sadd(Constants.passed_milestone_1_creatives_criteria_redis_key, @user.id)
        end
      end
    end

    all_poster_creative_ids.each_slice(50) do |poster_creative_ids|
      poster_creative_with_timestamps = poster_creative_ids.map { |poster_creative_id| [poster_creative_id, params[:poster_creative_ids][poster_creative_id]] }.to_h
      logger.debug "Marking as seen for user: #{@user.id} and creatives: #{poster_creative_with_timestamps}"
      QueuePosterCreativeViews.perform_async(@user.id, poster_creative_with_timestamps)
    end
    render json: { success: true }, status: :ok
  end

  def help
    user_id = @user.id
    phone = @user.phone
    name = @user.name
    get_badge_role = @user.get_badge_role
    user_role_id = get_badge_role&.id
    user_badge_role_text = get_badge_role&.get_description
    affiliated_party_id = @user.affiliated_party_circle_id
    affiliated_party_name = Circle.find_by_id(affiliated_party_id)&.name if affiliated_party_id.present?
    time_stamp = Time.zone.now.strftime('%Y-%m-%d %H:%M:%S')

    data = [
      [user_id, phone, name, user_role_id, user_badge_role_text, affiliated_party_name, time_stamp]
    ]
    spreadsheet_id = '1rOkXSvRqdlUs3ZSsD7o3GZyBv1FUvXcOjsioViNAA2o'

    ExportDataToGoogleSheets.perform_async(user_id, data, spreadsheet_id)

    render json: { message: '📞 మిమ్మల్ని సంప్రదిస్తాం' }, status: :ok
  end

  def subscription_details
    return render json: { message: "చెల్లింపులను ప్రాసెస్ చేయలేకపోతున్నాము" }, status: :service_unavailable
    order_id = params[:order_id]&.to_i
    if order_id.blank?
      render json: { message: "Missing required parameters" }, status: :bad_request
      return
    end

    order = Order.find_by(id: order_id)
    if order.blank?
      render json: { message: "Order not found" }, status: :not_found
      return
    end

    if order.user_id != @user.id
      render json: { message: "Forbidden" }, status: :forbidden
      return
    end

    subscription = @user.get_subscription_screen_json(order)
    render json: { order: order, subscription: subscription }, status: :ok
  end

  private

  def set_poster
    if !params[:poster_id].nil? && !params[:poster_id].empty? && params[:poster_id].to_i.to_s == params[:poster_id].to_s
      @poster = Poster.find(params[:poster_id])
    end

    render json: { message: "Poster not found" }, status: :not_found if @poster.nil?
  end
end
