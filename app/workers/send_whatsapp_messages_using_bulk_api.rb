class SendWhatsappMessagesUsingBulkApi
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  sidekiq_options queue: :wati, retry: 1
  sidekiq_throttle(
    concurrency: {
      limit: 5,
    },
    threshold: {
      limit: 30,
      period: 10.seconds,
    }
  )

  def perform(template_name, broadcast_name)
    Rails.logger.warn("SendWhatsappMessagesUsingBulkApi worker Triggered - template_name: #{template_name}, broadcast_name: #{broadcast_name}")
    json_data = $redis.hget(Constants.wati_campaigns_redis_key, broadcast_name)

    if json_data.blank?
      Rails.logger.warn("No Redis data found for broadcast_name: #{broadcast_name}")
      return
    end

    receivers_data_parse = JSON.parse(json_data)

    receivers = receivers_data_parse.map do |phone, user_details|
      {
        whatsappNumber: phone,
        customParams: user_details.map { |key, value| { name: key.to_s, value: value } }
      }
    end

    wati_end_point = Constants.get_send_messages_using_bulk_api

    url = URI("#{wati_end_point}")
    raise "Unable to form URL" if url.host.nil?

    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(url)
    request["content-type"] = 'application/json-patch+json'
    request["Authorization"] = Rails.application.credentials[:wati_api_key]
    request.body = {
      template_name: template_name,
      broadcast_name: broadcast_name,
      receivers: receivers
    }.to_json

    response = http.request(request)
    body = JSON.parse(response.body)

    if response.code.to_i != 200 || body["result"] != true
      Rails.logger.warn("Send WATI bulk messages API - Request Body: #{request.body}, Response: #{response.code}, Response Body: #{body}")
      Honeybadger.notify("WATI Bulk messages trigger error: #{response.code}", context: {
        response_code: response.code,
        response_body: body
      })
    else
      $redis.hdel(Constants.wati_campaigns_redis_key, broadcast_name)
    end
  end
end
