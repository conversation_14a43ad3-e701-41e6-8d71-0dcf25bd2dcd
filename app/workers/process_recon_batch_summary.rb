# frozen_string_literal: true

class ProcessReconBatchSummary
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :default, retry: 1
  sidekiq_throttle(
    :concurrency => { :limit => 1 },
    :threshold => { :limit => 5, :period => 1.minute }
  )

  def on_complete(status, options)
    redis_key = "failed_recon_charges:#{status.bid}"
    
    failed_charge_ids = $redis.smembers(redis_key)
    
    if failed_charge_ids.any?
      send_summary_alert(failed_charge_ids, status.bid)
    end
    
    # Clean up Redis data
    $redis.del(redis_key)
  end

  private

  def send_summary_alert(failed_charge_ids, batch_id)
    total_failed = failed_charge_ids.count
    
    # Format charge IDs for display (limit to first 20 for readability)
    charge_ids_display = if total_failed <= 20
                          failed_charge_ids.join(', ')
                        else
                          "#{failed_charge_ids.first(20).join(', ')} ... and #{total_failed - 20} more"
                        end
    
    notifier = Slack::Notifier.new(
      "*******************************************************************************"
    )
    
    notifier.post(
      text: ":warning: *Recon Batch Summary*\n" \
            "• Batch ID: `#{batch_id}`\n" \
            "• Total Failed Recons: #{total_failed}\n" \
            "• Failed Charge IDs: #{charge_ids_display}",
    )
  end
end
