# frozen_string_literal: true

class TriggerPosterGenerationForWatiTrialUsers
  include Sidekiq::Worker

  DAY_KEYS = [3, 4].freeze

  def perform
    Rails.logger.info("Starting poster generation trigger for WATI trial users")

    DAY_KEYS.each { |day| process_day(day) }

    Rails.logger.info("Finished triggering poster generation for WATI Day 3 & 4 users")
  end

  private

  def process_day(day)
    redis_key = Constants.wati_redis_key_for_day(day)
    user_ids = $redis.smembers(redis_key).map(&:to_i).uniq
    user_meta_data_insertions = []

    if user_ids.blank?
      Rails.logger.info("No users found for day #{day} (key: #{redis_key})")
      return
    end

    User.where(id: user_ids).find_in_batches(batch_size: 100) do |users|
      users.each do |user|
        campaign_name = "wati_trial_day_#{day}_poster"
        begin
          creative_data = get_user_creative_data_for_wati_campaign(user, campaign_name)

          if creative_data.blank? || creative_data.value.blank?
            Rails.logger.info("Skipping user #{user.id} — no creative found for #{campaign_name}")
            next
          end

          user_meta_data_insertions << creative_data
          GeneratePosterCampaignImage.perform_async(user.id, creative_data.value.to_i, campaign_name)
        rescue => e
          Rails.logger.error("[Day #{day}] Error for user #{user.id}: #{e}")
        end
      end
      UserMetadatum.import(user_meta_data_insertions, on_duplicate_key_ignore: true, batch_size: 100)
    end
  end

  def get_user_creative_data_for_wati_campaign(user, campaign_name)
    creative_id = user.today_most_shared_poster_related_to_user_circles
    return nil unless creative_id.present?
    key = "#{campaign_name}_most_shared_creative_id"
    UserMetadatum.new(user_id: user.id, key: key, value: creative_id)
  end

end
