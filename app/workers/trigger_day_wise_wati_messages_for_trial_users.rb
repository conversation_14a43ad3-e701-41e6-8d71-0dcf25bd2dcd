class TriggerDayWiseWatiMessagesForTrialUsers
  include Sidekiq::Worker

  def perform(redis_key, day)
    start_time = Time.zone.now
    Rails.logger.info("Starting TriggerDayWiseMessagesWorker with redis_key: #{redis_key}, day: #{day}")

    @redis_key = redis_key
    @day = day
    @current_time = Time.zone.now
    @user_ids = $redis.smembers(redis_key).map(&:to_i)
    @wati_campaigns_to_save = []
    @campaign_name = "wati_trial_day_#{@day}_poster"

    in_between_days = Constants.wati_trial_in_between_start_day..Constants.wati_trial_in_between_end_day
    return if @user_ids.blank?

    case @day
    when 2, in_between_days
      handle_day_2_or_in_between_days
    when 3, 4
      handle_day_3_or_4
    when 11
      handle_day_11
    when 13
      handle_day_13
    when 14
      handle_day_14
    when 15
      handle_day_15
    end

    finalize_campaign
    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("TriggerDayWiseWatiMessagesForTrialUsers done for #{redis_key} & #{day} || Time taken: #{elapsed_time} seconds")
  end

  private

  def users
    @users ||= User.includes(:user_plan).where(id: @user_ids)
  end

  def users_not_shared_today
    users.reject { |user| shared_today_user_ids.include?(user.id) }
  end

  def shared_today_user_ids
    @shared_today_user_ids ||= PosterShare.where(user_id: @user_ids, created_at: Time.zone.today.beginning_of_day..Time.zone.now).distinct.pluck(:user_id)
  end

  # In-trial users only see premium posters, so no premium poster share check is needed.
  # Since these users are already in the trial period, the created_at check is also skipped.
  def shared_once_in_trial_period_user_ids
    @shared_once_in_trial_period_user_ids ||= PosterShare.where(user_id: @user_ids).distinct.pluck(:user_id)
  end

  def auto_pay_active_user_ids
    return @auto_pay_active_user_ids unless @auto_pay_active_user_ids.nil?
    subscriptions = Subscription.where(user_id: @user_ids).to_a
    latest_subs = subscriptions.group_by(&:user_id).transform_values(&:last)
    @auto_pay_active_user_ids = latest_subs.select { |_, sub| sub.status == 'active' }.keys.to_set
  end

  def handle_day_2_or_in_between_days
    eligible_users = users_not_shared_today
    return if eligible_users.blank?

    tag = (@day == 2) ? 'day_2' : 'in_between_days'
    send_bulk_api_per_template(eligible_users, tag)
  end

  def handle_day_3_or_4
    shared_users, not_shared_users = users.partition { |user| shared_today_user_ids.include?(user.id) }

    shared_tag = (@day == 3) ? 'day_3' : 'day_4'
    not_shared_tag = (@day == 3) ? 'not_shared_day_3' : 'not_shared_day_4'

    send_bulk_api_per_template(shared_users, shared_tag) unless shared_users.blank?

    return if not_shared_users.blank?
    keys = [
      Constants.poster_image_url(@campaign_name),
      "#{@campaign_name}_most_shared_creative_id"
    ]

    poster_metadata = UserMetadatum
                        .where(user_id: @user_ids, key: keys)
                        .pluck(:user_id, :key, :value)

    @poster_image_urls = {}
    @hidden_poster_creative_ids = {}

    poster_metadata.each do |user_id, key, value|
      if key == Constants.poster_image_url(@campaign_name)
        @poster_image_urls[user_id] = value
      elsif key == "#{@campaign_name}_most_shared_creative_id"
        @hidden_poster_creative_ids[user_id] = value.to_i
      end
    end

    creative_ids = @hidden_poster_creative_ids.values.uniq
    @creative_map = PosterCreative
                      .includes(:event)
                      .where(id: creative_ids)
                      .index_by(&:id)

    send_bulk_api_per_template(not_shared_users, not_shared_tag)
  end

  def handle_day_11
    not_shared_users = users.reject { |user| shared_once_in_trial_period_user_ids.include?(user.id) }
    send_bulk_api_per_template(users, 'day_11')
    save_info_to_google_sheets(not_shared_users) unless not_shared_users.blank?
  end

  def handle_day_13
    not_shared_users = users.reject { |user| shared_once_in_trial_period_user_ids.include?(user.id) }

    send_bulk_api_per_template(not_shared_users, 'day_13') unless not_shared_users.blank?
  end

  def handle_day_14
    not_shared_users = users.reject { |user| shared_once_in_trial_period_user_ids.include?(user.id) }
    not_shared_active = not_shared_users.select { |user| auto_pay_active_user_ids.include?(user.id) }

    send_bulk_api_per_template(not_shared_active, 'not_shared_day_14') unless not_shared_active.empty?
  end

  def handle_day_15
    shared_users, not_shared_users = users.partition { |user| shared_once_in_trial_period_user_ids.include?(user.id) }

    shared_inactive = shared_users.reject { |user| auto_pay_active_user_ids.include?(user.id) }
    not_shared_inactive = not_shared_users.reject { |user| auto_pay_active_user_ids.include?(user.id) }

    send_bulk_api_per_template(shared_inactive, 'shared_day_15') unless shared_inactive.empty?
    send_bulk_api_per_template(not_shared_inactive, 'not_shared_day_15') unless not_shared_inactive.empty?
  end

  def send_bulk_api_per_template(users, tag)
    data_by_template = organise_data_based_on_template(users, tag)

    data_by_template.each do |template_name, users_data|
      users_data.each_slice(50).with_index do |users_data_batch, batch_index|
        broadcast_name = "#{template_name}_#{@current_time.strftime('%Y_%m_%d')}_#{batch_index + 1}"
        receivers_data = {}

        users_data_batch.each do |ud|
          phone = "91#{ud[:phone]}"
          payload = {
            name: ud[:name],
            poster_link: ud[:cta_link]
          }
          payload[:event_name] = ud[:event_name] if ud[:event_name].present?
          payload[:poster_image_url] = ud[:poster_image_url] if ud[:poster_image_url].present?
          payload[:paywall_link] = ud[:paywall_link] if ud[:paywall_link].present?
          payload[:amount] = ud[:amount] if ud[:amount].present?
          payload[:trial_extend_link] = ud[:trial_extend_link] if ud[:trial_extend_link].present?

          receivers_data[phone] = payload

          user_campaign_data = {
            user_id: ud[:user_id],
            broadcast_name: broadcast_name,
            campaign_data: payload,
            campaign_date: @current_time,
            campaign_type: "trial_to_payment_activation",
            status: "triggered",
            template_name: template_name,
          }
          @wati_campaigns_to_save << user_campaign_data

          EventTracker.perform_async(ud[:user_id], "trial_usage_campaign_triggered_backend", {
            day: @day,
            broadcast_name: broadcast_name,
            campaign_data: payload,
            template_name: template_name,
          })
        end

        $redis.hset(Constants.wati_campaigns_redis_key, broadcast_name, receivers_data.to_json)
        SendWhatsappMessagesUsingBulkApi.perform_async(template_name, broadcast_name)
      end
    end
  end

  def organise_data_based_on_template(users, tag)
    case tag
    when 'not_shared_day_3', 'not_shared_day_4'
      get_hidden_poster_templates(users)
    when 'in_between_days'
      build_data_by_template(users, tag, source: :upcoming_event)
    when 'shared_day_15', 'not_shared_day_15'
      build_data_by_template(users, tag, paywall: true, get_amount: true)
    when 'not_shared_day_14'
      build_data_by_template(users, tag, get_amount: true)
    else
      build_data_by_template(users, tag)
    end
  end

  def build_data_by_template(users, tag, paywall: false, get_amount: false, source: :poster)
    data_by_template = {}

    users.each_slice(100) do |user_batch|
      if source == :poster
        user_creative_ids = {}
        user_batch.each do |user|
          creative_id = user.today_most_shared_poster_related_to_user_circles
          next unless creative_id
          user_creative_ids[user] = creative_id
        end

        creative_ids = user_creative_ids.values.uniq
        creatives = PosterCreative.includes(:event).where(id: creative_ids).index_by(&:id)

        user_creative_ids.each do |user, creative_id|
          creative = creatives[creative_id]
          next unless creative

          event = creative.event
          template, user_data = build_user_template_data(user, event, creative, tag, paywall: paywall, get_amount: get_amount)
          next if template.nil? || user_data.nil?

          data_by_template[template] ||= []
          data_by_template[template] << user_data
        end

      elsif source == :upcoming_event
        user_batch.each do |user|
          events = Event.upcoming_events_including_current(user: user)
          next if events.blank?

          event = events.find { |e| e.get_creative_for_wati_campaign.present? }
          next unless event
          creative = event.get_creative_for_wati_campaign
          next unless creative

          template, user_data = build_user_template_data(user, event, creative, tag, paywall: paywall, get_amount: get_amount)
          next if template.nil? || user_data.nil?

          data_by_template[template] ||= []
          data_by_template[template] << user_data
        end
      end
    end

    data_by_template
  end

  def build_user_template_data(user, event, creative, tag, paywall: false, get_amount: false)
    return nil unless creative

    template = select_template(tag, event)
    deeplink = get_deep_link_for_event_and_creative(event&.id, creative.id)
    amount = user.user_plan.amount
    return nil unless amount

    user_data = {
      user_id: user.id,
      name: user.name,
      phone: user.phone
    }

    excluded_tags = %w[day_11 day_13 shared_day_14 not_shared_day_14 shared_day_15 not_shared_day_15]
    user_data[:event_name] = event.name if event.present? && !excluded_tags.include?(tag)
    user_data[:amount] = amount if get_amount
    user_data[:cta_link] = deeplink if deeplink.present?
    user_data[:paywall_link] = get_premium_experience_screen_deep_link if paywall
    user_data[:trial_extend_link] = get_premium_benefits_loss_screen_deep_link if tag == 'not_shared_day_14'

    [template, user_data]
  end

  def get_hidden_poster_templates(users)
    template = 'hidden_poster_for_not_shared_users_v3'
    data_by_template = {}
    users.each do |user|
      poster_image_url = @poster_image_urls[user.id]
      creative_id = @hidden_poster_creative_ids[user.id]
      next if poster_image_url.blank? || creative_id.blank?
      creative = @creative_map[creative_id]
      next unless creative
      event = creative.event
      deeplink = get_deep_link_for_event_and_creative(event&.id, creative_id)

      user_data = {
        user_id: user.id,
        name: user.name,
        phone: user.phone,
        poster_image_url: poster_image_url
      }
      user_data[:cta_link] = deeplink if deeplink.present?
      data_by_template[template] ||= []
      data_by_template[template] << user_data
    end
    data_by_template
  end

  def select_template(tag, event)
    case tag
    when 'day_2'
      event.present? ? 'trial_to_payment_day_2_with_event_v2' : 'trial_to_payment_day_2_without_event_v2'
    when 'day_3'
      event.present? ? 'trial_to_payment_day_3_with_event_v3' : 'trial_to_payment_day_3_without_event_v2'
    when 'day_4'
      event.present? ? 'trial_to_payment_day_4_with_event_v2' : 'trial_to_payment_day_4_without_event_v1'
    when 'in_between_days'
      'trial_to_payment_in_between_days_with_event'
    when 'day_11'
      'trial_to_payment_day_11_v2'
    when 'day_13'
      'trial_to_payment_day_13_v1'
    when 'not_shared_day_14'
      'trial_to_payment_not_shared_autopay_active_day_14_v6'
    when 'shared_day_15'
      'trial_to_payment_shared_autopay_inactive_day_15_v3'
    when 'not_shared_day_15'
      'trial_to_payment_not_shared_autopay_inactive_day_15_v2'
    end
  end

  def generate_singular_link(deeplink_path)
    deeplink_uri = URI.parse(deeplink_path)
    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/szt4')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s })
    Singular.shorten_link(link_uri.to_s).gsub('https://prajaapp.sng.link/A3x5b/', '')
  end

  def get_deep_link_for_event_and_creative(event_id, creative_id)
    path = if event_id.present?
             "praja://buzz.praja.app/posters/layout?id=#{event_id}&creative_id=#{creative_id}"
           else
             "praja://buzz.praja.app/posters/layout?creative_id=#{creative_id}"
           end
    generate_singular_link(path)
  end

  def get_premium_benefits_loss_screen_deep_link
    generate_singular_link("praja://buzz.praja.app/premium-benefits-loss-screen?source=wati_trial_usage_campaign")
  end

  def get_premium_experience_screen_deep_link
    generate_singular_link("praja://buzz.praja.app/premium-experience?payment-sheet=true")
  end

  def save_info_to_google_sheets(users)
    spreadsheet_id = '1cRyeSPNYsweXLKmyN7nEISYQuTN4VIoyyScmo5dGXxM'
    data = users.map { |user| [user.id, user.name, user.phone, auto_pay_active_user_ids.include?(user.id)] }

    data.each_slice(100) do |data_batch|
      ExportDataToGoogleSheets.perform_async(nil, data_batch, spreadsheet_id)
    end
  rescue => e
    Rails.logger.error("Error saving to Google Sheets: #{e.message}")
    Honeybadger.notify(e, context: { user_ids: users.map(&:id) })
  end

  def finalize_campaign
    if @wati_campaigns_to_save.present?
      @wati_campaigns_to_save.each_slice(100) do |campaign_batch|
        UserWatiCampaign.insert_all(campaign_batch)
      end
    end

    if [3, 4].include?(@day) && @user_ids.present?
      @user_ids.each_slice(100) do |user_ids_batch|
        UserMetadatum.where(user_id: user_ids_batch, key: [Constants.poster_image_url(@campaign_name), "#{@campaign_name}_most_shared_creative_id"]).delete_all
      end
    end
  end
end
