# frozen_string_literal: true

class PosterPhotoScorePredictorUsingAI
  include Sidekiq::Worker

  sidekiq_options queue: :default, retry: 3, lock: :until_and_while_executing, on_conflict: :log

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("PosterPhotoScorePredictorUsingAI retries exhausted: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })

    user = User.find_by(id: user_id)
    return unless user&.poster_photo&.url.present?

    photo_url = user.poster_photo.url

    begin
      response = evaluate_poster_photo_without_bg(photo_url)
      # Log the API response for debugging
      Rails.logger.warn("OpenAI API response received for user_id: #{user_id}, photo_url: #{photo_url}, response: #{response}")
      # Parse and store the results
      parse_and_store_results(user, response)
    rescue StandardError => e
      Honeybadger.notify(e, context: { user_id: user_id, photo_url: photo_url })
      Rails.logger.error("Poster photo without background evaluation failed for user #{user_id}: #{e.message}")
    end
  end

  private

  def evaluate_poster_photo_without_bg(photo_url)
    system_prompt = build_system_prompt
    user_prompt = build_user_prompt(photo_url)

    uri = URI(Constants.openai_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri)
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{Rails.application.credentials[:openai_api_key_for_annual_pack_prediction]}"

    request.body = {
      model: "gpt-4.1-mini",
      messages: [
        { role: "system", content: system_prompt },
        { role: "user", content: user_prompt }
      ],
    }.to_json

    response = http.request(request)

    if response.code.to_i != 200
      raise StandardError, "OpenAI API request failed with status #{response.code}: #{response.body}"
    end

    JSON.parse(response.body)
  end

  def build_system_prompt
    <<~PROMPT
      You are an expert evaluator specializing in scoring user profile images to predict the likelihood of users subscribing to a yearly "Pitch" subscription. Your evaluation is strictly based on the following scoring criteria:

      1. Premium Dressing Scoring Guidelines:
      Evaluate attire strictly based on fabric quality, neatness, formality, overall presentation, and visual impression of premium craftsmanship and material, including vibrant colors:
          - 0 points: Casual attire such as t-shirts, casual shirts, jeans, informal dresses, shorts, or visibly low-quality fabric.
          - 2 points: Semi-formal or semi-premium attire; moderately formal clothing with decent but not high-end fabric, including well-ironed shirts or kurtas in bright colors that still appear neat and presentable.
          - 4 points: Clearly premium attire with evident high-quality fabric and craftsmanship, such as:
              - Formal suits,
              - Crisp white premium-quality shirts,
              - Premium dhoti-kurta,
              - Silk/cotton premium sarees,
              - Well-tailored or designer wear with clear attention to detail, neatness, and fabric quality,
              - Vibrant-colored shirts (e.g., yellow) that appear made from premium fabric with excellent finish and presentation.

      2. Photo Quality Scoring Guidelines (1.5x weighted):
      Evaluate photo quality strictly based on clarity, lighting, composition, sharpness, and professionalism. Multiply your score by 1.5:
          - 0 points (Bad Quality): Blurry, grainy, poor lighting, distracting background.
          - 3 points (Average/Good Quality): Decent clarity, adequate lighting, reasonably clean background, but not professional-level.
          - 6 points (Professional/DSLR Quality): High clarity, professional lighting, sharp details, thoughtful background, studio-quality.

      3. Gold Status Scoring Guidelines (1.5x weighted):
      Evaluate the amount and visibility of gold jewelry (rings, chains, bracelets, necklaces) in the photo. Multiply your score by 1.5:
          - 0 points: No visible gold jewelry or only very minimal pieces (e.g., a thin single ring or chain that's barely noticeable).
          - 3 points: Moderate visible gold jewelry — for example, wearing one type of gold jewelry prominently (one chain, or one bracelet, or a couple of rings), but overall jewelry presence is limited.
          - 6 points: Prominent and multiple gold jewelry pieces clearly visible and substantial in appearance. This includes a combination of multiple rings, a thick or heavy chain, bracelets, and/or necklaces, all worn together in a way that the gold jewelry stands out strongly as a key visual element in the photo. If the user clearly wears more than one type of gold jewelry (for example, multiple rings + a noticeable chain + a bracelet), assign the full 6 points.

      Additionally, estimate the user's age based on their appearance in the photo.

      Provide your evaluation strictly in this JSON format:
      {
        "premium_dressing_score": integer,
        "premium_dressing_reason": string,
        "photo_quality_score": integer,
        "photo_quality_reason": string,
        "gold_status_score": integer,
        "gold_status_reason": string
        "total_score": integer (sum of above scores),
        "age": integer (estimated age of the user),
      }

      Do not deviate from this format.
    PROMPT
  end

  def build_user_prompt(photo_url)
    user_content = [{ type: "text", text: "Evaluate the following user profile photo using the scoring criteria provided, and estimate the user's age." }]
    user_content << { type: "image_url", image_url: { url: photo_url } }
  end

  def parse_and_store_results(user, response)
    content = response.dig("choices", 0, "message", "content")
    return unless content.present?

    begin
      # Handle both string and already parsed JSON content
      evaluation_data = content.is_a?(String) ? JSON.parse(content) : content

      # Calculate additional scores based on location and age
      location_score = calculate_location_score(user)
      age_score = calculate_age_score(evaluation_data["age"])

      # Calculate final total score including location and age scores
      final_total_score = evaluation_data["total_score"] + location_score + age_score

      # Store total score and age in UserMetadatum
      store_metadata(user, Constants.poster_photo_total_score_key, final_total_score)
      store_metadata(user, Constants.estimated_age_using_ai_key, evaluation_data["age"])
      Floww::UpdateLead.perform_async(user.id)

      # Log detailed feedback as warnings for monitoring
      log_detailed_feedback(user, evaluation_data)

      Rails.logger.warn("Poster photo without background evaluation completed for user #{user.id} - Base score: #{evaluation_data["total_score"]}, Location score: #{location_score}, Age score: #{age_score}, Final score: #{final_total_score}")
    rescue JSON::ParserError => e
      Honeybadger.notify(e, context: {
        user_id: user.id,
        content: content,
        response: response
      })
      Rails.logger.error("Failed to parse evaluation response for user #{user.id}: #{e.message}")
    end
  end

  def calculate_location_score(user)
    return 0 unless user.village_id.present?

    village = user.village
    return 0 unless village.present?

    case village.level.to_sym
    when :village
      1
    when :municipality
      2
    when :corporation
      3
    else
      0
    end
  end

  def calculate_age_score(estimated_age)
    return 0 unless estimated_age.present?

    age = estimated_age.to_i
    if age >= 35 && age <= 60
      4
    else
      0
    end
  end

  def store_metadata(user, key, value)
    metadatum = UserMetadatum.find_or_initialize_by(user_id: user.id, key: key)
    metadatum.value = value.to_s
    metadatum.save!
  end

  def log_detailed_feedback(user, evaluation_data)
    # Log each scoring category with its score and reason
    Rails.logger.warn("Poster photo without background evaluation feedback for user #{user.id} - Premium Dressing (Score: #{evaluation_data['premium_dressing_score']}): #{evaluation_data['premium_dressing_reason']}")
    Rails.logger.warn("Poster photo without background evaluation feedback for user #{user.id} - Photo Quality (Score: #{evaluation_data['photo_quality_score']}): #{evaluation_data['photo_quality_reason']}")
    Rails.logger.warn("Poster photo without background evaluation feedback for user #{user.id} - Gold Status (Score: #{evaluation_data['gold_status_score']}): #{evaluation_data['gold_status_reason']}")
  end
end 
