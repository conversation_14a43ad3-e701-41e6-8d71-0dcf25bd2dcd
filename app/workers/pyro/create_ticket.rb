# frozen_string_literal: true

module Pyro
  class CreateTicket
    include Sidekiq::Worker

    sidekiq_options queue: :pyro

    def perform(user_id, ticket_type, source, data = {})
      user = User.find_by(id: user_id)
      return if user.blank?

      # Extract additional data with default values
      rm_name = data[:rm_name] || ""
      layout_status = data[:layout_status] || ""
      subscription_status = data[:subscription_status] || ""
      is_user_atleast_paid_once = data[:is_user_atleast_paid_once] || false
      badge = data[:badge] || ""

      # Build payload according to Pyro API structure
      payload = {
        tenant_id: PyroApi::TENANT_ID,
        ticket_date: Time.zone.now.iso8601,
        user_id: user.id.to_s,
        name: user.name,
        phone: user.phone.to_s.start_with?('91') && user.phone.to_s.length > 10 ? "+#{user.phone}" : "+91#{user.phone}",
        source: source.humanize,
        subscription_status: subscription_status.humanize,
        atleast_paid_once: is_user_atleast_paid_once,
        reason: ticket_type.humanize,
        badge: badge.humanize,
        poster: user.get_subscription_status,
        layout_status: layout_status.humanize,
        rm_name: rm_name,
        praja_dashboard_user_link: "#{Constants.get_admin_host}/admin/users/#{user.hashid}",
        display_pic_url: user.photo.present? ? user.photo.url : ""
      }

      Pyro::SendApiRequest.perform_async(payload.to_json)
    end
  end
end
