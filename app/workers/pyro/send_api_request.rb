# frozen_string_literal: true

module Pyro
  class SendApiRequest
    include Sidekiq::Worker
    include Sidekiq::Throttled::Worker

    sidekiq_options queue: :pyro, retry: 1

    sidekiq_throttle(
      :threshold => { :limit => 5, :period => 1.second }
    )

    # @!method perform(payload)
    # This method is used to send API request to Pyro
    # @param payload [String] Request payload as JSON string
    def perform(payload)
      payload = JSON.parse(payload)
      start_time = Time.zone.now

      begin
        PyroApi.send_api_request(payload)
      rescue => e
        # Log timing before re-raising the error
        elapsed_time = Time.zone.now - start_time
        Rails.logger.warn("Time taken for Pyro send_api_request: #{elapsed_time} seconds")
        raise e
      else
        # Log timing for successful requests
        elapsed_time = Time.zone.now - start_time
        Rails.logger.warn("Time taken for Pyro send_api_request: #{elapsed_time} seconds")
      end
    end
  end
end
