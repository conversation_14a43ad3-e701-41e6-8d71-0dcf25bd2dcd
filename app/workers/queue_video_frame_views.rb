class QueueVideoFrameViews
  include Sidekiq::Worker
  sidekiq_options retry: 3, lock: :until_executed, on_conflict: :log

  def perform(user_id, video_frame_ids_timestamp)
    Honeybadger.context({user_id: user_id, video_frame_ids: video_frame_ids_timestamp})
    return if video_frame_ids_timestamp.blank? || user_id.blank?

    logger.debug "QueueVideoFrameViews: #{user_id} with frame ids: #{video_frame_ids_timestamp}"

    $redis.pipelined do |pipeline|
      video_frame_ids_timestamp.each do |video_frame_id, timestamp|
        video_frame_id = video_frame_id.to_i
        timestamp = timestamp.to_i

        if video_frame_id == 0 || timestamp == 0
          logger.error "QueueVideoFrameViews: video_frame_id or timestamp is 0"
          next
        end

        #Increase views count on video_frame_id
        pipeline.hincrby(Constants.video_frame_views_redis_key, video_frame_id, 1)

        #log user-date level video frame ids
        user_date_video_frame_views_queue = Constants.user_date_video_frame_views_queue_redis_key(user_id)
        pipeline.sadd(user_date_video_frame_views_queue, video_frame_id)
        pipeline.expireat(user_date_video_frame_views_queue, (Time.zone.now.end_of_day + 3.days).to_i)

        # add to video frame views queue
        pipeline.sadd(Constants.video_frame_views_queue_redis_key,
                      { video_frame_id: video_frame_id,
                        user_id: user_id,
                        viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)

      end
    end
  end
end
