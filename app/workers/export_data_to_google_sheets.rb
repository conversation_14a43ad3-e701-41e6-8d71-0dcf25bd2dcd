class ExportDataToGoogleSheets
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  sidekiq_options retry: 3, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    :concurrency => { :limit => 5 },
    :threshold => { :limit => 10, :period => 1.second }
  )

  def perform(user_id, data, spreadsheet_id)
    begin
      # save the user information into google xls
      service = Google::Apis::SheetsV4::SheetsService.new
      credentials_path = ENV['GOOGLE_CLOUD_CREDENTIALS']
      credentials = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: StringIO.new(credentials_path),
        scope: Google::Apis::SheetsV4::AUTH_SPREADSHEETS
      )

      service.authorization = credentials
      range = 'Sheet1!A1'

      value_range = Google::Apis::SheetsV4::ValueRange.new(values: data)

      service.append_spreadsheet_value(spreadsheet_id,
                                       range,
                                       value_range,
                                       value_input_option: 'USER_ENTERED')
    rescue => e
      Honeybadger.notify(e, context: { user_id: user_id, data: data, spreadsheet_id: spreadsheet_id })
    end
  end
end
