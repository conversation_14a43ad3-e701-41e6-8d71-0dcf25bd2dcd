class QueueVideoCreativeViews
  include Sidekiq::Worker
  sidekiq_options retry: 3, lock: :until_executed, on_conflict: :log

  def perform(user_id, video_creative_ids_timestamp)
    Honeybadger.context({user_id: user_id, video_creative_ids: video_creative_ids_timestamp})
    logger.debug "QueueVideoCreativeViews: #{user_id} #{video_creative_ids_timestamp}"

    $redis.pipelined do |pipeline|
      video_creative_ids_timestamp.each do |video_creative_id, timestamp|
        video_creative_id = video_creative_id.to_i
        timestamp = timestamp.to_i

        if video_creative_id == 0 || timestamp == 0
          logger.error "QueueVideoCreativeViews: video_creative_id or timestamp is 0"
          next
        end

        #Increase views count on video_creative_id
        pipeline.hincrby(Constants.video_creative_views_redis_key, video_creative_id, 1)

        #log user-date level video creative ids
        user_date_video_creative_views_queue = Constants.user_date_video_creative_views_queue_redis_key(user_id)
        pipeline.sadd(user_date_video_creative_views_queue, video_creative_id)
        pipeline.expireat(user_date_video_creative_views_queue, (Time.zone.now.end_of_day + 3.days).to_i)

        # add to video creative views queue
        pipeline.sadd(Constants.video_creative_views_queue_redis_key,
                      { video_creative_id: video_creative_id,
                        user_id: user_id,
                        viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S") }.to_json)

      end
    end
  end
end
