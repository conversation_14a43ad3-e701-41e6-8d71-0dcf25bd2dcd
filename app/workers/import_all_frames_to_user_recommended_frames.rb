class ImportAllFramesToUserRecommendedFrames
    include Sidekiq::Worker
    include Sidekiq::Throttled::Worker

    sidekiq_throttle(
      :threshold => { :limit => 1, :period => 2.seconds }
    )
    def perform(user_id)
      start_time = Time.zone.now
      user = User.find(user_id)
      frames = user.generate_auto_recommend_frames
      # import all frames to user_recommended_frames table with one query
      UserRecommendedFrame.import frames.map { |frame| UserRecommendedFrame.new(user_id: user.id, frame_id: frame[:id]) },
                                  on_duplicate_key_ignore: true if frames.present?

      elapsed_time = Time.zone.now - start_time
      Rails.logger.warn("Imported all frames to user recommended frames table in #{elapsed_time} sec")
    end
  end
