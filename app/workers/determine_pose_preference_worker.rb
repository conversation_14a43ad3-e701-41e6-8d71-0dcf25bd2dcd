# frozen_string_literal: true

class DeterminePosePreferenceWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options queue: :default, retry: 3, lock: :until_and_while_executing, on_conflict: :log

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    poster_creative_id = msg["args"].first
    Rails.logger.error("DeterminePosePreferenceWorker retries exhausted for poster_creative_id #{poster_creative_id}: #{ex.message}")
  end

  def perform(poster_creative_id)
    # todo: disabling this worker for production for now
    return if Rails.env.production?
    poster_creative = PosterCreative.find_by(id: poster_creative_id)

    creative_url = poster_creative.photo_v3&.url
    return unless creative_url.present?

    begin
      response = call_openai_api(creative_url)

      content = response.dig("choices", 0, "message", "content")
      unless content.present?
        Rails.logger.warn("No content in OpenAI response for poster_creative_id #{poster_creative_id}")
        raise StandardError, "No content in OpenAI response"
      end

      # Parse JSON response
      begin
        # Remove markdown code blocks if present
        json_content = content.gsub(/```json\n?/, '').gsub(/```\n?/, '').strip
        parsed_response = JSON.parse(json_content)
        pose_ranking = parsed_response["pose_ranking"]

        # Validate that we got a proper pose ranking array
        expected_poses_count = Constants.available_poses.length
        unless pose_ranking.is_a?(Array) && pose_ranking.length == expected_poses_count
          Rails.logger.warn("Invalid pose ranking format from OpenAI for poster_creative_id #{poster_creative_id}: expected #{expected_poses_count} poses, got #{pose_ranking}")
          raise StandardError, "Invalid pose ranking format: expected #{expected_poses_count} poses, got #{pose_ranking&.length || 0}"
        end

        # Validate that all poses in ranking are valid
        invalid_poses = pose_ranking - Constants.available_poses
        unless invalid_poses.empty?
          Rails.logger.warn("Invalid poses in ranking from OpenAI for poster_creative_id #{poster_creative_id}: #{invalid_poses}")
          raise StandardError, "Invalid poses in ranking: #{invalid_poses}"
        end

        # Validate that all available poses are included
        missing_poses = Constants.available_poses - pose_ranking
        unless missing_poses.empty?
          Rails.logger.warn("Missing poses in ranking from OpenAI for poster_creative_id #{poster_creative_id}: #{missing_poses}")
          raise StandardError, "Missing poses in ranking: #{missing_poses}"
        end

        update_pose_preference_order(poster_creative, pose_ranking)
        Rails.logger.info("Successfully updated pose preference for poster_creative_id #{poster_creative_id} with ranking: #{pose_ranking.join(',')}")
      rescue JSON::ParserError => e
        Rails.logger.warn("Failed to parse JSON response from OpenAI for poster_creative_id #{poster_creative_id}: #{e.message}")
        raise StandardError, "Failed to parse JSON response"
      end
    rescue StandardError => e
      Rails.logger.error("Error in DeterminePosePreferenceWorker for poster_creative_id #{poster_creative_id}: #{e.message}")
      raise e
    end
  end

  private

  def call_openai_api(creative_url)
    # Get the available poses dynamically from Constants
    poses_list = Constants.available_poses

    system_prompt = <<~PROMPT
      You are a pose-selection assistant. A poster creative image will be provided. Based solely on the visual and emotional tone of the creative, rank the following poses in order of suitability for the poster. If the creative has a clear political essence (such as political symbols, party branding, or leader references), prioritize poses with party scarfs.

      Available poses to rank:
      #{poses_list.map { |pose| "- #{pose}" }.join("\n")}

      Return your response **strictly in the following JSON format**—no explanation, no additional text:

      ```json
      {
        "pose_ranking": [
          "#{poses_list.first}",
          "#{poses_list.second}",
          "#{poses_list.third}"
        ]
      }
      ```

      Replace the pose order based on your judgment for each input image. The list must contain ALL #{poses_list.length} pose options from the available poses above, ranked from most to least suitable. Do not omit any pose or alter the key `"pose_ranking"`.
    PROMPT

    user_prompt = [
      {
        "type": "text",
        "text": "Analyze the uploaded image and rank all the available poses in order of suitability."
      },
      {
        "type": "image_url",
        "image_url": {
          "url": creative_url
        }
      }
    ]

    uri = URI(Constants.openai_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri)
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{Rails.application.credentials.openai_api_key_for_studio}"

    request.body = {
      model: "gpt-4o",
      messages: [
        { role: "system", content: system_prompt },
        { role: "user", content: user_prompt }
      ],
    }.to_json

    response = http.request(request)

    if response.code.to_i != 200
      raise StandardError, "OpenAI API request failed with status #{response.code}: #{response.body}"
    end

    JSON.parse(response.body)
  end

  def update_pose_preference_order(poster_creative, pose_ranking)
    poster_creative.update!(pose_preference_order: pose_ranking.join(','))
  end
end 
