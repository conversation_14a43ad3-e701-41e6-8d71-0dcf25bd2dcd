# frozen_string_literal: true
class InitiateWatiCampaignUsingCsv
  include Sidekiq::Worker

  # TODO: In future, if we are changing the timings of this cron, we should check whether the user has activated a subscription here as well.
  def perform(campaign_name, metadata_key)
    initialize_variables(campaign_name, metadata_key)

    fetch_and_populate_umd_from_user_metadata
    return if @users_master_data.empty?

    @user_ids = @users_master_data.keys

    populate_creatives_master_data

    set_user_data
    set_event_data

    decide_and_activate_offer_per_user unless metadata_key == Constants.trial_activation_wati_campaign_count
    set_template
    set_cta_link

    send_bulk_api_per_template

    send_mixpanel_events

    update_trigger_counts
    import_data_in_user_wati_campaigns
    cleanup_data
  end

  private

  def initialize_variables(campaign_name, metadata_key)
    @current_time = Time.zone.now
    @campaign_name = campaign_name
    @metadata_key = metadata_key
    @creative_id_metadata_key = @campaign_name + "_creative_id"

    @user_ids = []
    @users_master_data = {}
    # {
    #   user_id: 123,
    #   creative_id: 11,
    #   poster_image_url: 'asdf',
    #   trigger_count: 0,
    #   name: 'asfasdf',
    #   phone: 9999999999,
    #   amount: 1 / 299,
    #   plan_amount: 299,
    #   event_id: 1111,
    #   event_name: 'ugadi',
    #   template: 'template_name',
    #   cta_link: 'ldkajs/adsfsaf'
    #   broadcast_name: 'template_name_24_05_2025_2',
    #   priority_of_event: 'high'
    # }

    @creatives_master_data = {}
    # {
    #   event_id: creative.event_id,
    #   event_name: creative.event.name,
    #   event_type: get_event_type(creative.event),
    #   deep_link: get_deep_link_for_event_and_creative(creative.event_id, creative.id),
    # }

    ## When a new campaign is being setup, add templates in @master_templates, and update the switch case below.
    @master_templates = {
      trial_activation: {
        # Today event templates
        today_template: "today_high_event_for_trail_activation_campaign",

        # Tomorrow event templates
        tomorrow_template: "tomorrow_high_event_for_trail_activation_campaign",
      },
      premium_reactivation: {
        # Today event templates
        today_template: "today_high_event_for_reactivation",
        today_template_rs_1: "today_high_event_campaign_reactivation_with_rs_1",

        # Tomorrow event templates
        tomorrow_template: "tomorrow_high_event_for_re_activation",
        tomorrow_template_rs_1: "tomorrow_high_event_campaign_reactivation_with_rs_1",

        # Upcoming event templates
        upcoming_template: "upcoming_high_event_for_re_activation",
        upcoming_template_rs_1: "upcoming_high_event_campaign_reactivation_with_rs_1",
      }
    }

    case @metadata_key
    when Constants.trial_activation_wati_campaign_count
      @mixpanel_event_name = "wati_trial_activation_campaign_triggered_backend"
      @template_key = "trial_activation"
      @import_campaign_type = UserWatiCampaign.campaign_types[:trial_activation]
    when Constants.premium_reactivation_wati_campaign_count
      @mixpanel_event_name = "wati_reactivation_campaign_triggered_backend"
      @template_key = "premium_reactivation"
      @import_campaign_type = UserWatiCampaign.campaign_types[:subscription_reactivation]
    end
  end

  def fetch_and_populate_umd_from_user_metadata
    ### Get the users for whom we need to send campaign.
    # And, the initial data points of creative_id, poster_image_url, trigger_count
    UserMetadatum
      .where(key: [@creative_id_metadata_key, Constants.poster_image_url(@campaign_name), @metadata_key])
      .find_each(batch_size: 1000) do |um|
      user_id = um.user_id
      unless @users_master_data[user_id].present?
        @users_master_data[user_id] = {
          user_id: user_id,
          creative_id: nil,
          poster_image_url: nil,
          trigger_count: nil,
        }
      end

      if um.key == @creative_id_metadata_key
        @users_master_data[user_id][:creative_id] = um.value.to_i
      elsif um.key == Constants.poster_image_url(@campaign_name)
        @users_master_data[user_id][:poster_image_url] = um.value
      elsif um.key == @metadata_key
        @users_master_data[user_id][:trigger_count] = um.value.to_i
      end
    end

    # Remove users who do not have creative_id
    @users_master_data.delete_if { |_, umd| umd[:creative_id].blank? }

    # Remove users who do not have poster_image_url
    @users_master_data.delete_if { |_, umd| umd[:poster_image_url].blank? }

    # Remove users who do not have trigger_count
    @users_master_data.delete_if { |_, umd| umd[:trigger_count].blank? }
  end

  def set_user_data
    @user_ids.each_slice(100) do |user_ids_batch|
      users_info = User.joins("LEFT JOIN user_plans on user_plans.user_id = users.id")
                       .where(id: user_ids_batch)
                       .pluck(:id, :name, :phone, :amount)

      users_info.each do |user_info|
        user_id = user_info[0]
        @users_master_data[user_id][:name] = user_info[1]
        @users_master_data[user_id][:phone] = user_info[2]
        @users_master_data[user_id][:amount] = user_info[3].to_i
        @users_master_data[user_id][:plan_amount] = user_info[3].to_i
      end
    end
  end

  def decide_and_activate_offer_per_user
    rs_1_offer_expire_time = @current_time.end_of_day.advance(days: 100).to_i

    @user_ids.each_slice(100) do |user_ids_batch|
      redis_zadd_commands = []
      rs_1_user_ids = SubscriptionCharge.where(charge_amount: 1, status: :success, user_id: user_ids_batch)
                                        .where("success_at > ?", 6.months.ago.beginning_of_day)
                                        .pluck(:user_id)

      user_ids_batch.each do |user_id|
        unless rs_1_user_ids.include?(user_id)
          redis_zadd_commands << [Constants.premium_1_rs_user_redis_key, rs_1_offer_expire_time, user_id]
          @users_master_data[user_id][:amount] = 1
        end
      end

      $redis.pipelined do |pipeline|
        redis_zadd_commands.each do |key, score, uid|
          pipeline.zadd(key, score, uid)
        end
      end
    end
  end

  def populate_creatives_master_data
    creative_ids = @users_master_data.values.map { |c| c[:creative_id] }
    PosterCreative.includes(:event).where(id: creative_ids).each do |creative|
      @creatives_master_data[creative.id] = {
        event_id: creative.event_id,
        event_name: creative.event.name,
        priority_of_event: creative.event.priority,
        event_type: get_event_type(creative.event),
        deep_link: get_deep_link_for_event_and_creative(creative.event_id, creative.id),
      }
    end
  end

  def set_event_data
    @users_master_data.each do |_, umd|
      umd[:event_id] = @creatives_master_data[umd[:creative_id]][:event_id]
      umd[:event_name] = @creatives_master_data[umd[:creative_id]][:event_name]
      umd[:priority_of_event] = @creatives_master_data[umd[:creative_id]][:priority_of_event]
      umd[:event_type] = @creatives_master_data[umd[:creative_id]][:event_type]
    end
  end

  def set_template
    @users_master_data.each do |_, umd|
      umd[:template] = get_template_name(
        umd[:event_type],
        umd[:amount] == 1,
      )
    end
  end

  def set_cta_link
    @users_master_data.each do |_, umd|
      umd[:cta_link] = umd[:event_type] == 'today' ?
                         @creatives_master_data[umd[:creative_id]][:deep_link] :
                         get_premium_experience_screen_deep_link
      umd[:cta_link] = umd[:cta_link].gsub('https://prajaapp.sng.link/A3x5b/', '')
    end
  end

  def organise_data_based_on_template
    template_based_data = {}
    # {
    #   'template_name_1': [umd1, umd2]
    #   'template_name_2': [umd11, umd21]
    # }

    @users_master_data.each do |_, umd|
      unless template_based_data[umd[:template]].present?
        template_based_data[umd[:template]] = []
      end

      template_based_data[umd[:template]] << umd
    end

    template_based_data
  end

  def send_bulk_api_per_template
    organise_data_based_on_template.each do |template_name, users_data|
      users_data.each_slice(50).with_index do |users_data_batch, batch_index|
        broadcast_name = "#{template_name}_#{@current_time.strftime("%Y_%m_%d")}_#{batch_index + 1}"
        receivers_data = {}
        users_data_batch.each do |ud|
          ud[:broadcast_name] = broadcast_name
          phone = "91#{ud[:phone]}"
          receivers_data[phone] = {
            name: ud[:name],
            poster_image_url: ud[:poster_image_url],
            event_name: ud[:event_name],
            poster_link: ud[:cta_link],
            amount: ud[:plan_amount].to_s
          }
        end

        json_data = receivers_data.to_json
        $redis.hset(Constants.wati_campaigns_redis_key, broadcast_name, json_data)
        SendWhatsappMessagesUsingBulkApi.perform_async(
          template_name,
          broadcast_name
        )
      end
    end
  end

  # def generate_upload_send_csv_per_template
  #   csv_header = ["Name", "CountryCode", "Phone", "AllowBroadcast", "AllowSMS", "poster_image_url", "event_name", "poster_link", "amount"]
  #
  #   organise_data_based_on_template.each do |template_name, users_data|
  #     users_data.each_slice(50).with_index do |users_data_batch, batch_index|
  #       broadcast_name =  "#{template_name}_#{@current_time.strftime("%Y_%m_%d")}_#{batch_index + 1}"
  #       users_data_batch.each{ |ud| ud[:broadcast_name] = broadcast_name }
  #       rows = users_data_batch.map do |ud|
  #         [
  #           ud[:name],
  #           "91",
  #           ud[:phone],
  #           "TRUE",
  #           "FALSE",
  #           ud[:poster_image_url],
  #           ud[:event_name],
  #           ud[:cta_link],
  #           ud[:plan_amount]
  #         ]
  #       end
  #
  #       csv_file = CSV.generate(headers: true) do |csv|
  #         csv << csv_header
  #         rows.each { |row| csv << row }
  #       end
  #
  #
  #       SendWhatsappMsgUsingCsv..perform_async(
  #         template_name,
  #         broadcast_name,
  #         upload_csv_to_s3(csv_file, template_name, batch_index)
  #       )
  #     end
  #   end
  # end

  def send_mixpanel_events
    # NOTE: Assuming +1 to the trigger_count of users_master_data
    @users_master_data.each do |user_id, umd|
      event_properties = {
        event_id: umd[:event_id],
        priority_of_event: umd[:priority_of_event],
        creative_id: umd[:creative_id],
        broadcast_name: umd[:broadcast_name],
        trigger_count: umd[:trigger_count] + 1,
      }

      if @mixpanel_event_name == "wati_reactivation_campaign_triggered_backend"
        event_properties[:amount] = umd[:amount]
        event_properties[:plan_amount] = umd[:plan_amount]
      end

      EventTracker.perform_async(user_id, @mixpanel_event_name, event_properties)
    end
  end

  def update_trigger_counts
    @user_ids.each_slice(100) do |user_ids_batch|
      UserMetadatum.where(user_id: user_ids_batch,
                          key: @metadata_key)
                   .update_all("value = COALESCE(value, 0) + 1")
    end
  end

  def cleanup_data
    @user_ids.each_slice(100) do |user_ids_batch|
      UserMetadatum.where(user_id: user_ids_batch,
                          key: [@creative_id_metadata_key, Constants.poster_image_url(@campaign_name)])
                   .delete_all
    end
  end

  def get_event_type(event)
    if event.start_time < @current_time
      'today'
    elsif event.start_time < (@current_time + 1.day).end_of_day
      'tomorrow'
    else
      'upcoming'
    end
  end

  def get_template_name(event_type, is_rs_1_offer)
    key_name = "#{event_type}_template"
    key_name += "_rs_1" if is_rs_1_offer
    @master_templates[@template_key.to_sym][key_name.to_sym]
  end

  def get_deep_link_for_event_and_creative(event_id, creative_id)
    deeplink_uri = URI.parse("praja://buzz.praja.app/posters/layout?id=#{event_id}&creative_id=#{creative_id}")
    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/l0ft')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s })

    link = link_uri.to_s
    Singular.shorten_link(link)
  end

  def get_premium_experience_screen_deep_link
    deeplink_uri = URI.parse("praja://buzz.praja.app/premium-experience?payment-sheet=true")
    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/l0ft')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s })

    link = link_uri.to_s
    Singular.shorten_link(link, 1.month)
  end

  def upload_csv_to_s3(csv_file, template_name, batch_index)
    resource = Aws::S3::Resource.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      ))

    hashed_file_name = "campaign-#{batch_index}-at-#{@current_time.strftime("%Y-%m-%d_%H:%m:%s")}.csv"

    s3_object_path = "#{Rails.env}/wati/#{@template_key}/#{template_name}/#{@current_time.strftime("%Y-%m-%d")}/#{hashed_file_name}"

    checksum = Digest::MD5.base64digest(csv_file)

    object = resource.bucket(Constants.aws_s3_bucket_name_for_csvs)
                     .object(s3_object_path)
    object.put(body: csv_file, content_md5: checksum)

    s3_object_path
  end

  def import_data_in_user_wati_campaigns
    user_wati_campaign_data = []
    @users_master_data.each do |user_id, umd|
      campaign_data = {
        creative_id: umd[:creative_id],
        event_id: umd[:event_id],
        event_name: umd[:event_name],
        priority_of_event: umd[:priority_of_event],
        poster_image_url: umd[:poster_image_url],
        trigger_count: umd[:trigger_count],
        amount: umd[:amount],
        plan_amount: umd[:plan_amount],
        cta_link: umd[:cta_link]
      }
      user_wati_campaign_data << UserWatiCampaign.new(
        user_id: user_id,
        broadcast_name: umd[:broadcast_name],
        campaign_data: campaign_data,
        campaign_date: Time.zone.now,
        campaign_type: @import_campaign_type,
        status: UserWatiCampaign.statuses[:triggered],
        template_name: umd[:template]
      )
    end
    UserWatiCampaign.import(user_wati_campaign_data, batch_size: 100)
  end
end
