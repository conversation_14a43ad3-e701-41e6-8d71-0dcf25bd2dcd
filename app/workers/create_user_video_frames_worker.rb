# frozen_string_literal: true

class CreateUserVideoFramesWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Sidekiq::AssuredJobs::Worker
  include IdentityPhotoGeneration

  sidekiq_options queue: :video_posters_generation, retry: 3, lock: :until_and_while_executing, on_conflict: :log

  sidekiq_throttle(
    :threshold => { :limit => 2, :period => 1.second }
  )
  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("CreateUserVideoFramesWorker retries exhausted: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })

    user = User.find_by_id(user_id)
    return if user.blank?

    Rails.logger.info("Creating user video frames for user #{user_id}")

    # Get all active video frames
    video_frames = VideoFrame.where(active: true)
    return if video_frames.empty?

    identity_photos_map = {}
    #generate all the identity photos first
    video_frames.each do |video_frame|
      begin
        existing_frame = UserVideoFrame.find_by(user: user, video_frame: video_frame, active: true)
        next if existing_frame.present?

        identity_photo_url = generate_identity_image(user, video_frame)
        identity_photos_map[video_frame] = identity_photo_url

      rescue StandardError => e
        #exit early dont create partial user video frames
        Honeybadger.notify(e, context: { user_id: user_id, video_frame_id: video_frame.id })
        Rails.logger.error("Failed to generate identity photo for user #{user_id}, video_frame #{video_frame.id}: #{e.message}")
        return
      end
    end

    created_frames = []

    if identity_photos_map.any?
      records = identity_photos_map.map do |video_frame, identity_photo_url|
        {
          user_id: user.id,
          video_frame_id: video_frame.id,
          identity_photo_url: identity_photo_url,
          active: true
        }
      end

      begin
        UserVideoFrame.insert_all!(records)
        Rails.logger.info("Successfully created video frames with identity photos for user #{user_id}")
        user.deactivate_existing_user_video_posters
      rescue StandardError => e
        Honeybadger.notify(e, context: { user_id: user_id })
        Rails.logger.error("Bulk creation failed for UserVideoFrames for user #{user_id}: #{e.message}")
      end
    end

    if created_frames.any?
      Rails.logger.info("Successfully created #{created_frames.count} video frames with identity photos for user #{user_id}")
      # Update user_video_poster with active: false (following existing pattern)
      user.deactivate_existing_user_video_posters
    end
  end

  # All identity photo generation methods are now provided by the IdentityPhotoGeneration concern
end
