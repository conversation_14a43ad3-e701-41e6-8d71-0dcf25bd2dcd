module MediaService
  class PosterPhotoBgRemovalProcessorWorker
    include Sidekiq::Worker
    include Sidekiq::AssuredJobs::Worker

    sidekiq_options retry: 3, lock: :until_and_while_executing, on_conflict: :reject

    sidekiq_retries_exhausted do |msg, ex|
      Honeybadger.notify(ex, context: { args: msg["args"] })
      Rails.logger.error("PosterPhotoBgRemovalProcessorWorker retries exhausted: #{ex.message}")
    end

    def perform(photo_id, key, user_id, auto_assign_to_layout = false)
      return if key.blank?

      keys = User::BG_REMOVAL_POSTER_PHOTO_KEYS
      return nil unless keys.include?(key)

      photo = Photo.find_by(id: photo_id)
      raise PhotoNotFoundError.new(photo_id) if photo.blank?

      response = send_photo_for_bg_removal(photo, user_id)
      raise BackgroundRemovalFailed.new(photo.id, response) unless response.code.to_i == 201

      bg_removed_photo = save_processed_photo(response, user_id)
      # save in user metadatum along with that key
      save_in_user_metadata(bg_removed_photo, key, user_id)

      if auto_assign_to_layout
        user = User.find_by(id: user_id)
        if key == Constants.poster_photo_without_background_key
          user.update(poster_photo: bg_removed_photo)
        elsif key == Constants.hero_frame_photo_key
          user.update(hero_frame_photo: bg_removed_photo)
        elsif key == Constants.family_frame_photo_key
          user.update(family_frame_photo: bg_removed_photo)
        end
      end
    rescue StandardError => e
      case e
      when BackgroundRemovalNetworkError
        raise
      when BackgroundRemovalFailed
        raise
      else
        raise
      end
    end

    private

    def send_photo_for_bg_removal(photo, user_id)
      uri = URI(Constants.get_photo_bg_removal_url_for_internal_services)

      token = JsonWebToken.encode({ user_id: user_id, created_at: Time.zone.now.to_i })
      request = Net::HTTP::Post.new(uri)
      request["Authorization"] = "Bearer #{token}"

      photo_file = nil
      begin
        photo_file = URI.open(photo.compressed_url(size: 1440, quality: 100))
        # Detect content type or use a default
        content_type = detect_content_type(photo) || 'image/jpeg'

        form_data = [['photo', photo_file, {
          filename: File.basename(photo.url),
          content_type: content_type
        }]]
        request.set_form(form_data, 'multipart/form-data')

        Net::HTTP.start(uri.host, uri.port,
                        use_ssl: uri.scheme == 'https',
                        open_timeout: 10,
                        read_timeout: 180
        ) do |http|
          http.request(request)
        end
      ensure
        photo_file&.close if photo_file.respond_to?(:close)
      end
    rescue => e
      raise BackgroundRemovalNetworkError.new(photo.id, e)
    end

    def save_processed_photo(response, user_id)
      parsed_response = JSON.parse(response.body, symbolize_names: true)

      processed_photo = Photo.new(
        ms_data: parsed_response,
        user_id: user_id,
        service: parsed_response[:service]
      )
      if processed_photo.save!
        processed_photo
      end
    end

    def detect_content_type(photo)
      # Extract content type from metadata or filename extension
      return 'image/jpeg' if photo.url.to_s.end_with?('.jpg', '.jpeg')
      return 'image/png' if photo.url.to_s.end_with?('.png')
      return 'image/gif' if photo.url.to_s.end_with?('.gif')
      nil
    end

    def save_in_user_metadata(photo, key, user_id)
      um = UserMetadatum.find_or_create_by(user_id: user_id, key: key)
      um.update(value: photo.id) if um.present? && um.value != photo.id.to_s
    end
  end
end
