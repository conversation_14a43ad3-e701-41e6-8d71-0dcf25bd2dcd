# frozen_string_literal: true
module MediaService
  class AddAndSaveGreyBgImageWorker
    include Sidekiq::Worker
    include Sidekiq::Throttled::Worker
    include Sidekiq::AssuredJobs::Worker
    include Capture

    sidekiq_options queue: :critical, retry: 3, lock: :until_and_while_executing, on_conflict: :log

    sidekiq_throttle(
      concurrency: { limit: 5 },
      threshold: { limit: 10, period: 10.seconds }
    )

    sidekiq_retries_exhausted do |msg, ex|
      user_id = msg["args"].first
      Honeybadger.notify(ex, context: { 
        worker: 'AddAndSaveGreyBgImageWorker',
        user_id: user_id,
        args: msg["args"] 
      })
      Rails.logger.error("AddAndSaveGreyBgImageWorker retries exhausted for user #{user_id}: #{ex.message}")
    end

    def perform(user_id)
      Honeybadger.context({ user_id: user_id })
      Rails.logger.info("AddAndSaveGreyBgImageWorker: Starting processing for user #{user_id}")

      begin
        # Validate user exists
        user = User.find_by_id(user_id)
        unless user
          Rails.logger.warn("AddAndSaveGreyBgImageWorker: User not found with id #{user_id}")
          return
        end

        # Check if user has poster_photo
        unless user.poster_photo.present?
          Rails.logger.info("AddAndSaveGreyBgImageWorker: User #{user_id} has no poster_photo, skipping")
          return
        end

        # Get poster photo URL
        photo_url = user.poster_photo.placeholder_url
        unless photo_url.present?
          Rails.logger.warn("AddAndSaveGreyBgImageWorker: User #{user_id} poster_photo has no placeholder_url, skipping")
          return
        end

        # Generate HTML with grey background
        html = generate_html(photo_url)

        # Capture HTML as image
        Rails.logger.info("AddAndSaveGreyBgImageWorker: Capturing HTML as image for user #{user_id}")
        captured_image = capture_html_as_image(html, '#grey-container')

        unless captured_image['cdn_url'].present?
          raise "Capture service did not return cdn_url for user #{user_id}: #{captured_image}"
        end

        # Create new Photo record
        new_photo = create_photo_from_url(user, captured_image['cdn_url'])

        # Update user fields
        update_user_photo_fields(user, new_photo)

        Rails.logger.info("AddAndSaveGreyBgImageWorker: Successfully processed user #{user_id}, created photo #{new_photo.id}")

      rescue StandardError => e
        Honeybadger.notify(e, context: { user_id: user_id })
        Rails.logger.error("AddAndSaveGreyBgImageWorker: Error processing user #{user_id}: #{e.message}")
        raise e
      end
    end

    private

    def generate_html(photo_url)
      <<~HTML
        <div id="outer-container" style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%;">
          <div id="grey-container" style="background-color: #808080; display: inline-block;">
            <img src="#{photo_url}" style="max-width: 100%; max-height: 100%; object-fit: contain;" />
          </div>
        </div>
      HTML
    end

    def create_photo_from_url(user, image_url)
      Rails.logger.info("AddAndSaveGreyBgImageWorker: Creating Photo record for user #{user.id}")

      photo = Photo.new(
        url: image_url,
        user_id: user.id,
        explicit: false,
        service: 'aws'
      )

      unless photo.save!
        raise "Failed to create Photo record for user #{user.id}: #{photo.errors.full_messages.join(', ')}"
      end

      Rails.logger.info("AddAndSaveGreyBgImageWorker: Created Photo #{photo.id} for user #{user.id}")
      photo
    end

    def update_user_photo_fields(user, new_photo)
      Rails.logger.info("AddAndSaveGreyBgImageWorker: Updating user fields for user #{user.id}")

      if user.poster_photo_with_background.nil?
        # Always update poster_photo_with_background
        user.update!(poster_photo_with_background: new_photo)
        Rails.logger.info("AddAndSaveGreyBgImageWorker: Updated poster_photo_with_background for user #{user.id}")
      end

      # Conditionally update photo field only if currently nil
      if user.photo.nil?
        user.update!(photo: new_photo)
        Rails.logger.info("AddAndSaveGreyBgImageWorker: Updated photo field for user #{user.id}")
      else
        Rails.logger.info("AddAndSaveGreyBgImageWorker: User #{user.id} already has photo, skipping photo field update")
      end
    end
  end
end
