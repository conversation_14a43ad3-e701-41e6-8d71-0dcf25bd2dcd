# frozen_string_literal: true

class PosterPhotoSuitabilityEvaluatorWorker
  include Sidekiq::Worker

  sidekiq_options queue: :default, retry: 3, lock: :until_and_while_executing, on_conflict: :log

  sidekiq_retries_exhausted do |msg, ex|
    user_id = msg["args"]&.first
    Honeybadger.notify(ex, context: {
      args: msg["args"],
      user_id: user_id,
      worker_class: msg["class"],
      error_class: ex.class.name,
      error_message: ex.message
    })
    Rails.logger.error("PosterPhotoSuitabilityEvaluatorWorker retries exhausted for user #{user_id}: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })

    user = User.find_by(id: user_id)
    return unless user&.poster_photo_with_background&.url.present?

    photo_url = user.poster_photo_with_background.url

    begin
      response = evaluate_poster_photo_suitability(photo_url, user.name)
      # Log the API response for debugging
      Rails.logger.warn("PosterPhotoSuitabilityEvaluatorWorker API response for user #{user_id}: #{response}")

      parse_and_store_results(user, response)
    rescue StandardError => e
      Rails.logger.error("PosterPhotoSuitabilityEvaluatorWorker failed for user #{user_id}: #{e.message}")
      raise e # Re-raise to trigger Sidekiq retry mechanism
    end
  end

  private

  def evaluate_poster_photo_suitability(photo_url, name)
    system_prompt = <<~PROMPT
      You are a strict image suitability evaluator for a face generation pipeline. Given an image and optional name, assess it for generation suitability by extracting the following attributes. Return a valid JSON object with the results only — no explanation or formatting.

      Criteria:
      1. is_human (bool): Is the primary subject a human?
      2. single_subject (bool): Is only one human clearly visible?
      3. gender ("male" | "female" | "uncertain"): Gender presentation of the main subject.
      4. clear_face (bool): Face is fully visible — no sunglasses, masks, heavy shadows, blur, etc.
      5. religion_from_name (string | null): Try to infer religion from name, e.g., "Hindu", "Muslim", "Christian" or return null if unknown.
      6. religion_from_photo (string | null): Infer religion based on visible visual cues like attire, symbols, accessories, etc., if possible.
      7. suitable_for_generation (bool): This must be true only if:
         - is_human is true
         - single_subject is true
         - clear_face is true
         - Gender is Male/Female

      Output only the following JSON structure:
      {
        "is_human": true,
        "single_subject": true,
        "gender": "male",
        "clear_face": true,
        "religion_from_name": "Hindu",
        "religion_from_photo": "Hindu",
        "suitable_for_generation": true
      }
    PROMPT

    user_prompt = [
      {
        "type": "text",
        "text": "Analyze the uploaded image.\nName: #{name}"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": photo_url
        }
      }
    ]

    uri = URI(Constants.openai_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri)
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{Rails.application.credentials.openai_api_key_for_studio}"

    request.body = {
      model: "gpt-4o",
      messages: [
        { role: "system", content: system_prompt },
        { role: "user", content: user_prompt }
      ],
    }.to_json

    response = http.request(request)

    if response.code.to_i != 200
      raise StandardError, "OpenAI API request failed with status #{response.code}: #{response.body}"
    end

    JSON.parse(response.body)
  end

  def parse_and_store_results(user, response)
    content = response.dig("choices", 0, "message", "content")
    return unless content.present?

    begin
      # Handle both string and already parsed JSON content
      evaluation_data = content.is_a?(String) ? JSON.parse(content) : content

      # Store suitable_for_generation and gender in UserMetadatum if they're not nil
      suitable_for_generation = evaluation_data["suitable_for_generation"]
      if suitable_for_generation.present?
        store_metadata(user, Constants.poster_photo_suitability_for_generation_key, suitable_for_generation)
      end

      gender = evaluation_data["gender"]
      if gender.present?
        store_metadata(user, Constants.determine_gender_from_poster_photo_key, gender)
      else
        # notify honeybadger if gender is not present
        Honeybadger.notify("Gender not evaluated for user poster photo", context: { user_id: user.id })
        return
      end

      # Log all evaluation data for monitoring and debugging
      Rails.logger.warn("Poster photo suitability evaluation completed for user #{user.id} - " \
                          "is_human: #{evaluation_data['is_human']}, " \
                          "single_subject: #{evaluation_data['single_subject']}, " \
                          "gender: #{evaluation_data['gender']}, " \
                          "clear_face: #{evaluation_data['clear_face']}, " \
                          "religion_from_name: #{evaluation_data['religion_from_name']}, " \
                          "religion_from_photo: #{evaluation_data['religion_from_photo']}, " \
                          "suitable_for_generation: #{evaluation_data['suitable_for_generation']}")
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse poster photo suitability evaluation response for user #{user.id}: #{e.message}")
      raise e # Re-raise to trigger Sidekiq retry mechanism
    end
  end

  def store_metadata(user, key, value)
    user_metadata = UserMetadatum.find_or_initialize_by(user: user, key: key)
    user_metadata.value = value.to_s
    user_metadata.save!
  end
end 
