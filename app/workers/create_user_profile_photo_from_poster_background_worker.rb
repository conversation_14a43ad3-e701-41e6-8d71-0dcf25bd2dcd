# frozen_string_literal: true

class CreateUserProfilePhotoFromPosterBackgroundWorker
  include Sidekiq::Worker
  
  sidekiq_options retry: 3, queue: :default
  
  sidekiq_retries_exhausted do |msg, ex|
    user_id = msg["args"].first
    Honeybadger.notify(ex, context: { 
      worker: 'CreateUserProfilePhotoFromPosterBackgroundWorker',
      user_id: user_id,
      args: msg["args"] 
    })
    Rails.logger.error("CreateUserProfilePhotoFromPosterBackgroundWorker retries exhausted for user #{user_id}: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })
    
    # Validate user exists (use find_by to avoid exceptions)
    user = User.find_by(id: user_id)
    unless user
      Rails.logger.warn("CreateUserProfilePhotoFromPosterBackgroundWorker: User not found with id #{user_id}")
      return
    end

    # Check user has no existing profile photo
    if user.photo.present?
      Rails.logger.info("CreateUserProfilePhotoFromPosterBackgroundWorker: User #{user_id} already has profile photo, skipping")
      return
    end


    return unless user.poster_photo_with_background.present?
    new_photo = Photo.new(url: user.poster_photo_with_background.url,
                          user_id: user.id,
                          explicit: false,
                          service: 'aws')
    return unless new_photo.save!
    #notify if new photo is not having path
    Honeybadger.notify("New photo created but path is blank for user #{user_id}") unless new_photo.path.present?
    user.update(photo: new_photo)
  end
end
