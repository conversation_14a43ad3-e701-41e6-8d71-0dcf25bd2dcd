# frozen_string_literal: true

class SendChargeToPg
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Sidekiq::AssuredJobs::Worker

  sidekiq_options queue: :payments, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: { limit: 3 },
    # Allow 2.5 jobs/sec to be processed for Cashfree and 5/sec for all others
    threshold: {
      limit: ->(subscription_charge_id, payment_gateway) { payment_gateway == 'cashfree' ? 2 : 5 },
      period: 1.seconds,
      key_suffix: ->(subscription_charge_id, payment_gateway) { payment_gateway }
    }
  )

  def perform(subscription_charge_id, payment_gateway)
    Rails.logger.warn("SendChargeToPg worker started for subscription_charge_id: #{subscription_charge_id} payment_gateway: #{payment_gateway}")
    start_time = Time.zone.now
    return if subscription_charge_id.blank?

    subscription_charge = SubscriptionCharge.find_by(id: subscription_charge_id)
    return if subscription_charge.blank?

    begin
      subscription_charge.mark_as_sent_to_pg!
    rescue => exception
      Honeybadger.notify(exception, context: { id: subscription_charge.id })
      return
    end

    # if success
    #   # If this is the first charge on autopay, send a message
    #   first_autopay_charge = SubscriptionCharge.where(subscription_id: subscription_charge.subscription_id)
    #                                            .where("charge_amount > 1").first
    #   if first_autopay_charge.present? && first_autopay_charge.id == subscription_charge.id
    #     begin
    #       user = User.find(subscription_charge.user_id)
    #       # user.send_charge_message(subscription_charge)
    #     rescue => exception
    #       Honeybadger.notify(exception, context: { id: subscription_charge.id })
    #     end
    #   end
    # end
    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("SendChargeToPg worker completed for subscription_charge_id: #{subscription_charge_id} payment_gateway: #{payment_gateway} send_charge_to_pg_worker_runtime: #{elapsed_time} seconds")
  end
end
