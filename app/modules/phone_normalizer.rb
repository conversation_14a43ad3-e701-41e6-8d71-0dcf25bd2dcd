module PhoneNormalizer
  extend ActiveSupport::Concern

  private

  def normalize_phone_number(phone)
    # Check if current admin user is in the test agent partners Redis set
    is_test_user = current_admin_user&.id.present? && 
                   $redis.sismember(Constants.test_agent_partners_redis_key, current_admin_user.id.to_s)
    
    return phone if is_test_user
    return nil if phone.blank?
    
    begin
      phone_object = TelephoneNumber.parse(phone, :IN)
      if !phone_object.valid?([:mobile])
        return nil
      end

      str = phone_object.national_number(formatted: false)
      str = str[1..] if str.start_with?('0')            # strip trunk '0' only
      str.to_i
    rescue StandardError
      nil
    end
  end
end
