class UserRecommendedFrame < ApplicationRecord
  belongs_to :user, -> { where active: true }
  belongs_to :frame, -> { where active: true }
  validates :user_id, uniqueness: { scope: :frame_id }

  def self.recommended_frames(user:, exclude_frame_ids: [])
    recommended_frames = self.where(user: user).where.not(frame_id: exclude_frame_ids).map(&:frame)

    recommended_frames << self.recommended_family_frame
    recommended_frames << self.recommended_hero_frame

    recommended_frames.compact
  end

  def self.recommended_premium_frames(user:, exclude_frame_ids: [])
    recommended_frames = []

    recommended_frames << self.recommended_family_frame
    recommended_frames << self.recommended_hero_frame
    
    recommended_frames += self.joins(:frame).where(user: user)
                                              .where.not(frame_id: exclude_frame_ids)
                                              .where(frames: { frame_type: :premium })
                                              .map(&:frame)

    recommended_frames.compact
  end

  def self.recommended_family_frame
    Frame.where(frame_type: :family_frame_premium, identity_type: :gold_lettered_user, active: true).last
  end

  def self.recommended_hero_frame
    Frame.where(frame_type: :hero_frame_premium, identity_type: :top_trapezoidal_identity, active: true).last
  end
end
