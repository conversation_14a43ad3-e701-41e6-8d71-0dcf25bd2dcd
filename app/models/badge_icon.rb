class BadgeIcon < ApplicationRecord
  enum color: Role::BADGE_COLOR
  belongs_to :badge_icon_group
  belongs_to :admin_medium
  has_many :user_roles

  after_update_commit :trigger_identity_image_generation_for_users

  validates_uniqueness_of :badge_icon_group_id, scope: [:color],
                          message: "group id with this color is already taken"
  validates :badge_icon_group_id, numericality: { only_integer: true }, presence: true, allow_blank: false
  validates :admin_medium_id, numericality: { only_integer: true }, presence: true, allow_blank: false
  validates :color, inclusion: { in: colors.keys }
  validate :check_badge_icon_group_id, :check_admin_medium_id

  def check_badge_icon_group_id
    errors.add(:badge_icon_group_id, "Badge Icon Group must exist") if badge_icon_group.nil?
  end

  def check_admin_medium_id
    errors.add(:admin_medium_id, "Admin media must exist") if admin_medium.nil?
  end

  def trigger_identity_image_generation_for_users
    # Trigger identity image generation for all users using this badge icon when it changes
    if saved_change_to_badge_icon_group_id?
      user_roles.find_in_batches(batch_size: 250) do |batch|
        batch.each do |user_role|
          IdentityPhotoGenerationWorker.perform_async(user_role.user_id)
        end
      end
    end
  end
end
