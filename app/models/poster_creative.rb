class PosterCreative < ApplicationRecord
  include JsonBuilder
  has_paper_trail
  belongs_to :event, optional: true
  belongs_to :creator, polymorphic: true
  belongs_to :photo_v2, polymorphic: true, optional: true # photo_v2 is the premium photo for poster creative
  belongs_to :photo_v3, polymorphic: true # photo_v3 is the premium photo for poster creative
  belongs_to :designer, class_name: "<PERSON><PERSON><PERSON><PERSON>", foreign_key: "designer_id", optional: true

  enum creative_kind: %i[wishes quotes schemes info contestant prepoll manifesto congrats], _suffix: true
  enum h1_leader_photo_ring_type: %i[dark light], _suffix: true
  enum h2_leader_photo_ring_type: %i[dark light sticker], _suffix: true

  has_many :poster_creative_circles, dependent: :destroy
  has_many :metadatum, as: :entity
  has_many :poster_shares

  attr_accessor :circle_ids, :has_event, :send_dm_message, :dm_text_message, :send_dm_message_notification,
                :scheduled_notification_job
  attribute :send_dm_message
  attribute :dm_text_message
  attribute :send_dm_message_notification
  attribute :start_time
  attribute :end_time

  validates_presence_of :creative_kind, :h1_leader_photo_ring_type, :h2_leader_photo_ring_type
  validates :creative_kind, inclusion: { in: PosterCreative.creative_kinds.keys }

  validate :presence_of_photo_v3, on: :create
  validate :check_photos_aspect_ratio, on: :create, if: -> { errors.blank? }
  validate :validate_whether_event_has_primary_creative, if: -> { errors.blank? }
  validate :validate_event_has_active_free_creative, if: -> { self.event.present? }

  before_save :update_primary_creative, if: -> { self.primary? }
  before_save :set_default_values, if: -> { new_record? }
  after_create_commit :generate_link_preview_image, if: -> { self.photo_v3.present? }
  after_create_commit :determine_pose_preference_async, if: -> { should_trigger_pose_preference_determination? }
  after_update_commit :delete_scheduled_notification_job, :if => lambda { |pc| pc.saved_change_to_active? && !pc.active? }
  after_create_commit :send_message_to_dm
  after_update_commit :update_send_message_to_dm
  after_commit :index_creative_for_posters_feed

  scope :active, -> { where(active: true) }

  def self.ransackable_associations(_auth_object = nil)
    %w[poster_creative_circle live_poster_creative]
  end

  def self.ransackable_scopes(_auth_object = nil)
    %i[poster_creative_circle_eq live_poster_creative_eq]
  end

  def self.poster_creative_circle_eq(value)
    joins(:poster_creative_circles).where(poster_creative_circles: { circle_id: value })
  end

  def validate_event_has_active_free_creative
    # Skip validation if this is a new free and active creative (it will satisfy the requirement)
    return if !self.paid && self.active

    # Check if there are any other active, free creatives for this event
    other_active_free_creatives = PosterCreative.where(event_id: event_id, paid: false, active: true)
    other_active_free_creatives = other_active_free_creatives.where.not(id: id) unless new_record?

    # If no other active free creatives exist and this creative doesn't satisfy the requirement
    unless other_active_free_creatives.exists?
      if self.paid
        errors.add(:base, "Event should have at least one free creative that is active. Please create a free and active creative first.")
      elsif !self.active
        errors.add(:active, "Event must have at least one active free creative. You cannot deactivate this creative unless another free active creative exists.")
      end
    end
  end

  def self.live_poster_creative_eq(value)
    if value == 'yes'
      where('start_time <= ? AND end_time >= ? AND active = ?', Time.zone.now, Time.zone.now, true)
    elsif value == 'no'
      where('start_time > ? OR end_time < ? OR active = ?', Time.zone.now, Time.zone.now, false)
    else
      all
    end
  end

  def send_message_to_dm
    if send_dm_message && !paid && Time.zone.now <= end_time
      if start_time > Time.zone.now
        jid = SendPosterCreativeWorker.perform_at(start_time, id, send_dm_message_notification, dm_text_message)
        Metadatum.create(entity: self, key: Constants.poster_creative_jid_key, value: jid)
      else
        SendPosterCreativeWorker.perform_async(id, send_dm_message_notification, dm_text_message)
      end
    end
  end

  def update_send_message_to_dm
    if saved_change_to_start_time? && Time.zone.now <= end_time
      poster_creative_metadata = Metadatum.where(entity: self, key: Constants.poster_creative_jid_key).first
      if poster_creative_metadata.present?
        if start_time >= Time.zone.now
          Sidekiq::ScheduledSet.new.find_job(poster_creative_metadata.value).reschedule(start_time)
        else
          Sidekiq::ScheduledSet.new.find_job(poster_creative_metadata.value).reschedule(Time.zone.now)
        end
      end
    end
  end

  def set_default_values
    if self.event.present?
      self.start_time = self.event.start_time if self.start_time.blank?
      self.end_time = self.event.end_time if self.end_time.blank?
    else
      self.start_time = Time.zone.now if self.start_time.blank?
      self.end_time = Event::DEFAULT_EVENT_END_TIME if self.end_time.blank?
    end
  end

  def update_primary_creative
    # update primary creative as false if current creative is primary
    if self.primary? && self.active? && self.event.present?
      self.event.poster_creatives.where.not(id: self.id).update_all(primary: false)
    end
  end

  def presence_of_photo_v3
    if self.photo_v3&.blob_data&.blank? && self.photo_v3&.ms_data&.blank?
      errors.add(:photo_v3, "Poster creative photo is absent")
    end
  end

  def validate_whether_event_has_primary_creative
    if self.event.present?
      if (active_changed?(from: false, to: true) && !self.primary && !self.event.poster_creatives.
        where(primary: true, active: true).where.not(id: id).exists?) || (!self.primary && self.active &&
        !self.event.poster_creatives.where(primary: true, active: true).where.not(id: id).exists?)
        errors.add(:primary, "Event must have a primary creative")
      elsif primary_changed?(from: false, to: true) && !self.active
        errors.add(:primary, "inactive creative cannot be a primary creative")
      elsif primary_changed?(from: true, to: false) && self.event.poster_creatives.where(primary: true, active: true).
        where.not(id: id).count == 0
        errors.add(:primary, "Event must have a primary creative so please make another creative as primary and then
                             inactive this creative")
      end
    end

    if self.event.present? && self.event.active? && active_changed?(from: true, to: false) && primary
      errors.add(:primary, "primary creative cannot be inactive, So Please make another creative as primary and then
                             inactive this creative")
    end
  end

  def delete_scheduled_notification_job
    scheduled_job_exists = Metadatum.where(entity: self, key: Constants.poster_creative_jid_key).exists?
    if scheduled_job_exists
      scheduled_job_metadata = Metadatum.where(entity: self, key: Constants.poster_creative_jid_key).first
      Sidekiq::ScheduledSet.new.find_job(scheduled_job_metadata.value)&.delete
      scheduled_job_metadata.destroy
    end
  end

  def check_photos_aspect_ratio
    aspect_ratio_1 = 630.0 / 940.0
    aspect_ratio_2 = 4.0 / 5.0
    if self.photo_v2.present? && self.errors.blank?
      photo_path = (self.photo_v2.respond_to?(:ms_data) && self.photo_v2.ms_data.present?) ? self.photo_v2.ms_data.cdn_url : self.photo_v2.blob_data.path
      w, h = FastImage.size(photo_path)
      if ((w.to_f / h.to_f) - aspect_ratio_1).abs > 0.01
        errors.add(:photo_v2, "Poster creative photo v2 is not a valid aspect ratio")
      end
    end

    if self.photo_v3.present? && self.errors.blank?
      photo_path = (self.photo_v3.respond_to?(:ms_data) && self.photo_v3.ms_data.present?) ? self.photo_v3.ms_data.cdn_url : self.photo_v3.blob_data.path
      w, h = FastImage.size(photo_path)
      if ((w.to_f / h.to_f) - aspect_ratio_2).abs > 0.01
        errors.add(:photo_v3, "Poster creative photo v3 is not a valid aspect ratio")
      end
    end
  end

  def self.get_creative_kind_verbose(creative_kind)
    case creative_kind.to_sym
    when :wishes
      I18n.t('creative_verbose.wishes')
    when :quotes
      I18n.t('creative_verbose.quotes')
    when :schemes
      I18n.t('creative_verbose.schemes')
    when :info
      I18n.t('creative_verbose.info')
    end
  end

  def self.get_creative_kinds_for_sections
    self.creative_kinds.keys.reverse - %w[wishes contestant prepoll manifesto congrats]
  end

  def self.of_event(event_id:, include_paid: false, creative_id: nil, include_expired: true, include_inactive: true)
    paid_sub_query = sub_query_for_paid(include_paid)
    expired_sub_query = sub_query_for_expired(include_expired, true)
    inactive_sub_query = sub_query_for_inactive(include_inactive, true)

    query = PosterCreative
              .joins(:event)
              .where("event_id = ? #{paid_sub_query} #{expired_sub_query} #{inactive_sub_query}", event_id)

    # # if creative_id is present, then order by that creative first
    if creative_id.present?
      query.order(Arel.sql("CASE WHEN poster_creatives.id = #{creative_id} THEN 0 ELSE 1 END"), :primary => :desc, :id => :desc)
      # if creative_id is not present, then order by primary first and then by id desc
    else
      query.order(:primary => :desc, :id => :desc)
    end
  end

  def self.of_kind_in_circle(kind:, circle_id:, include_paid: false, creative_id: nil, include_expired: true, include_inactive: true)
    paid_sub_query = sub_query_for_paid(include_paid)
    photo_query = "AND poster_creatives.photo_v3_id IS NOT NULL"
    expired_sub_query = sub_query_for_expired(include_expired, false)
    inactive_sub_query = sub_query_for_inactive(include_inactive, false)

    query = PosterCreative
              .joins(:poster_creative_circles)
              .where(creative_kind: kind)
              .where("poster_creative_circles.circle_id = ? #{paid_sub_query} #{photo_query} #{expired_sub_query} #{inactive_sub_query}", circle_id)

    # if creative_id is present, then order by that creative first and then by priority and then by id desc
    if creative_id.present?
      query.order(Arel.sql("CASE WHEN poster_creatives.id = #{creative_id} THEN 0 ELSE 1 END"), :primary => :desc, :id => :desc)
      # if creative_id is not present, then order by primary first and then by id desc
    else
      query.order(:primary => :desc, :id => :desc)
    end
  end

  def self.of_circle(circle_id:, kind: nil, creative_id: nil, include_paid: false, include_expired: false, include_inactive: false, limit: 20, offset: 0)
    paid_sub_query = sub_query_for_paid(include_paid)
    photo_query = "AND poster_creatives.photo_v3_id IS NOT NULL"
    expired_sub_query = sub_query_for_expired(include_expired, false)
    inactive_sub_query = sub_query_for_inactive(include_inactive, false)

    query = PosterCreative
              .joins(:poster_creative_circles)
              .where("poster_creative_circles.circle_id = ? #{paid_sub_query} #{photo_query} #{expired_sub_query} #{inactive_sub_query}", circle_id)
              .where.not(creative_kind: [:contestant, :prepoll, :manifesto])

    if kind.present?
      query = query.where(creative_kind: kind)
    end

    if creative_id.present?
      query = query.order(Arel.sql("CASE WHEN poster_creatives.id = #{creative_id} THEN 0 ELSE 1 END"), :id => :desc)
    else
      query = query.order(:id => :desc)
    end

    query.offset(offset).limit(limit)
  end

  def self.get_creatives(creative_id:, include_paid: false, include_expired: true, include_inactive: true)
    paid_sub_query = sub_query_for_paid(include_paid)
    expired_sub_query = sub_query_for_expired(include_expired, false)
    inactive_sub_query = sub_query_for_inactive(include_inactive, false)

    PosterCreative.where("id = ? #{paid_sub_query} #{expired_sub_query} #{inactive_sub_query}", creative_id)
                  .order(:primary => :desc, :id => :desc)
  end

  def self.creatives_with_events_of_circle(circle_id:, include_paid: false, include_expired: false, include_inactive: false, limit: 20)
    paid_sub_query = sub_query_for_paid(include_paid)
    expired_sub_query = sub_query_for_expired(include_expired, true)
    inactive_sub_query = sub_query_for_inactive(include_inactive, true)
    events = Event.joins(:event_circles)
                  .where(event_circles: { circle_id: })
                  .where('events.start_time < ?', Time.zone.now)
                  .where('events.end_time > ?', Time.zone.now)
                  .where(active: true)
    PosterCreative
      .joins(:event)
      .where("#{paid_sub_query} #{expired_sub_query} #{inactive_sub_query}"[4..]) # [4..] remove the first 'AND '
      .where(event_id: events.pluck(:id))
      .where('poster_creatives.start_time < ?', Time.zone.now)
      .where('poster_creatives.end_time > ?', Time.zone.now)
      .order(primary: :desc, id: :desc).limit(limit)
  end

  def self.sub_query_for_paid(include_paid)
    if include_paid
      ''
    else
      'AND poster_creatives.paid = false'
    end
  end

  def self.sub_query_for_expired(include_expired, for_event)
    if include_expired
      ""
    else
      creatives_expire_query = "AND poster_creatives.start_time <= '#{Time.now}' AND poster_creatives.end_time >= '#{Time.now}'"
      if for_event
        return "AND poster_creatives.end_time >= events.start_time #{creatives_expire_query}"
      end
      creatives_expire_query
    end
  end

  def self.sub_query_for_inactive(include_inactive, for_event)
    if include_inactive
      ''
    else
      creatives_active_query = "AND poster_creatives.active = true"
      if for_event
        return "AND events.active = true #{creatives_active_query}"
      end
      creatives_active_query

    end
  end

  # Returns the photo v1 url based on the user's subscription status
  def get_photo_v1_url(user)
    # TODO: Check again with sidda if he remembers why this is done
    if !user.has_premium_layout? && user.is_poster_subscribed
      photo_v2&.url || photo_v3&.url
    else
      paid ? nil : (photo_v2&.url || photo_v3&.url)
    end
  end

  def generate_link_preview_image
    GeneratePosterCreativePreviewV1.perform_async(id)
  end

  def get_json(event: nil, category_kind: nil, circle_id: nil,
               require_poster_params: true, send_circle_cta: false)
    poster_params = nil
    deeplink = nil
    # we are not sending poster params for kyc carousel if the user is not affiliated with the leader
    # because he will get other party gradients in layouts section and we don't want to show different gradients
    # this is doing for a badge user only
    if require_poster_params
      if event.present?
        poster_params = {
          id: event.id.to_s,
          creative_id: id.to_s,
        }
      else
        poster_params = {
          category_kind: category_kind,
          circle_id: circle_id.to_s,
          creative_id: id.to_s,
        }
      end
    end

    deeplink = "/posters/layout?source=creative_carousel&#{poster_params.to_query}" if poster_params.present?

    json_data = {
      id: id.to_s,
      creative_photo: photo_v3,
      quote_attachment_data: nil,
      create_poster_enabled: true,
      create_poster_params: poster_params,
      deeplink: deeplink,
    }
    # Add cta text and deeplink for contestant kind, so that we can show the cta button in the carousel
    json_data.merge!(cta_text: I18n.t('cta.circle_view'),
                     cta_deeplink: URI.parse("/circles/#{circle_id}")) if send_circle_cta &&
                                                                          category_kind&.to_sym == :contestant
    json_data
  end

  def self.category_kind_based_poster_creatives(circle_ids:, sub_query_for_free_users:,
                                                creative_kind:, offset: 0, count: Constants.creatives_count)
    # Adjust the limit to fetch one extra record to check for the presence of a next page.
    # This does not increase the load significantly but allows us to know if more records exist.
    limit = count + 1
    creatives_hash_per_circle = {}
    circle_ids.each do |circle_id|
      creatives = PosterCreative.includes(:photo_v3)
                                .joins(:poster_creative_circles)
                                .where(poster_creative_circles: { circle_id: circle_id })
                                .where(creative_kind: creative_kind, active: true)
                                .where("start_time < ? AND end_time > ? #{sub_query_for_free_users}", Time.zone.now,
                                       Time.zone.now)
                                .where.not(photo_v3_id: nil)
                                .order(:primary => :desc, :id => :desc)
                                .offset(offset)
                                .limit(limit)

      # Only include if there are any creatives found
      if creatives.present?
        # Check if we fetched more items than the requested count. If so, there's a next page.
        has_next_page = creatives.size > count

        # If there's a next page, remove the extra item not meant to be returned.
        creatives = creatives.first(count) if has_next_page
        next_page_url = has_next_page ? "/creatives?circle_id=#{circle_id}&category_kind=#{creative_kind}&offset=#{offset + count}&count=#{count}" : nil

        creatives_hash_per_circle[circle_id] = {
          poster_creatives: creatives,
          next_page_url: next_page_url
        }
      end
    end
    creatives_hash_per_circle
  end

  def self.fetch_category_kind_data_as_json_v2(user_affiliated_circle_ids:, sub_query_for_free_users:)
    category_kind_sections = []
    PosterCreative.get_creative_kinds_for_sections.each do |creative_kind|
      circle_ids_with_poster_creatives = category_kind_based_poster_creatives(circle_ids: user_affiliated_circle_ids,
                                                                              sub_query_for_free_users: sub_query_for_free_users,
                                                                              creative_kind: creative_kind)
      circle_ids_with_poster_creatives.each do |circle_id, poster_creatives_hash|
        poster_creatives, next_page_url = poster_creatives_hash.values_at(:poster_creatives, :next_page_url)
        circle = Circle.find_by(id: circle_id)
        items = poster_creatives.map { |poster_creative| poster_creative.get_json(category_kind: creative_kind, circle_id: circle_id) }
        title = "#{PosterCreative.get_creative_kind_verbose(creative_kind)}-#{circle&.name}"
        analytics_params = { kind: creative_kind, circle_id: circle_id, circle_name: circle&.name }
        # build feed item id by using circle id and creative kind
        feed_item_id = "circle-#{circle_id}_#{creative_kind}"
        category_kind_sections << PosterCreative.build_creative_carousel(title: title, items: items,
                                                                         next_page_url: next_page_url,
                                                                         analytics_params: analytics_params,
                                                                         feed_item_id: feed_item_id)
      end
    end
    category_kind_sections
  end

  def self.get_relevant_fan_poster_as_toast(user_id:, circle_ids:, created_from: 1.week.ago, created_upto: Time.zone.now)
    creatives_fetch_limit = 10
    max_count_per_creative_per_user = Constants.relevant_fan_poster_max_view_count

    relevant_creatives = PosterCreative.joins(:poster_creative_circles)
                                       .where(poster_creative_circles: { circle_id: circle_ids })
                                       .where(creative_kind: :info, active: true)
                                       .where('start_time < ? AND end_time > ? AND poster_creatives.paid = false',
                                              Time.zone.now,
                                              Time.zone.now)
                                       .where('poster_creatives.created_at > ? AND poster_creatives.created_at < ?', created_from, created_upto)
                                       .order(:primary => :desc, :id => :desc)
                                       .limit(creatives_fetch_limit)
    toast_creative = nil
    preview_image_url = nil
    relevant_creatives.each do |creative|
      preview_image_metadata = Metadatum.where(entity: creative, key: 'creative_link_preview_image_url').first
      next unless preview_image_metadata.present?

      hash_key = "relevant_fan_poster_creative_#{creative.id}"
      field = user_id.to_s

      # check if user has seen this creative max_count_per_creative_per_user times
      if $redis.hget(hash_key, field).to_i < max_count_per_creative_per_user
        toast_creative = creative
        preview_image_url = preview_image_metadata.value
        $redis.hincrby(hash_key, field, 1)
        $redis.expireat(hash_key, (creative.created_at + 10.days).to_i)
        break
      end
    end

    return nil unless toast_creative.present?

    circle_id = toast_creative.poster_creative_circles.find { |row| circle_ids.include? row.circle_id }.circle_id
    circle = Circle.find_by(id: circle_id)

    message = I18n.t('fan_poster_toast.default_message')
    if circle.political_leader_level?
      message = I18n.t('fan_poster_toast.leader_message', leader_circle: circle.name)
    elsif circle.political_party_level?
      message = I18n.t('fan_poster_toast.party_message', party_circle: circle.name)
    end

    {
      feed_item_id: Constants.relevant_fan_poster_toast_id,
      feed_type: 'feed_toast',
      image_url: Capture.apply_img_transform(preview_image_url),
      cta_url: "praja://buzz.praja.app/posters/layout?creative_id=#{toast_creative.id}&circle_id=#{circle_id}&category_kind=#{toast_creative.creative_kind}",
      image_width: 600,
      image_height: 315,
      is_removable: false,
      toast_color: 0xffE8EAF6,
      header: I18n.t('fan_poster_toast.header'),
      message: message,
    }
  end

  # get the latest creative of each circle id and location id and return hash of creative with circle id as key
  # along with has_next_page and offset
  def self.latest_creative_of_contestants(circle_ids:, location_ids:, sub_query_for_free_users:,
                                          creative_kind:, offset: 0, count: Constants.creatives_count)
    # Adjust the limit to fetch one extra record to check for the presence of a next page.
    # This does not increase the load significantly but allows us to know if more records exist.
    creative_count = 0
    max_count = count + offset
    creatives_hash_per_circle = {}
    has_next_page = false
    circle_ids.each do |circle_id|
      creatives = PosterCreative
                    .joins(:poster_creative_circles)
                    .where(poster_creative_circles: { circle_id: circle_id })
                    .joins("INNER JOIN poster_creative_circles as pcc ON pcc.poster_creative_id = poster_creatives.id")
                    .where(pcc: { circle_id: location_ids })
                    .where(creative_kind: creative_kind, active: true)
                    .where("start_time < ? AND end_time > ? #{sub_query_for_free_users}", Time.zone.now,
                           Time.zone.now)
                    .where.not(photo_v3_id: nil)
                    .order(primary: :desc, id: :desc)
                    .first

      # Only include if there are any creatives found
      if creatives.present?
        # Check if we fetched more items than the requested count. If so, there's a next page.
        creative_count += 1
        if creative_count > max_count
          has_next_page = true
          break
        end

        creatives_hash_per_circle[circle_id] = creatives
      end
    end
    # return offset to max_count creatives only
    # Slice the keys for the desired range and then map to get the corresponding key-value pairs
    creatives_hash_selected_keys = creatives_hash_per_circle.keys.slice(offset..max_count)
    creatives_hash_selected_range = creatives_hash_selected_keys.map { |key| [key, creatives_hash_per_circle[key]] }.to_h

    [creatives_hash_selected_range, has_next_page, (offset + count)]
  end

  def self.manifesto_creatives(circle_ids:, offset: 0, count: Constants.creatives_count)
    limit = count + 1
    creatives = PosterCreative
                  .joins(:poster_creative_circles)
                  .includes(:poster_creative_circles)
                  .where(poster_creative_circles: { circle_id: circle_ids })
                  .where(creative_kind: :manifesto, active: true)
                  .where("start_time < ? AND end_time > ?", Time.zone.now,
                         Time.zone.now)
                  .where.not(photo_v3_id: nil)
                  .order(primary: :desc, id: :asc)
                  .offset(offset)
                  .limit(limit)
    has_next_page = creatives.size > count
    # If there's a next page, remove the extra item not meant to be returned.
    creatives = creatives.first(count) if has_next_page
    next_page_url = has_next_page ? "/creatives?circle_id=#{circle_ids.first}&category_kind=#{:manifesto}&offset=#{offset + count}&count=#{count}" : nil

    [creatives, next_page_url, (offset + count)]
  end

  def self.congrats_creatives(creative_circle_ids:, circle_id: nil, offset: 0, count: Constants.creatives_count)
    limit = count + 1
    creatives = PosterCreative
                  .joins(:poster_creative_circles)
                  .includes(:poster_creative_circles)
                  .where(poster_creative_circles: { circle_id: creative_circle_ids })
                  .where(creative_kind: :congrats, active: true)
                  .where("start_time < ? AND end_time > ?", Time.zone.now,
                         Time.zone.now)
                  .where.not(photo_v3_id: nil)
                  .order(primary: :desc, id: :desc)
                  .select('poster_creatives.*, poster_creative_circles.circle_id AS creative_circle_id')
                  .offset(offset)
                  .limit(limit)

    has_next_page = creatives.size > count
    # If there's a next page, remove the extra item not meant to be returned.
    creatives = creatives.first(count) if has_next_page
    next_page_url = has_next_page ? "/creatives?circle_id=#{circle_id}&category_kind=#{:congrats}&offset=#{offset + count}&count=#{count}" : nil

    [creatives, next_page_url]
  end

  def self.get_latest_creative_of_circle(circle_id)
    PosterCreative.joins(:poster_creative_circles)
                  .where(poster_creative_circles: { circle_id: circle_id })
                  .where('start_time < ? AND end_time > ? AND poster_creatives.paid = false',
                         Time.zone.now, Time.zone.now)
                  .where(active: true)
                  .order(id: :desc)
                  .first
  end

  def build_creative_json(user:, posters_tab_v3_enabled: true, is_layout_locked:,
                          is_eligible_for_premium_creatives:, category_kind: nil, circle_id: nil, category_id: nil,
                          creative_id: nil, from_admin_dashboard: false)
    if h2_leader_photo_ring_type.to_sym == :sticker
      h2_background_gradients = user.get_h2_background_gradients_of_user(creative_circle_id: circle_id,
                                                                         category_id: category_id,
                                                                         has_creative_id: creative_id.present?,
                                                                         has_layout_data: is_eligible_for_premium_creatives)
    else
      h2_background_gradients = nil
    end
    circle = nil
    circle = Circle.find_by_id(circle_id) if circle_id.present?
    header_1_leader_photo_ring_type = self.build_h1_leader_photo_ring_type(user: user, from_admin_dashboard:)

    header_2_leader_photo_ring_type = self.build_h2_leader_photo_ring_type(user: user, from_admin_dashboard:)

    {
      id: id,
      v2_url: posters_tab_v3_enabled ? photo_v3.url : photo_v2.url,
      thumbnail_url: photo_v3.placeholder_url,
      v1_url: get_photo_v1_url(user),
      paid: paid,
      h1_background_type: header_1_leader_photo_ring_type,
      h2_background_type: header_2_leader_photo_ring_type,
      leader_photo_ring_color: header_1_leader_photo_ring_type.to_sym == :dark ? 0xff000000 : 0xffFFFFFF,
      h2_background_gradients: h2_background_gradients,
      is_locked: (is_layout_locked && paid),
      start_time: start_time,
      event_id: event_id,
      creative_kind: creative_kind,
      analytics_params: {
        creative_id: id,
        h1_background_type: header_1_leader_photo_ring_type,
        h2_background_type: header_2_leader_photo_ring_type,
        photo_v2_supported: photo_v2.present?,
        photo_v3_supported: photo_v3.present?,
        paid: paid,
        circle_id: circle_id,
        circle_name: circle&.name,
        circle_name_en: circle&.name_en,
        circle_type: circle&.circle_type,
        circle_level: circle&.level,
        category_kind: category_kind,
        category_id: category_id,
        category_name: category_id.present? ? Event.find_by_id(category_id)&.name : nil
      }.compact,
    }
  end

  def build_h1_leader_photo_ring_type(user:, from_admin_dashboard:)
    if from_admin_dashboard || AppVersionSupport.transparent_background_type_supported?
      poster_layout = user.get_user_poster_layout
      if poster_layout.present? && poster_layout.h1_background_type.present? &&
         !poster_layout.creative_based_h1_background_type?
        return poster_layout.h1_background_type
      end
    end
    h1_leader_photo_ring_type
  end

  def build_h2_leader_photo_ring_type(user:, from_admin_dashboard:)
    if from_admin_dashboard || AppVersionSupport.transparent_background_type_supported?
      poster_layout = user.get_user_poster_layout
      if poster_layout.present? && poster_layout.h2_background_type.present? &&
         !poster_layout.creative_based_h2_background_type?
        return poster_layout.h2_background_type
      end
    end
    h2_leader_photo_ring_type
  end

  def self.upcoming_event_creatives(user:, is_layout_locked:)
    events = Event.upcoming_events(user: user)
    event_ids = events.pluck(:id).uniq
    poster_creatives = PosterCreative.where(event_id: event_ids, primary: true)
                                     .order(Arel.sql("FIELD(event_id, #{event_ids.join(',')})"))
    creatives_json = poster_creatives.map do |creative|
      creative.build_creative_json(user: user, posters_tab_v3_enabled: true, is_layout_locked: is_layout_locked,
                                   is_eligible_for_premium_creatives: true)
    end
    creatives_json.compact
  end

  # if event_id is given, then order by that event first and then by priority and then by id desc
  def self.upcoming_event_creatives_including_current(user:, is_layout_locked:, event_id: nil)
    events = Event.upcoming_events_including_current(user: user)
    event_ids = events.pluck(:id).uniq

    # If event_id is provided, prioritize it
    if event_id.present?
      event_id = event_id.to_i

      if event_ids.include?(event_id)
        # If event is in the list, move it to the beginning
        event_ids.delete(event_id)
      end
      # Add event_id to the beginning (whether it was in the list or not)
      event_ids.unshift(event_id)
    end

    poster_creatives = PosterCreative.where(event_id: event_ids, primary: true)
                                     .order(Arel.sql("FIELD(event_id, #{event_ids.join(',')})"))
    creatives_json = poster_creatives.map do |creative|
      creative.build_creative_json(user: user, posters_tab_v3_enabled: true, is_layout_locked: is_layout_locked,
                                   is_eligible_for_premium_creatives: true)
    end
    creatives_json.compact
  end

  private

  def index_creative_for_posters_feed
    # if id changed or priority changed or start_time or end_time changed or active changed or primary changed
    if saved_change_to_id? || saved_change_to_start_time? || saved_change_to_end_time? || saved_change_to_active? ||
       saved_change_to_primary?
      index_id = self.event_id.present? ? "event_#{self.event_id}" : "creative_#{self.id}"
      IndexCreativesForPostersFeed.perform_async(index_id)
    end
  end

  def determine_pose_preference_async
    Rails.logger.warn("Triggering pose preference determination for poster_creative_id: #{id}")
    DeterminePosePreferenceWorker.perform_async(id)
  end

  def should_trigger_pose_preference_determination?
    # Only trigger if we have a photo and pose_preference_order is empty
    photo_v3&.url.present? && pose_preference_order.blank?
  end
end
