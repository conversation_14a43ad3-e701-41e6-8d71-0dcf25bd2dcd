class VideoCreative < ApplicationRecord
  belongs_to :event, optional: true
  belongs_to :circle, optional: true
  belongs_to :creator, polymorphic: true
  belongs_to :video, class_name: "Video"
  belongs_to :designer, class_name: "Ad<PERSON><PERSON>ser", foreign_key: "designer_id", optional: true

  enum kind: {
    wishes: "wishes",
    quotes: "quotes",
    schemes: "schemes",
    info: "info",
    contestant: "contestant",
    prepoll: "prepoll",
    manifesto: "manifesto",
    congrats: "congrats"
  }

  has_many :video_creative_circles, dependent: :destroy
  has_many :video_poster_shares

  attr_accessor :circle_ids, :has_event
  attribute :start_time
  attribute :end_time

  validates :kind, inclusion: { in: VideoCreative.kinds.keys }

  validate :presence_of_video, on: :create

  before_save :set_default_values, if: -> { new_record? }

  after_commit :index_for_posters_feed

  scope :active, -> { where(active: true) }

  ransacker :event_priority do |parent|
    Arel.sql("(SELECT events.priority FROM events WHERE events.id = video_creatives.event_id)")
  end

  def self.ransackable_associations(_auth_object = nil)
    ["video_creative_circles"]
  end

  def self.ransackable_scopes(_auth_object = nil)
    %i[video_creative_circle_eq]
  end

  def self.video_creative_circle_eq(value)
    joins(:video_creative_circles).where(video_creative_circles: { circle_id: value })
  end

  def presence_of_video
    if self.video&.ms_data&.blank?
      errors.add(:video, "Video Creative is absent!")
    end
  end

  def set_default_values
    if self.event.present?
      self.start_time ||= self.event.start_time
      self.end_time ||= self.event.end_time
    else
      self.start_time = Time.zone.now if self.start_time.blank?
      self.end_time = Event::DEFAULT_EVENT_END_TIME if self.end_time.blank?
    end
  end

  def index_for_posters_feed
    if saved_change_to_id? || saved_change_to_start_time? || saved_change_to_end_time? || saved_change_to_active?
      IndexCreativesForPostersFeed.perform_async("video_creative_#{self.id}")
    end
  end

end
