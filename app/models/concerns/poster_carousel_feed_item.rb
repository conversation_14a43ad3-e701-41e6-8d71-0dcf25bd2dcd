module PosterCarouselFeedItem
  extend ActiveSupport::Concern
  include UsersConcern

  included do

    def eligible_for_poster_carousel_in_feed?(user:)
      AppVersionSupport.poster_carousel_in_feed_supported? && !user.has_poster_share_today &&
        !user.has_seen_frames_twice
    end

    def poster_carousel_in_feed(user:)
      # for premium pitch eligible users and trial users, show all creatives
      # for others show only events
      eligible_for_premium_pitch = user.premium_pitch_hot_lead?
      if user.is_poster_subscribed
        poster_creative, params = most_relevant_creative(user: user, only_events: true)
      elsif eligible_for_premium_pitch || user.is_trial_user? || (user.is_eligible_for_start_trial? &&
        user.has_premium_layout?)
        poster_creative, params = most_relevant_creative(user: user)
      else
        poster_creative, params = most_relevant_creative(user: user, only_events: true, only_high_priority_events: true)
      end
      return if poster_creative.nil?
      # if user is eligible for premium pitch, show more button should not be shown and for others
      # show more button should be shown
      show_more = eligible_for_premium_pitch ? false : true

      build_carousel(user: user, poster_creative: poster_creative, show_more: show_more, params: params)
    end

    def poster_carousel_in_premium_benefits_loss_screen(user:)
      return if !user.is_poster_subscribed || !user.has_premium_layout?
      # Note: showing this carousel only for premium users with layout
      is_layout_locked = !user.is_poster_subscribed
      poster_creatives = PosterCreative.upcoming_event_creatives(user:, is_layout_locked:)
      return if poster_creatives.blank?

      layouts = user.get_user_layouts

      # Determine the count to iterate based on whichever is smaller
      count = [poster_creatives.size, layouts.size].min

      # Take only the required number of items
      poster_creatives = poster_creatives.first(count)
      layouts = layouts.first(count)
      items = layouts.map.with_index do |layout, index|
        layout.merge!(show_camera_icon: false)
        {
          event_date: format_date_in_telugu(poster_creatives[index].dig(:start_time)),
          layout: layout,
          creative: poster_creatives[index],
          params: {
            id: poster_creatives[index].dig(:event_id),
            category_kind: poster_creatives[index].dig(:creative_kind),
            creative_id: poster_creatives[index].dig(:id)
          }
        }
      end
      PosterCreative.build_poster_carousel(user: user, items: items, show_more: false, params: {},
                                           enable_help_button: false)
    end

    private

    def format_date_in_telugu(date)
      month_key = date.strftime('%b').downcase
      day = date.strftime('%-d')
      "#{I18n.t("date.months.#{month_key}")} #{day}"
    end

    def most_relevant_creative(user:, only_events: false, only_high_priority_events: false)
      is_eligible_for_premium_creatives = user.is_eligible_for_premium_creatives?
      sub_query_for_free_users = user.get_sub_query_for_free_users(is_eligible_for_premium_creatives)
      user_affiliated_circle_ids = user.affiliated_circle_ids_for_posters_tab
      events = Event.events_for_posters_tab(user_affiliated_circle_ids:, sub_query_for_free_users:)
                    .select { |event| event.priority.to_sym == :high || !only_high_priority_events }

      if events.present?
        event = events.first
        # Get a random creative by using the random parameter
        poster_creative = event.event_poster_creatives(sub_query_for_free_users: sub_query_for_free_users,
                                                       limit: 1, random: true).first
        params = { category_id: event.id, creative_id: poster_creative&.id }
        return [poster_creative, params] if poster_creative.present?
      elsif !only_events
        creative = fetch_weighted_poster_creatives(circle_ids: user_affiliated_circle_ids,
                                                   sub_query: sub_query_for_free_users).last
        if creative.present?
          circle_id = (creative.poster_creative_circles.pluck(:circle_id) & user_affiliated_circle_ids).first
          params = { category_kind: creative.creative_kind, circle_id: circle_id, creative_id: creative.id }
          return [creative, params]
        end
      end
    end

    def fetch_weighted_poster_creatives(circle_ids:, sub_query:, limit: 1)
      # order based on circle level where poster_creative_circles has circle association and circle has level
      # political leader > party > state > others
      PosterCreative.includes(:photo_v3)
                    .joins(poster_creative_circles: :circle)
                    .where(poster_creative_circles: { circle_id: circle_ids })
                    .where(creative_kind: PosterCreative.creative_kinds.keys - ['contestant'], active: true)
                    .where("start_time < ? AND end_time > ? #{sub_query}", Time.zone.now, Time.zone.now)
                    .where.not(photo_v3_id: nil)
                    .order(Arel.sql("CASE circles.level
                                      WHEN 7 THEN 0
                                      WHEN 6 THEN 1
                                      WHEN 10 THEN 2
                                      ELSE 3
                                     END ASC,
                                    poster_creatives.id DESC"))
                    .limit(limit)
    end

    def build_carousel(user:, poster_creative:, show_more: true, params:)
      is_layout_locked = !user.is_poster_subscribed
      has_layout = user.get_user_poster_layout.present?
      creative_json = poster_creative.build_creative_json(user: user, is_layout_locked: is_layout_locked,
                                                          is_eligible_for_premium_creatives: user.is_eligible_for_premium_creatives?,
                                                          category_kind: params[:category_kind], circle_id: params[:circle_id],
                                                          category_id: params[:category_id], creative_id: poster_creative.id)
      premium_creative = premium_creative_for_params(user: user, params: params) if is_layout_locked && has_layout
      premium_creative_json = premium_creative.build_creative_json(
        user: user,
        is_layout_locked: true,
        is_eligible_for_premium_creatives: true,
        category_kind: params[:category_kind],
        circle_id: params[:circle_id],
        category_id: params[:category_id],
        creative_id: premium_creative.id
      ) if premium_creative.present?

      layouts = user.get_user_layouts(category_id: params[:category_id], circle_id: params[:circle_id],
                                      category_kind: params[:category_kind])
      # show more button should not be shown if layouts are less than or equal to 3
      show_more = false if layouts.size <= 3
      # send only 3 layouts
      layouts = layouts.first(3)
      params = {
        id: params[:category_id],
        circle_id: params[:circle_id],
        category_kind: params[:category_kind],
        creative_id: poster_creative.id
      }.compact
      items = layouts.map do |layout|
        {
          layout: layout,
          # for users with layout and without premium, use the premium creative for the locked layouts we show in the
          # carousel
          creative: layout[:is_locked] && premium_creative.present? ? premium_creative_json : creative_json,
          params: params
        }
      end

      PosterCreative.build_poster_carousel(user: user, items: items, show_more: show_more, params: params)
    end

    def premium_creative_for_params(user:, params:)
      event_id = params[:category_id]
      return if event_id.blank?

      event = Event.find_by(id: event_id)
      return if event.blank?

      paid_creative = event.event_poster_creatives(sub_query_for_free_users: 'AND poster_creatives.paid = true',
                                                   limit: 1).last

      paid_creative
    end
  end
end
