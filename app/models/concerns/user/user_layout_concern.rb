module User::UserLayoutConcern
  extend ActiveSupport::Concern

  def user_json_for_rm_layout_creation_tool
    # Fetch all location circles in a single query
    location_circles = Circle.where(id: [village_id, mandal_id, district_id, state_id])
                             .index_by(&:id)

    {
      id: id,
      phone: phone,
      photo: photo,
      name: name,
      status: status,
      village: location_circles[village_id],
      mandal: location_circles[mandal_id],
      district: location_circles[district_id],
      state: location_circles[state_id],
      badge: user_badge_json_including_unverified,
      dob: dob,
      affiliated_party: get_affiliated_circle_json_for_rm_layout_creation,
      profession: get_profession_for_rm_layout_creation,
    }
  end

  def user_badge_json_including_unverified
    user_role = get_badge_role_including_unverified
    user_role&.get_json
  end

  def get_profession_for_rm_layout_creation
    user_profession = UserProfession.includes(:profession, :sub_profession).where(user: self).first
    return unless user_profession&.profession

    profession_name = user_profession.profession.name_en
    sub_profession_name = user_profession.sub_profession&.name_en

    sub_profession_name ? "#{profession_name} (#{sub_profession_name})" : profession_name
  end

  def get_affiliated_circle_id_for_rm_layout_creation
    get_user_poster_layout_including_inactive ? get_poster_affiliated_party_id : affiliated_party_circle_id
  end

  def get_affiliated_circle_json_for_rm_layout_creation
    circle = Circle.find_by(id: jathara_v2_enabled? ? affiliated_party_circle_id : get_affiliated_circle_id_for_rm_layout_creation)
    circle&.get_json_for_rm_layout_creation(poster_photos_required: false)
  end

  def get_layout_remarks(poster_layout)
    return unless poster_layout
    poster_layout.get_latest_remark_of_layout
  end

  def get_badge_free_text_for_rm_layout_creation
    UserMetadatum.where(user: self, key: Constants.badge_free_text_key).last&.value
  end

  def delete_badge_free_text_from_rm_layout_creation
    um = UserMetadatum.where(user: self, key: Constants.badge_free_text_key).last
    um.update(key: Constants.badge_free_text_key_deleted) if um.present?
  end

  def get_affiliated_circles_for_rm_layout_creation(poster_layout)
    return [] unless poster_layout

    leader_photos = poster_layout.user_leader_photos
                                 .includes(:circle, :photo)
                                 .order(:header_type, :priority)

    existing_circles = []
    requested_circles = []

    photo_ids = leader_photos.select { |lp| lp.photo_id.present? }.map(&:photo_id)

    photo_map = Photo.where(id: photo_ids).index_by(&:id) if photo_ids.any?

    leader_photos.each do |leader_photo|
      circle_json = { priority: leader_photo.priority, type: leader_photo.header_type }

      if leader_photo.circle_id.blank?
        photo_id = leader_photo.photo_id
        circle_json[:name_en] = circle_json[:name] = leader_photo.draft_data&.dig("circle_name")
        if photo_id.present?
          circle_json[:poster_photos] = [{
                                           id: photo_id,
                                           url: photo_map[photo_id]&.placeholder_url,
                                           selected: true
                                         }]
        end
        requested_circles << circle_json
      else
        # Only get circle details if the circle exists
        circle_json.merge!(leader_photo.circle.get_json_for_rm_layout_creation(poster_photos_required: true))
        # if leader_photo.photo_id is in poster_photos list, then add a param selected as true
        if photo = circle_json[:poster_photos].find { |photo| photo[:id] == leader_photo.photo_id }
          photo[:selected] = true
        end
        existing_circles << circle_json
      end
    end

    all_circles = existing_circles + requested_circles

    grouped_circles = all_circles.group_by { |circle| circle[:type] }

    UserLeaderPhoto.header_types.keys.flat_map do |header_type|
      grouped_circles[header_type].to_a.sort_by { |circle| circle[:priority] }
    end
  end

  # these are uploaded by admin user (RM) for the user that is no edit happened on this photos
  def get_layout_original_identity_photos
    keys = User::VALID_ORIGINAL_POSTER_PHOTO_KEYS

    # Fetch user metadata and map keys to photo IDs
    key_with_photo_id = UserMetadatum.where(user: self, key: keys)
                                     .pluck(:key, :value)
                                     .to_h
                                     .transform_values(&:to_i)

    # Preload photos in one query
    photo_urls = Photo.where(id: key_with_photo_id.values.compact)
                      .index_by(&:id)
                      .transform_values(&:placeholder_url)

    # Transform into required format
    key_with_photo_id.map do |key, photo_id|
      photo_url = photo_urls[photo_id]
      { type: key,
        photo: {
          id: photo_id,
          url: photo_url,
        }
      } if photo_url.present?
    end.compact
  end

  # v2: Prioritizes user data, then user model, then nil for each key
  def get_layout_original_identity_photos_v2
    keys = User::VALID_ORIGINAL_POSTER_PHOTO_KEYS
   
    key_with_photo_id = {}
    key_with_photo_type = {}
    
    # 2. For missing keys, fetch from user columns
    keys.each do |key|
      case key
      when Constants.poster_photo_without_background_original_key
        user_attribute_key = "poster_photo_id"
        user_attribute_key_for_photo_type = "poster_photo_type"
      when Constants.hero_frame_photo_original_key
        user_attribute_key = "hero_frame_photo_id"
        user_attribute_key_for_photo_type = "hero_frame_photo_type"
      when Constants.family_frame_photo_original_key
        user_attribute_key = "family_frame_photo_id"
        user_attribute_key_for_photo_type = "family_frame_photo_type"
      else
        user_attribute_key = nil
        user_attribute_key_for_photo_type = nil
      end
      next if user_attribute_key.blank? || user_attribute_key_for_photo_type.blank?
      user_photo_id = self.send(user_attribute_key) rescue nil
      user_photo_type = self.send(user_attribute_key_for_photo_type) rescue nil
      key_with_photo_id[key] = user_photo_id.to_i if user_photo_id.present?
      key_with_photo_type[key] = user_photo_type if user_photo_type.present?
    end

    available_keys = key_with_photo_id.keys.compact
     # 2. Fetch from metadata
    metadata_key_with_photo_id = UserMetadatum.where(user: self, key: keys).where.not(key: available_keys)
                                     .pluck(:key, :value)
                                     .to_h
                                     .transform_values(&:to_i)
    key_with_photo_id = key_with_photo_id.merge(metadata_key_with_photo_id)

    metadata_key_with_photo_id.each do |key, photo_id|
      key_with_photo_type[key] = Photo.name
    end

    # 3. Preload all photo URLs
    photo_ids = []
    admin_medium_ids = []
    # photo_urls = Photo.where(id: photo_ids).index_by(&:id).transform_values(&:placeholder_url)
    key_with_photo_type.each do |key, photo_type|
      if photo_type == Photo.name
        photo_ids << key_with_photo_id[key]
      else
        admin_medium_ids << key_with_photo_id[key]
      end
    end

    photo_urls = Photo.where(id: photo_ids).index_by(&:id).transform_values(&:placeholder_url)
    admin_medium_urls = AdminMedium.where(id: admin_medium_ids).index_by(&:id).transform_values(&:placeholder_url)

    photo_urls = photo_urls.merge(admin_medium_urls)

    # 4. Build result
    key_with_photo_id.map do |key, photo_id|
      photo_url = photo_urls[photo_id]
      next unless photo_url.present?
      {
        type: key,
        photo: {
          id: photo_id,
          url: photo_url
        }
      }
    end.compact
  end

  def get_layout_bg_removed_identity_photos
    keys = User::BG_REMOVAL_POSTER_PHOTO_KEYS

    # Fetch user metadata and map keys to photo IDs
    key_with_photo_id = UserMetadatum.where(user: self, key: keys)
                                     .pluck(:key, :value)
                                     .to_h
                                     .transform_values(&:to_i)

    # Preload photos in one query
    photo_urls = Photo.where(id: key_with_photo_id.values.compact)
                      .index_by(&:id)
                      .transform_values(&:placeholder_url)

    # Transform into required format
    key_with_photo_id.map do |key, photo_id|
      photo_url = photo_urls[photo_id]
      { type: key,
        photo: {
          id: photo_id,
          url: photo_url,
        }
      } if photo_url.present?
    end.compact
  end
end
