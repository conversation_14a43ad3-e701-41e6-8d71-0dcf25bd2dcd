class BadgeIconGroup < ApplicationRecord
  belongs_to :circle, optional: true
  has_many :badge_icons
  has_many :user_roles, through: :badge_icons

  after_update_commit :trigger_identity_image_generation_for_users

  validates_uniqueness_of :circle_id

  def trigger_identity_image_generation_for_users
    # Trigger identity image generation for all users using badge icons from this group when it changes
    if saved_change_to_circle_id?
      user_roles.find_in_batches(batch_size: 250) do |batch|
        batch.each do |user_role|
          IdentityPhotoGenerationWorker.perform_async(user_role.user_id)
        end
      end
    end
  end
end
