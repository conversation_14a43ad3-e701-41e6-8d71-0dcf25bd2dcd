require 'sidekiq-scheduler'

# Run this cron every day at 4:00 AM
class CronToSendWatiMessagesToTrialUsers
  include Sidekiq::Worker

  EXPIRY_IN_SECONDS = 20.hours.to_i
  INELIGIBLE_DAYS = [1, 12].freeze

  def perform
    start_time = Time.zone.now
    Rails.logger.info('Starting to categorize trial users into day-wise Redis keys...')

    unique_days = Set.new
    redis_keys_by_day = {}

    Metadatum.where(key: Constants.user_poster_trial_start_date_key)
      .where('created_at >= ?', 14.days.ago.beginning_of_day)
      .find_in_batches(batch_size: 100) do |batch|
      user_ids = batch.pluck(:entity_id).compact.uniq
      # filter the user_ids who have user poster layout
      valid_user_ids = UserPosterLayout.where(entity_type: 'User', entity_id: user_ids, active: true).pluck(:entity_id)
      batch.each do |metadatum|
        user_id = metadatum.entity_id
        start_date = metadatum.value
        next unless valid_user_ids.include?(user_id)

        day = (Time.zone.today - start_date.to_date).to_i + 1
        next unless valid_day?(day)

        redis_key = Constants.wati_redis_key_for_day(day)
        $redis.sadd(redis_key, user_id)
        $redis.expire(redis_key, EXPIRY_IN_SECONDS) if $redis.ttl(redis_key) == -1
        redis_keys_by_day[redis_key] ||= day
        unique_days << day
      end
    end

    scheduled_time = Time.zone.now.change(hour: 11, min: 45, sec: 0)

    redis_keys_by_day.each do |redis_key, day|
      TriggerDayWiseWatiMessagesForTrialUsers.perform_at(scheduled_time, redis_key, day)
      scheduled_time += 2.minutes
    end

    TriggerPosterGenerationForWatiTrialUsers.perform_async

    elapsed_time = Time.zone.now - start_time
    Rails.logger.info("Finished categorizing users. Duration: #{elapsed_time} seconds")
  end

  private

  def valid_day?(day)
    !INELIGIBLE_DAYS.include?(day) && day > 0
  end
end
