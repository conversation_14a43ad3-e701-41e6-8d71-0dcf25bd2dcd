# frozen_string_literal: true

class CronToGenerateVideoPosterElements
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now
    start_id = $redis.get(Constants.user_poster_layouts_table_last_processed_id_key).to_i

    raw_layouts = UserPosterLayout
                    .where('id > ?', start_id)
                    .limit(500)
                    .pluck(:id, :entity_id, :entity_type)

    return if raw_layouts.empty?

    filtered_user_ids = []
    raw_layouts.each do |_, entity_id, entity_type|
      filtered_user_ids << entity_id if entity_type == 'User'
    end

    end_id = raw_layouts.last[0]
    $redis.set(Constants.user_poster_layouts_table_last_processed_id_key, end_id)

    filtered_user_ids.each_slice(100) do |user_ids|
      users = User
                .includes(:user_video_frames)
                .where(id: user_ids)
                .index_by(&:id)

      user_ids.each do |user_id|
        user = users[user_id]
        next unless user

        user_video_frames_count = user.user_video_frames.select(&:active?).count
        next if user_video_frames_count == 3

        CreateUserVideoFramesWorker.set(queue: :low).perform_async(user.id)
        GenerateProtocolImageWorker.set(queue: :low).perform_async(user.id)
      end
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Processed user poster layouts in #{elapsed_time} sec; end_id: #{end_id}")
  end
end
