require 'sidekiq-scheduler'

class CronToJoinPosterAffiliatedCircleFromLayouts
  include Sidekiq::Worker
  sidekiq_options queue: :default

  def perform
    start_time = Time.zone.now

    start_id = $redis.get(Constants.user_poster_layout_table_last_processed_id_key).to_i

    layouts = UserPosterLayout
                .where('id > ?', start_id)
                .limit(5000)
                .pluck(:id, :entity_id, :entity_type, :active)

    return if layouts.blank?

    end_id = 0
    layouts_to_process = []
    layouts.each do |id, entity_id, entity_type, active|
      if active && entity_type == 'User'
        layouts_to_process << entity_id
      end
      end_id = id
    end

    $redis.set(Constants.user_poster_layout_table_last_processed_id_key, end_id)

    layouts_to_process.each_slice(500) do |batch|
      batch.each do |entity_id|
        affiliated_circle_id = Metadatum.find_by(key: Constants.poster_affiliated_party_key, entity_id: entity_id)&.value.to_i
        next if affiliated_circle_id.zero?
        unless UserCircle.where(user_id: entity_id, circle_id: affiliated_circle_id).exists?
          UserCircle.create(user_id: entity_id, circle_id: affiliated_circle_id, source_of_join: :protocol)
        end
      end
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Poster Affiliated Party Join update time: #{elapsed_time} sec || end_id: #{end_id}")
  end
end
