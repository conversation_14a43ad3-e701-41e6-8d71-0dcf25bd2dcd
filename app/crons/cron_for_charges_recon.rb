require 'sidekiq-scheduler'

class CronForChargesRecon
  include Sidekiq::Worker

  def perform
    charge_ids = SubscriptionCharge
      .joins(:subscription)
      .where("charge_date >= ? AND charge_date < ?",
             Time.zone.now.yesterday.beginning_of_day,
             Time.zone.today.beginning_of_day)
      .where(subscription_charges: { status: :sent_to_pg })
      .where(subscriptions: { status: [:active, :on_hold] })
      .pluck(:id)

    return if charge_ids.empty?

    # Create a Sidekiq batch for recon jobs
    batch = Sidekiq::Batch.new
    batch.on(:complete, ProcessReconBatchSummary)
    
    batch.jobs do
      charge_ids.each do |charge_id|
        SubscriptionChargeReconcile.perform_async(charge_id, batch.bid)
      end
    end
  end
end
