class ProcessVideoCreativeViewsBatch
  include Sidekiq::Worker

  def perform
    logger.info("ProcessVideoCreativeViewsBatch cron running")

    key = Constants.video_creative_views_queue_redis_key
    video_creative_views_batch = []
    video_creative_ids = []

    while (video_creative_view = $redis.spop(key)) != nil
      video_creative_view = JSON.parse(video_creative_view)
      video_creative_ids << video_creative_view["video_creative_id"]

      video_creative_views_batch << VideoCreativeView.new(video_creative_id: video_creative_view["video_creative_id"],
                                                          user_id: video_creative_view["user_id"],
                                                          viewed_at: video_creative_view["viewed_at"])
    end

    VideoCreativeView.import(video_creative_views_batch, on_duplicate_key_ignore: true, batch_size: 100) if video_creative_views_batch.present?

    video_creative_ids.uniq!
    if video_creative_ids.present?
      video_creative_ids.each_slice(100) do |ids|
        video_creatives = VideoCreative.where(id: ids).select(:id, :event_id)
        video_creatives.each do |video_creative|
          if video_creative.present?
            IndexCreativesForPostersFeed.perform_async("video_creative_#{video_creative.id}")
          end
        end
      end
    end

  end
end
