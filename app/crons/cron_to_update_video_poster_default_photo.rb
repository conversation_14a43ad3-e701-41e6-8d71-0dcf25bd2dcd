# frozen_string_literal: true

class CronToUpdateVideoPosterDefaultPhoto
  include Sidekiq::Worker

  def perform
    start_time = Time.zone.now
    start_id = $redis.get(Constants.user_poster_layouts_table_last_processed_id_key).to_i

    raw_layouts = UserPosterLayout
                    .where('id > ?', start_id)
                    .limit(500)
                    .pluck(:id, :entity_id, :entity_type)

    return if raw_layouts.empty?

    filtered_user_ids = []
    raw_layouts.each do |_, entity_id, entity_type|
      filtered_user_ids << entity_id if entity_type == 'User'
    end

    end_id = raw_layouts.last[0]
    $redis.set(Constants.user_poster_layouts_table_last_processed_id_key, end_id)

    filtered_user_ids.each_slice(100) do |user_ids|
      users = User.where(id: user_ids)
                .index_by(&:id)

      user_ids.each do |user_id|
        user = users[user_id]
        next unless user
        next if user.video_poster_default_photo.present?

        if user.video_poster_default_photo.blank? && user.poster_photo.present?
          user.update_columns(
            video_poster_default_photo_id: user.poster_photo.id,
            video_poster_default_photo_type: user.poster_photo.class.name
          )
        end
      end
    end

    elapsed_time = Time.zone.now - start_time
    Rails.logger.warn("Processed User Photo Update in #{elapsed_time} sec; end_id: #{end_id}")
  end
end
