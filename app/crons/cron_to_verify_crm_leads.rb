require 'sidekiq-scheduler'

class CronToVerifyCrmLeads
  include Sidekiq::Worker

  def perform
    Rails.logger.debug("Starting cron to verify crm leads #{Time.zone.now}")
    interested_leads = PremiumPitch.joins(:user)
                                   .where("premium_pitches.created_at >= ?", Time.zone.yesterday.beginning_of_day)
                                   .where("premium_pitches.created_at <= ?", 15.minutes.ago)
                                   .where(premium_pitches: { status: :interested, crm_stage: nil })
                                   .where.not(premium_pitches: { lead_type: :OABLT_APOutbound })
                                   .where(users: { internal: false })
                                   .pluck(:user_id)
    return if interested_leads.empty?

    notifier = Slack::Notifier.new(
      "*******************************************************************************"
    )

    notifier.post(
      text: ":alert: *Interested Leads whose crm_stage is empty* on #{Time.zone.now.strftime('%d %b, %Y')}\n" +
        "Leads Count: *#{interested_leads.count}*",
      attachments: [
        {
          text: "user_ids list: \n #{interested_leads.join(', ')}",
          color: 'danger'
        }
      ]
    )
    Rails.logger.debug("Ending cron to verify crm leads #{Time.zone.now}")
  end
end
