class ProcessVideoFrameViewsBatch
  include Sidekiq::Worker

  def perform
    logger.info("ProcessVideoFrameViewsBatch cron running")

    key = Constants.video_frame_views_queue_redis_key
    video_frame_views_batch = []

    while (video_frame_view = $redis.spop(key)) != nil
      video_frame_view = JSON.parse(video_frame_view)

      video_frame_views_batch << VideoFrameView.new(video_frame_id: video_frame_view["video_frame_id"],
                                                    user_id: video_frame_view["user_id"],
                                                    viewed_at: video_frame_view["viewed_at"])
    end

    VideoFrameView.import(video_frame_views_batch, on_duplicate_key_ignore: true, batch_size: 100) if video_frame_views_batch.present?
  end
end
