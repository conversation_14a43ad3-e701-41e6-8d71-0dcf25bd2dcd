require 'sidekiq-scheduler'

class PosterPhotoScorePredictorBatchCron
  include Sidekiq::Worker

  sidekiq_options queue: :default, retry: 3

  BATCH_SIZE = 1000
  REDIS_KEY = "poster_photo_score_predictor_last_processed_layout_id"

  def perform
    Rails.logger.warn("Starting PosterPhotoScorePredictorBatchCron at #{Time.zone.now}")

    # Get the last processed layout ID from Redis
    last_processed_id = get_last_processed_layout_id
    Rails.logger.warn("Resuming from layout ID: #{last_processed_id || 'beginning'}")

    # Process layouts in batches with Redis tracking
    process_layouts_in_batches(last_processed_id)

    Rails.logger.warn("Completed PosterPhotoScorePredictorBatchCron at #{Time.zone.now}")
  end

  private

  def get_last_processed_layout_id
    last_id = $redis.get(REDIS_KEY)
    last_id.present? ? last_id.to_i : 0
  end

  def set_last_processed_layout_id(layout_id)
    $redis.set(REDIS_KEY, layout_id)
  end

  def process_layouts_in_batches(last_processed_id)
    processed_count = 0

    # Fetch the next batch of layouts
    layouts_batch = fetch_layouts_batch(last_processed_id)

    if layouts_batch.empty?
      Rails.logger.info("No more layouts to process. All caught up!")
      return
    end

    last_processed_layout_id = last_processed_id
    # Process each layout in the batch
    layouts_batch.each do |layout|
      last_processed_layout_id = layout.id
      user = layout.entity

      # Skip if user doesn't have a poster photo
      next unless should_process_user?(user)

      PosterPhotoScorePredictorUsingAI.perform_async(user.id)
      processed_count += 1
      sleep(0.5) if processed_count % 20 == 0
    end

    # Update last processed ID
    set_last_processed_layout_id(last_processed_layout_id)

    Rails.logger.warn("Total users processed in this run: #{processed_count}")
  end

  def fetch_layouts_batch(last_processed_id)
    # Fetch the next batch of UserPosterLayout records who has no user plan data
    # Fetch the next batch of UserPosterLayout records for users who have no user plan data
    UserPosterLayout.where(entity_type: "User")
                    .where("user_poster_layouts.id > ?", last_processed_id)
                    .where(active: true)
                    .joins("LEFT JOIN user_plans ON user_plans.user_id = user_poster_layouts.entity_id")
                    .where(user_plans: { id: nil })
                    .includes(:entity) # Eager load user to avoid N+1 queries
                    .order(:id)
                    .limit(BATCH_SIZE)
  end

  def should_process_user?(user)
    return false unless user.is_a?(User)
    return false unless user.poster_photo.present?
    true
  end
end
