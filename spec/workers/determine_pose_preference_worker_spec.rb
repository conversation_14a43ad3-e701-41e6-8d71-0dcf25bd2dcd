# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeterminePosePreferenceWorker, type: :worker do
  describe '#perform' do
    let(:poster_creative) { create(:poster_creative, pose_preference_order: '', primary: true) }
    let(:worker) { described_class.new }

    before do
      # Mock the valid response with all 7 poses
      response_body = {
        "choices" => [
          {
            "message" => {
              "content" => '{"pose_ranking": ["white_shirt_with_party_scarf", "namaste_pose_for_male", "handover_heart_pose_for_male", "traditional_wear_pose_for_male", "saree_with_party_scarf", "namaste_pose_for_female", "handover_heart_pose_for_female"]}'
            }
          }
        ]
      }.to_json

      # Mock Net::HTTP response
      http_response = instance_double(Net::HTTPResponse)
      allow(http_response).to receive(:code).and_return(200)
      allow(http_response).to receive(:body).and_return(response_body)

      # Mock Net::HTTP
      http = instance_double(Net::HTTP)
      allow(Net::HTTP).to receive(:new).and_return(http)
      allow(http).to receive(:use_ssl=)
      allow(http).to receive(:request).and_return(http_response)
    end

    context 'when poster creative exists and is active' do
      it 'calls OpenAI API and updates pose preference order' do
        worker.perform(poster_creative.id)

        poster_creative.reload
        expect(poster_creative.pose_preference_order).to eq('white_shirt_with_party_scarf,namaste_pose_for_male,handover_heart_pose_for_male,traditional_wear_pose_for_male,saree_with_party_scarf,namaste_pose_for_female,handover_heart_pose_for_female')
      end
    end

    context 'when poster creative does not exist' do
      it 'returns early without processing' do
        # Should not raise any errors and return early
        expect { worker.perform(99999) }.to raise_error
      end
    end

    context 'when OpenAI returns no content' do
      before do
        # Override the mock to return no content
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => nil
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/No content in OpenAI response for poster_creative_id #{poster_creative.id}/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, "No content in OpenAI response")
      end
    end

    context 'when OpenAI returns invalid JSON' do
      before do
        # Override the mock to return invalid JSON
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => "invalid json"
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/Failed to parse JSON response from OpenAI for poster_creative_id #{poster_creative.id}/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, "Failed to parse JSON response")
      end
    end

    context 'when OpenAI returns JSON with markdown code blocks' do
      before do
        # Override the mock to return JSON wrapped in markdown code blocks
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => '```json
{"pose_ranking": ["white_shirt_with_party_scarf", "namaste_pose_for_male", "handover_heart_pose_for_male", "traditional_wear_pose_for_male", "saree_with_party_scarf", "namaste_pose_for_female", "handover_heart_pose_for_female"]}
```'
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)
      end

      it 'strips markdown code blocks and processes JSON correctly' do
        worker.perform(poster_creative.id)

        poster_creative.reload
        expect(poster_creative.pose_preference_order).to eq('white_shirt_with_party_scarf,namaste_pose_for_male,handover_heart_pose_for_male,traditional_wear_pose_for_male,saree_with_party_scarf,namaste_pose_for_female,handover_heart_pose_for_female')
      end
    end

    context 'when OpenAI returns invalid pose ranking format (wrong length)' do
      before do
        # Override the mock to return wrong number of poses
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => '{"pose_ranking": ["white_shirt_with_party_scarf", "namaste_pose_for_male", "handover_heart_pose_for_male"]}'
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/Invalid pose ranking format from OpenAI for poster_creative_id #{poster_creative.id}: expected 7 poses, got/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, /Invalid pose ranking format: expected 7 poses, got 3/)
      end
    end

    context 'when OpenAI returns invalid poses in ranking' do
      before do
        # Override the mock to return invalid poses
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => '{"pose_ranking": ["invalid_pose", "namaste_pose_for_male", "handover_heart_pose_for_male", "traditional_wear_pose_for_male", "saree_with_party_scarf", "namaste_pose_for_female", "handover_heart_pose_for_female"]}'
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/Invalid poses in ranking from OpenAI for poster_creative_id #{poster_creative.id}: \["invalid_pose"\]/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, /Invalid poses in ranking: \["invalid_pose"\]/)
      end
    end

    context 'when OpenAI returns missing poses in ranking' do
      before do
        # Override the mock to return missing poses (duplicate one valid pose, exclude another)
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => '{"pose_ranking": ["white_shirt_with_party_scarf", "namaste_pose_for_male", "handover_heart_pose_for_male", "traditional_wear_pose_for_male", "saree_with_party_scarf", "namaste_pose_for_female", "white_shirt_with_party_scarf"]}'
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/Missing poses in ranking from OpenAI for poster_creative_id #{poster_creative.id}: \["handover_heart_pose_for_female"\]/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, /Missing poses in ranking: \["handover_heart_pose_for_female"\]/)
      end
    end

    context 'when OpenAI returns pose_ranking as non-array' do
      before do
        # Override the mock to return pose_ranking as string instead of array
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => '{"pose_ranking": "not_an_array"}'
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/Invalid pose ranking format from OpenAI for poster_creative_id #{poster_creative.id}: expected 7 poses, got/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, /Invalid pose ranking format: expected 7 poses, got 12/)
      end
    end

    context 'when OpenAI returns empty array for pose_ranking' do
      before do
        # Override the mock to return empty array
        response_body = {
          "choices" => [
            {
              "message" => {
                "content" => '{"pose_ranking": []}'
              }
            }
          ]
        }.to_json

        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(200)
        allow(http_response).to receive(:body).and_return(response_body)

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:error)
      end

      it 'logs warning and raises error' do
        expect(Rails.logger).to receive(:warn).with(/Invalid pose ranking format from OpenAI for poster_creative_id #{poster_creative.id}: expected 7 poses, got/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, /Invalid pose ranking format: expected 7 poses, got 0/)
      end
    end

    context 'when API request fails' do
      before do
        # Mock failed HTTP response
        http_response = instance_double(Net::HTTPResponse)
        allow(http_response).to receive(:code).and_return(500)
        allow(http_response).to receive(:body).and_return("Internal Server Error")

        http = instance_double(Net::HTTP)
        allow(Net::HTTP).to receive(:new).and_return(http)
        allow(http).to receive(:use_ssl=)
        allow(http).to receive(:request).and_return(http_response)

        allow(Rails.logger).to receive(:error)
      end

      it 'logs error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Error in DeterminePosePreferenceWorker for poster_creative_id #{poster_creative.id}: OpenAI API request failed/)

        expect { worker.perform(poster_creative.id) }.to raise_error(StandardError, /OpenAI API request failed/)
      end
    end
  end

  describe 'sidekiq_retries_exhausted callback' do
    it 'notifies Honeybadger and logs error when retries are exhausted' do
      poster_creative_id = 123
      error_message = "Test error"
      exception = StandardError.new(error_message)
      msg = { "args" => [poster_creative_id] }

      expect(Honeybadger).to receive(:notify).with(exception, context: { args: [poster_creative_id] })
      expect(Rails.logger).to receive(:error).with("DeterminePosePreferenceWorker retries exhausted for poster_creative_id #{poster_creative_id}: #{error_message}")

      # Call the sidekiq_retries_exhausted block
      described_class.sidekiq_retries_exhausted_block.call(msg, exception)
    end
  end
end 
