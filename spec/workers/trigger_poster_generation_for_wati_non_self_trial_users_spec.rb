require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe TriggerPosterGenerationForWatiTrialUsers, type: :worker do
  let(:campaign_day3) { 'wati_trial_day_3_poster' }
  let(:campaign_day4) { 'wati_trial_day_4_poster' }

  describe '#perform' do
    context 'when users exist in Redis for day 3 and day 4' do
      let(:user_day3) { create(:user) }
      let(:user_day4) { create(:user) }

      before do
        $redis.sadd(Constants.wati_redis_key_for_day(3), user_day3.id)
        $redis.sadd(Constants.wati_redis_key_for_day(4), user_day4.id)

        allow_any_instance_of(described_class).to receive(:get_user_creative_data_for_wati_campaign) do |_, user, campaign|
          case user.id
          when user_day3.id
            UserMetadatum.new(user_id: user.id, key: "#{campaign_day3}_most_shared_creative_id", value: '101')
          when user_day4.id
            UserMetadatum.new(user_id: user.id, key: "#{campaign_day4}_most_shared_creative_id", value: '202')
          end
        end
      end

      it 'enqueues GeneratePosterCampaignImage jobs with correct arguments' do
        described_class.new.perform

        expect(GeneratePosterCampaignImage.jobs.map { |j| j['args'] }).to include(
                                                                            [user_day3.id, 101, campaign_day3],
                                                                            [user_day4.id, 202, campaign_day4]
                                                                          )
      end
    end

    context 'when user has no creative data' do
      let(:user) { create(:user) }

      before do
        $redis.sadd(Constants.wati_redis_key_for_day(3), user.id)
      end

      it 'skips the user and does not enqueue any jobs' do
        allow(described_class.new).to receive(:get_user_creative_data_for_wati_campaign).with(user, campaign_day3).and_return(nil)

        described_class.new.perform

        expect(GeneratePosterCampaignImage.jobs).to be_empty
      end
    end

    context 'when Redis has no users' do
      it 'does not enqueue any jobs' do
        described_class.new.perform
        expect(GeneratePosterCampaignImage.jobs).to be_empty
      end
    end

    context 'when user metadata is generated' do
      let(:user) { create(:user) }
      let(:creative_data) { UserMetadatum.new(user_id: user.id, key: "#{campaign_day3}_most_shared_creative_id", value: '123') }

      before do
        $redis.sadd(Constants.wati_redis_key_for_day(3), user.id)
        allow_any_instance_of(described_class).to receive(:get_user_creative_data_for_wati_campaign).and_return(creative_data)
      end

      it 'imports metadata using UserMetadatum.import' do
        expect(UserMetadatum).to receive(:import).with([creative_data], on_duplicate_key_ignore: true, batch_size: 100)
        described_class.new.perform
      end
    end

    context 'when an error occurs while processing a user' do
      let(:user) { create(:user) }

      before do
        $redis.sadd(Constants.wati_redis_key_for_day(3), user.id)
        allow_any_instance_of(described_class).to receive(:get_user_creative_data_for_wati_campaign)
                                                    .and_raise(StandardError.new('Something went wrong'))
      end

      it 'logs the error for that user' do
        expect(Rails.logger).to receive(:error).with(/\[Day 3\] Error for user #{user.id}: Something went wrong/)
        described_class.new.perform
      end
    end

    context 'when no users are present for the day in Redis' do
      before do
        redis_key = Constants.wati_redis_key_for_day(3)
        $redis.del(redis_key) # Ensure the Redis set is empty
      end

      it 'returns early without processing' do
        described_class.new.perform

        expect(GeneratePosterCampaignImage.jobs).to be_empty
      end
    end

    context "returns user metadata object" do
      let(:user) { create(:user) }
      it 'returns UserMetadatum object if creative_id is present' do
        allow(user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(123)

        result = described_class.new.send(:get_user_creative_data_for_wati_campaign, user, 'wati_trial_day_3_poster')

        expect(result).to be_a(UserMetadatum)
        expect(result.user_id).to eq(user.id)
        expect(result.key).to eq('wati_trial_day_3_poster_most_shared_creative_id')
        expect(result.value).to eq('123')
      end

    end

  end
end
