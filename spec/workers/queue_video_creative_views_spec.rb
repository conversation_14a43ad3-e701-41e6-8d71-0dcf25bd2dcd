# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueueVideoCreativeViews, type: :worker do
  let(:user_id) { 123 }
  let(:video_creative_id) { 456 }
  let(:timestamp) { Time.current.to_i }
  let(:invalid_id) { 0 }
  let(:invalid_timestamp) { 0 }

  describe 'sidekiq options' do
    it 'is configured with retry option set to 3' do
      expect(described_class.sidekiq_options['retry']).to eq(3)
    end

    it 'uses until_executed lock' do
      expect(described_class.sidekiq_options['lock']).to eq(:until_executed)
    end
  end

  describe '#perform' do
    before do
      # Clear Redis data before each test
      $redis.del(Constants.video_creative_views_redis_key)
      $redis.del(Constants.video_creative_views_queue_redis_key)
      $redis.del(Constants.user_date_video_creative_views_queue_redis_key(user_id))
    end

    context 'with valid parameters' do
      it 'increments the view count for the video creative' do
        described_class.new.perform(user_id, { video_creative_id => timestamp })

        expect($redis.hget(Constants.video_creative_views_redis_key, video_creative_id.to_s)).to eq('1')
      end

      it 'adds the video creative to user-date queue' do
        described_class.new.perform(user_id, { video_creative_id => timestamp })
        user_queue_key = Constants.user_date_video_creative_views_queue_redis_key(user_id)

        expect($redis.sismember(user_queue_key, video_creative_id.to_s)).to be true
      end

      it 'sets expiration for user-date queue' do
        described_class.new.perform(user_id, { video_creative_id => timestamp })
        user_queue_key = Constants.user_date_video_creative_views_queue_redis_key(user_id)

        expect($redis.ttl(user_queue_key)).to be > 0
      end

      it 'adds the view details to the views queue' do
        described_class.new.perform(user_id, { video_creative_id => timestamp })
        expected_entry = {
          video_creative_id: video_creative_id,
          user_id: user_id,
          viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S")
        }.to_json

        expect($redis.sismember(Constants.video_creative_views_queue_redis_key, expected_entry)).to be true
      end
    end

    context 'with invalid parameters' do
      it 'logs error when video_creative_id is 0' do
        allow_any_instance_of(described_class).to receive(:logger).and_return(Rails.logger)
        expect(Rails.logger).to receive(:error).with("QueueVideoCreativeViews: video_creative_id or timestamp is 0")
        described_class.new.perform(user_id, { invalid_id => timestamp })
      end

      it 'logs error when timestamp is 0' do
        allow_any_instance_of(described_class).to receive(:logger).and_return(Rails.logger)
        expect(Rails.logger).to receive(:error).with("QueueVideoCreativeViews: video_creative_id or timestamp is 0")
        described_class.new.perform(user_id, { video_creative_id => invalid_timestamp })
      end

      it 'does not process invalid entries' do
        described_class.new.perform(user_id, { invalid_id => invalid_timestamp })

        expect($redis.hlen(Constants.video_creative_views_redis_key)).to eq(0)
        expect($redis.scard(Constants.video_creative_views_queue_redis_key)).to eq(0)
      end
    end

    context 'with multiple video creatives' do
      let(:video_creative_ids_timestamp) do
        {
          100 => Time.current.to_i,
          200 => (Time.current - 1.hour).to_i,
          300 => (Time.current - 2.hours).to_i
        }
      end

      it 'processes all valid entries' do
        described_class.new.perform(user_id, video_creative_ids_timestamp)

        expect($redis.hlen(Constants.video_creative_views_redis_key)).to eq(3)
        expect($redis.scard(Constants.video_creative_views_queue_redis_key)).to eq(3)
      end
    end
  end
end
