require "rails_helper"
require "sidekiq/testing"

RSpec.describe TriggerDayWiseWatiMessagesForTrialUsers, type: :worker do
  context "trigger day wise wati messages test cases" do
    before do
      allow(Singular).to receive(:shorten_link).and_return("short-link")
      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
    end

    it "sends bulk messages for day 2 user" do
      user = create(:user)
      create(:user_plan, user: user, amount: 299)

      key = Constants.wati_redis_key_for_day(2)
      $redis.sadd(key, user.id)

      create(:poster_creative, id: 123, primary: true)

      allow(PosterShare).to receive_message_chain(:where, :distinct, :pluck).and_return([])

      allow(user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(123)
      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      allow_any_instance_of(described_class)
        .to receive(:organise_data_based_on_template)
              .and_return({
                            "day_2_template" => [
                              { user_id: user.id, phone: user.phone, name: user.name }
                            ]
                          })

      described_class.new.perform(key, 2)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).once
      expect(UserWatiCampaign).to have_received(:insert_all)
      expect(EventTracker).to have_received(:perform_async)
      expect($redis).to have_received(:hset)
    end

    it "sends bulk messages for in between days users" do
      user = create(:user)
      create(:user_plan, user: user, amount: 299)
      key = Constants.wati_redis_key_for_day(5)
      $redis.sadd(key, user.id)

      event = create(:event)
      creative = create(:poster_creative, event: event, primary: true)

      allow(Event).to receive(:upcoming_events_including_current).and_return([event])
      allow(event).to receive(:get_creative_for_wati_campaign).and_return(creative)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      described_class.new.perform(key, 5)
      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async)
      expect(UserWatiCampaign).to have_received(:insert_all)
      expect(EventTracker).to have_received(:perform_async)
      expect($redis).to have_received(:hset)
    end

    it "sends correct messages on day 3 to shared and not shared users with appropriate templates" do
      key = Constants.wati_redis_key_for_day(3)

      shared_user = create(:user, phone: "1234567890", name: "SharedUser")
      not_shared_user = create(:user, phone: "9876543210", name: "NotSharedUser")

      create(:user_plan, user: shared_user, amount: 299)
      create(:user_plan, user: not_shared_user, amount: 299)

      $redis.sadd(key, shared_user.id)
      $redis.sadd(key, not_shared_user.id)

      creative_101 = create(:poster_creative, id: 101, primary: true)
      create(:poster_share, user: shared_user, poster_creative: creative_101, actioned_at: Time.zone.now)
      allow(shared_user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(101)

      poster_image_url = "https://praja.app/poster.jpg"
      creative_map = create(:poster_creative, id: 202, primary: true)
      allow(creative_map).to receive(:event).and_return(create(:event, name: "Test Event"))

      worker_instance = described_class.new
      worker_instance.instance_variable_set(:@poster_image_urls, { not_shared_user.id => poster_image_url })
      worker_instance.instance_variable_set(:@hidden_poster_creative_ids, { not_shared_user.id => 202 })
      worker_instance.instance_variable_set(:@creative_map, { 202 => creative_map })

      allow(worker_instance).to receive(:organise_data_based_on_template)
                                  .and_return(
                                    { "day_3_template" => [{
                                                             phone: shared_user.phone,
                                                             name: shared_user.name,
                                                             user_id: shared_user.id
                                                           }] },
                                    { "hidden_poster_for_not_shared_users_v3" => [{
                                                                                    phone: not_shared_user.phone,
                                                                                    name: not_shared_user.name,
                                                                                    user_id: not_shared_user.id,
                                                                                    poster_image_url: poster_image_url
                                                                                  }] }
                                  )

      allow(UserMetadatum).to receive(:where).and_return(double(pluck: [], delete_all: true))
      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      worker_instance.perform(key, 3)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).twice

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).with(
        "day_3_template", a_string_starting_with("day_3_template_")
      )

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).with(
        "hidden_poster_for_not_shared_users_v3", a_string_starting_with("hidden_poster_for_not_shared_users_v3_")
      )
    end

    it "sends correct messages on day 4 to shared and not shared users with appropriate templates" do
      key = Constants.wati_redis_key_for_day(4)

      shared_user = create(:user, phone: "1234567890", name: "SharedUser")
      not_shared_user = create(:user, phone: "9876543210", name: "NotSharedUser")

      create(:user_plan, user: shared_user, amount: 299)
      create(:user_plan, user: not_shared_user, amount: 299)

      $redis.sadd(key, shared_user.id)
      $redis.sadd(key, not_shared_user.id)

      creative_shared = create(:poster_creative, id: 201, primary: true)
      hidden_creative = create(:poster_creative, id: 202, primary: true)
      create(:poster_share, user: shared_user, poster_creative: creative_shared, actioned_at: Time.zone.now)

      allow(shared_user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(201)
      allow(not_shared_user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(nil)

      worker = described_class.new
      worker.instance_variable_set(:@poster_image_urls, { not_shared_user.id => "https://praja.app/poster.jpg" })
      worker.instance_variable_set(:@hidden_poster_creative_ids, { not_shared_user.id => 202 })
      worker.instance_variable_set(:@creative_map, { 202 => hidden_creative })

      allow(hidden_creative).to receive(:event).and_return(create(:event, name: "Hidden Event"))

      allow(worker).to receive(:organise_data_based_on_template)
                         .and_return(
                           { "day_4_template" => [{
                                                    phone: shared_user.phone,
                                                    name: shared_user.name,
                                                    user_id: shared_user.id
                                                  }] },
                           { "hidden_poster_for_not_shared_users_v3" => [{
                                                                           phone: not_shared_user.phone,
                                                                           name: not_shared_user.name,
                                                                           user_id: not_shared_user.id,
                                                                           poster_image_url: "https://praja.app/poster.jpg"
                                                                         }] }
                         )

      creatives_relation = PosterCreative.where(id: [201, 202])
      allow(PosterCreative).to receive(:includes).with(:event).and_return(creatives_relation)

      allow(UserMetadatum).to receive(:where).and_return(double(pluck: [], delete_all: true))
      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      worker.perform(key, 4)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).twice

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).with(
        "day_4_template", a_string_starting_with("day_4_template_")
      )

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).with(
        "hidden_poster_for_not_shared_users_v3", a_string_starting_with("hidden_poster_for_not_shared_users_v3_")
      )
    end

    it "handles day 11: sends bulk and logs not shared to Google Sheets" do
      shared_user = create(:user)
      not_shared_user = create(:user)
      create(:user_plan, user: shared_user, amount: 299)
      create(:user_plan, user: not_shared_user, amount: 299)

      creative_201 = create(:poster_creative, id: 201, primary: true)
      create(:poster_share, user: shared_user, poster_creative: creative_201, actioned_at: Time.zone.now)

      allow(shared_user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(201)
      allow(not_shared_user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(nil)

      key = Constants.wati_redis_key_for_day(11)
      $redis.sadd(key, shared_user.id)
      $redis.sadd(key, not_shared_user.id)

      allow_any_instance_of(described_class)
        .to receive(:organise_data_based_on_template)
              .and_return({
                            "day_11_template" => [
                              { user_id: shared_user.id, phone: shared_user.phone, name: shared_user.name },
                              { user_id: not_shared_user.id, phone: not_shared_user.phone, name: not_shared_user.name }
                            ]
                          })

      allow(PosterCreative).to receive(:includes).with(:event).and_return(PosterCreative.where(id: 201))
      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(ExportDataToGoogleSheets).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      described_class.new.perform(key, 11)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).at_least(:once)
      expect(ExportDataToGoogleSheets).to have_received(:perform_async).at_least(:once)
    end

    it "handles day 13: sends only to users who did not share" do
      shared_user = create(:user)
      not_shared_user = create(:user)
      create(:user_plan, user: shared_user, amount: 299)
      create(:user_plan, user: not_shared_user, amount: 299)

      creative_201 = create(:poster_creative, id: 201, primary: true)
      create(:poster_share, user: shared_user, poster_creative: creative_201, actioned_at: Time.zone.now)

      key = Constants.wati_redis_key_for_day(13)
      $redis.sadd(key, shared_user.id)
      $redis.sadd(key, not_shared_user.id)

      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      allow_any_instance_of(described_class)
        .to receive(:organise_data_based_on_template)
              .and_return({ "day_13_template" => [{ user_id: not_shared_user.id, phone: not_shared_user.phone, name: not_shared_user.name }] })

      described_class.new.perform(key, 13)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).once
    end

    it "sends messages for shared and not shared users with active subscription on day 14" do
      shared_user = create(:user)
      not_shared_user = create(:user)

      create(:user_plan, user: shared_user, amount: 299)
      create(:user_plan, user: not_shared_user, amount: 299)
      creative_201 = create(:poster_creative, id: 201, primary: true)
      creative_202 = create(:poster_creative, id: 202, primary: true)
      create(:poster_share, user: shared_user, poster_creative: creative_201, actioned_at: Time.zone.now)
      allow(not_shared_user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(creative_202.id)

      create(:subscription, user: shared_user, status: "active")
      create(:subscription, user: not_shared_user, status: "active")

      key = Constants.wati_redis_key_for_day(14)
      $redis.sadd(key, shared_user.id)
      $redis.sadd(key, not_shared_user.id)

      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)
      allow(Singular).to receive(:shorten_link).and_return("premium-benefits-loss_screen_deep_link")

      allow_any_instance_of(described_class)
        .to receive(:organise_data_based_on_template).with([not_shared_user], 'not_shared_day_14')
              .and_return({ "day_14_template" => [{ user_id: not_shared_user.id, phone: not_shared_user.phone, name: not_shared_user.name, amount: not_shared_user.user_plan.amount, trial_extend_link: "premium-benefits-loss_screen_deep_link" }] })

      described_class.new.perform(key, 14)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).once
    end

    it "sends messages for shared and not shared users with inactive subscription on day 15" do
      shared_user = create(:user)
      not_shared_user = create(:user)
      user = create(:user)

      create(:user_plan, user: shared_user, amount: 299)
      create(:user_plan, user: not_shared_user, amount: 299)
      create(:user_plan, user: user, amount: 299)
      creative_201 = create(:poster_creative, id: 201, primary: true)
      create(:poster_share, user: shared_user, poster_creative: creative_201, actioned_at: Time.zone.now)

      create(:subscription, user: shared_user, status: "closed")
      create(:subscription, user: not_shared_user, status: "closed")
      create(:subscription, user: user, status: "active")

      key = Constants.wati_redis_key_for_day(15)
      $redis.sadd(key, shared_user.id)
      $redis.sadd(key, not_shared_user.id)
      $redis.sadd(key, user.id)

      allow(SendWhatsappMessagesUsingBulkApi).to receive(:perform_async)
      allow(UserWatiCampaign).to receive(:insert_all)
      allow(EventTracker).to receive(:perform_async)
      allow($redis).to receive(:hset)

      allow_any_instance_of(described_class)
        .to receive(:organise_data_based_on_template)
              .and_return({
                            "day_15_template" => [
                              { user_id: shared_user.id, phone: shared_user.phone, name: shared_user.name, amount: user.user_plan.amount },
                              { user_id: not_shared_user.id, phone: not_shared_user.phone, name: not_shared_user.name, amount: user.user_plan.amount }
                            ]
                          })

      described_class.new.perform(key, 15)

      expect(SendWhatsappMessagesUsingBulkApi).to have_received(:perform_async).twice
    end

    it "returns early when redis set is empty" do
      key = Constants.wati_redis_key_for_day(2)
      $redis.del(key)

      expect(SendWhatsappMessagesUsingBulkApi).not_to receive(:perform_async)
      described_class.new.perform(key, 2)
    end

    it "skips users in get_hidden_poster_templates if poster_image_url or creative_id is missing" do
      user = create(:user)
      worker = described_class.new
      worker.instance_variable_set(:@poster_image_urls, {})
      worker.instance_variable_set(:@hidden_poster_creative_ids, { user.id => nil })
      result = worker.send(:get_hidden_poster_templates, [user])
      expect(result["hidden_poster_for_not_shared_users_v3"]).to be_nil
    end

    it "selects correct template when event is nil for day_2" do
      template = described_class.new.send(:select_template, "day_2", nil)
      expect(template).to eq("trial_to_payment_day_2_without_event_v2")
    end

    it "selects correct template when event is present for day_2" do
      template = described_class.new.send(:select_template, "day_2", 123)
      expect(template).to eq("trial_to_payment_day_2_with_event_v2")
    end

    it "selects correct template when event is nil for day_3" do
      template = described_class.new.send(:select_template, "day_3", nil)
      expect(template).to eq("trial_to_payment_day_3_without_event_v2")
    end

    it "selects correct template when event is present for day_3" do
      template = described_class.new.send(:select_template, "day_3", 123)
      expect(template).to eq("trial_to_payment_day_3_with_event_v3")
    end

    it "selects correct template when event is nil for day_4" do
      template = described_class.new.send(:select_template, "day_4", nil)
      expect(template).to eq("trial_to_payment_day_4_without_event_v1")
    end

    it "selects correct template when event is present for day_4" do
      template = described_class.new.send(:select_template, "day_4", 123)
      expect(template).to eq("trial_to_payment_day_4_with_event_v2")
    end

    it "selects correct template for day 11" do
      template = described_class.new.send(:select_template, "day_11", 123)
      expect(template).to eq("trial_to_payment_day_11_v2")
    end

    it "selects correct template for day 13" do
      template = described_class.new.send(:select_template, "day_13", 123)
      expect(template).to eq("trial_to_payment_day_13_v1")
    end

    it "selects correct template for not_shared_day_14" do
      template = described_class.new.send(:select_template, "not_shared_day_14", 123)
      expect(template).to eq("trial_to_payment_not_shared_autopay_active_day_14_v6")
    end

    it "selects correct template for shared_day_15" do
      template = described_class.new.send(:select_template, "shared_day_15", 123)
      expect(template).to eq("trial_to_payment_shared_autopay_inactive_day_15_v3")
    end

    it "selects correct template for not_shared_day_15" do
      template = described_class.new.send(:select_template, "not_shared_day_15", 123)
      expect(template).to eq("trial_to_payment_not_shared_autopay_inactive_day_15_v2")
    end

    it "skips user in build_data_by_template when no creative or user data is returned" do
      user = create(:user)
      allow(user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(123)
      allow(PosterCreative).to receive(:includes).and_return(double(where: {}))

      data = described_class.new.send(:build_data_by_template, [user], "day_2")
      expect(data).to be_empty
    end

    it "generates premium experience deeplink" do
      allow(Singular).to receive(:shorten_link).and_return("short-link")
      result = described_class.new.send(:get_premium_experience_screen_deep_link)
      expect(result).to eq("short-link")
    end

    it "generates the premium benefits screen deeplink" do
      allow(Singular).to receive(:shorten_link).and_return("premium-benefits-loss_screen_deep_link")
      result = described_class.new.send(:get_premium_benefits_loss_screen_deep_link)
      expect(result).to eq("premium-benefits-loss_screen_deep_link")
    end

    it "logs error and notifies Honeybadger if Google Sheets export fails" do
      user = create(:user)
      allow(ExportDataToGoogleSheets).to receive(:perform_async).and_raise(StandardError.new("sheet error"))
      allow(Honeybadger).to receive(:notify)
      allow(Rails.logger).to receive(:error)

      expect {
        described_class.new.send(:save_info_to_google_sheets, [user])
      }.not_to raise_error

      expect(Honeybadger).to have_received(:notify)
      expect(Rails.logger).to have_received(:error).with(/sheet error/)
    end

    it "generates deeplink without event_id if event_id is nil" do
      creative_id = 123
      expected_path = "praja://buzz.praja.app/posters/layout?creative_id=#{creative_id}"

      allow_any_instance_of(described_class)
        .to receive(:generate_singular_link)
              .with(expected_path)
              .and_return("short-link")

      worker = described_class.new
      link = worker.send(:get_deep_link_for_event_and_creative, nil, creative_id)
      expect(link).to eq("short-link")
    end

    it "generates deeplink with event_id when present" do
      event_id = 45
      creative_id = 123
      expected_path = "praja://buzz.praja.app/posters/layout?id=#{event_id}&creative_id=#{creative_id}"

      allow_any_instance_of(described_class)
        .to receive(:generate_singular_link)
              .with(expected_path)
              .and_return("short-link")

      worker = described_class.new
      link = worker.send(:get_deep_link_for_event_and_creative, event_id, creative_id)
      expect(link).to eq("short-link")
    end

    describe "#organise_data_based_on_template" do
      let(:worker) { described_class.new }
      let(:users) { [double("User", id: 1)] }

      it "calls get_hidden_poster_templates for not_shared_day_3" do
        expect(worker).to receive(:get_hidden_poster_templates).with(users)
        worker.send(:organise_data_based_on_template, users, 'not_shared_day_3')
      end

      it "calls get_hidden_poster_templates for not_shared_day_4" do
        expect(worker).to receive(:get_hidden_poster_templates).with(users)
        worker.send(:organise_data_based_on_template, users, 'not_shared_day_4')
      end

      it "calls build_data_by_template with source for in_between_days" do
        expect(worker).to receive(:build_data_by_template).with(users, 'in_between_days', source: :upcoming_event)
        worker.send(:organise_data_based_on_template, users, 'in_between_days')
      end

      it "calls build_data_by_template with paywall and get_amount for shared_day_15" do
        expect(worker).to receive(:build_data_by_template).with(users, 'shared_day_15', paywall: true, get_amount: true)
        worker.send(:organise_data_based_on_template, users, 'shared_day_15')
      end

      it "calls build_data_by_template with paywall and get_amount for not_shared_day_15" do
        expect(worker).to receive(:build_data_by_template).with(users, 'not_shared_day_15', paywall: true, get_amount: true)
        worker.send(:organise_data_based_on_template, users, 'not_shared_day_15')
      end

      it "calls build_data_by_template with get_amount for not_shared_day_14" do
        expect(worker).to receive(:build_data_by_template).with(users, 'not_shared_day_14', get_amount: true)
        worker.send(:organise_data_based_on_template, users, 'not_shared_day_14')
      end

      it "calls build_data_by_template with no extra args for other tags" do
        expect(worker).to receive(:build_data_by_template).with(users, 'some_random_tag')
        worker.send(:organise_data_based_on_template, users, 'some_random_tag')
      end
    end

    describe "#build_data_by_template" do
      let(:worker) { described_class.new }
      let(:user) { double("User", id: 1) }
      let(:creative) { double("PosterCreative", id: 42, event: event) }
      let(:event) { double("Event", id: 99) }

      before do
        allow(user).to receive(:today_most_shared_poster_related_to_user_circles).and_return(42)
        allow(PosterCreative).to receive_message_chain(:includes, :where, :index_by).and_return({ 42 => creative })
        allow(worker).to receive(:build_user_template_data).with(user, event, creative, 'tag_x', paywall: false, get_amount: false).and_return(["template_1", { user_id: 1 }])
      end

      it "builds data_by_template hash with template and user_data for poster source" do
        result = worker.send(:build_data_by_template, [user], 'tag_x')
        expect(result).to eq({ "template_1" => [{ user_id: 1 }] })
      end
    end

    describe "#get_hidden_poster_templates" do
      let(:worker) { described_class.new }
      let(:user) { double("User", id: 1, name: "Test User", phone: "9999999999") }
      let(:creative) { double("Creative", id: 101, event: event) }
      let(:event) { double("Event", id: 202) }

      before do
        worker.instance_variable_set(:@poster_image_urls, { 1 => "https://image.url/poster.jpg" })
        worker.instance_variable_set(:@hidden_poster_creative_ids, { 1 => 101 })
        worker.instance_variable_set(:@creative_map, { 101 => creative })
        allow(worker).to receive(:get_deep_link_for_event_and_creative)
                           .with(event.id, 101)
                           .and_return("praja://some-deep-link")
      end

      it "adds user data to template when all conditions are met" do
        result = worker.send(:get_hidden_poster_templates, [user])

        expect(result).to eq({ "hidden_poster_for_not_shared_users_v3" => [{
                                                                             user_id: 1,
                                                                             name: "Test User",
                                                                             phone: "9999999999",
                                                                             poster_image_url: "https://image.url/poster.jpg",
                                                                             cta_link: "praja://some-deep-link"
                                                                           }]})
      end

      it "skips user if poster_image_url is blank" do
        worker.instance_variable_set(:@poster_image_urls, { 1 => nil })
        result = worker.send(:get_hidden_poster_templates, [user])
        expect(result).to eq({})
      end

      it "skips user if creative_id is blank" do
        worker.instance_variable_set(:@hidden_poster_creative_ids, { 1 => nil })
        result = worker.send(:get_hidden_poster_templates, [user])
        expect(result).to eq({})
      end

      it "skips user if creative is not found in map" do
        worker.instance_variable_set(:@creative_map, {})
        result = worker.send(:get_hidden_poster_templates, [user])
        expect(result).to eq({})
      end

      it "omits cta_link if deeplink is blank" do
        allow(worker).to receive(:get_deep_link_for_event_and_creative).and_return(nil)
        result = worker.send(:get_hidden_poster_templates, [user])

        expect(result["hidden_poster_for_not_shared_users_v3"].first).not_to have_key(:cta_link)
      end
    end

    it "correctly maps UserMetadatum keys to @poster_image_urls and @hidden_poster_creative_ids" do
      campaign_name = "wati_trial_day_3_poster"
      user_id = 123
      poster_url_key = Constants.poster_image_url(campaign_name)
      creative_id_key = "#{campaign_name}_most_shared_creative_id"

      worker = described_class.new
      worker.instance_variable_set(:@day, 3)
      worker.instance_variable_set(:@campaign_name, campaign_name)
      worker.instance_variable_set(:@user_ids, [user_id])

      allow(worker).to receive(:users).and_return([double("User", id: user_id)])
      allow(worker).to receive(:shared_today_user_ids).and_return([])

      allow(UserMetadatum).to receive(:where).with(user_id: [user_id], key: [poster_url_key, creative_id_key])
                                             .and_return(double(pluck: [
                                               [user_id, poster_url_key, "https://test.image.url"],
                                               [user_id, creative_id_key, "456"]
                                             ]))

      allow(PosterCreative).to receive_message_chain(:includes, :where, :index_by).and_return({})
      allow(worker).to receive(:send_bulk_api_per_template)

      worker.send(:handle_day_3_or_4)

      expect(worker.instance_variable_get(:@poster_image_urls)).to eq({ user_id => "https://test.image.url" })
      expect(worker.instance_variable_get(:@hidden_poster_creative_ids)).to eq({ user_id => 456 })
    end

  end
end
