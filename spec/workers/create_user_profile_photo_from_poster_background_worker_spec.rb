# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateUserProfilePhotoFromPosterBackgroundWorker, type: :worker do
  subject { described_class.new }

  let(:user) { create(:user, photo: nil) }
  let(:photo) { create(:photo, path: 'test/path/image.jpg', user: user) }

  before do
    allow(<PERSON>bad<PERSON>).to receive(:notify)
    allow(Honeybadger).to receive(:context)
    allow(Rails.logger).to receive(:warn)
    allow(Rails.logger).to receive(:error)
    allow(Rails.logger).to receive(:info)
  end

  describe '#perform' do
    context 'when user not found' do
      it 'logs warning and returns without raising error' do
        expect(Rails.logger).to receive(:warn).with(/User not found with id 999/)

        expect { subject.perform(999) }.not_to raise_error
      end
    end

    context 'when user already has profile photo' do
      let(:existing_photo) { create(:photo, user: user) }

      before { user.update(photo: existing_photo) }

      it 'logs info and returns early' do
        expect(Rails.logger).to receive(:info).with(/already has profile photo, skipping/)

        subject.perform(user.id)
      end

      it 'does not create new photo' do
        expect { subject.perform(user.id) }.not_to change(Photo, :count)
      end
    end

    context 'when poster_photo_with_background is nil' do
      before { user.update(poster_photo_with_background: nil) }

      it 'returns early without creating photo' do
        expect { subject.perform(user.id) }.not_to change(Photo, :count)
      end
    end

    context 'with valid Photo source' do
      before { user.update(poster_photo_with_background: photo) }

      it 'creates new profile photo successfully' do
        expect { subject.perform(user.id) }.to change(Photo, :count).by(1)

        user.reload
        expect(user.photo).to be_present
        expect(user.photo.url).to eq(photo.url)
        expect(user.photo.user_id).to eq(user.id)
        expect(user.photo.service).to eq('aws')
        expect(user.photo.explicit).to be_falsey
      end

      it 'notifies Honeybadger if new photo has blank path' do
        # Mock a photo that will be created without a path
        allow_any_instance_of(Photo).to receive(:path).and_return('')

        expect(Honeybadger).to receive(:notify).with("New photo created but path is blank for user #{user.id}")

        subject.perform(user.id)
      end
    end
  end

  describe 'sidekiq_retries_exhausted' do
    it 'notifies Honeybadger and logs error' do
      msg = { "args" => [user.id] }
      ex = StandardError.new("Test error")

      expect(Honeybadger).to receive(:notify).with(ex, context: {
        worker: 'CreateUserProfilePhotoFromPosterBackgroundWorker',
        user_id: user.id,
        args: [user.id]
      })
      expect(Rails.logger).to receive(:error).with(/retries exhausted for user #{user.id}/)

      described_class.sidekiq_retries_exhausted_block.call(msg, ex)
    end
  end
end