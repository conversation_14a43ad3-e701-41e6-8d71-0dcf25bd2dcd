# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PosterPhotoSuitabilityEvaluatorWorker, type: :worker do
  subject { described_class.new }

  let(:user) { create(:user, name: '<PERSON><PERSON><PERSON>') }
  let(:photo) { create(:photo, url: 'https://example.com/test-image.jpg', user: user) }

  before do
    allow(Honeybadger).to receive(:notify)
    allow(Honeybadger).to receive(:context)
    allow(Rails.logger).to receive(:warn)
    allow(Rails.logger).to receive(:error)
  end

  describe '#perform' do
    context 'when user not found' do
      it 'returns early without raising error' do
        expect { subject.perform(999) }.not_to raise_error
      end
    end

    context 'when user has no poster_photo_with_background' do
      before { user.update(poster_photo_with_background: nil) }

      it 'returns early without calling OpenAI' do
        expect(subject).not_to receive(:evaluate_poster_photo_suitability)
        subject.perform(user.id)
      end
    end

    context 'when user has valid poster_photo_with_background' do
      let(:mock_response) do
        {
          "choices" => [
            {
              "message" => {
                "content" => JSON.generate({ "is_human" => true,
                                             "single_subject" => true,
                                             "gender" => "male",
                                             "clear_face" => true,
                                             "religion_from_name" => "Hindu",
                                             "religion_from_photo" => "Hindu",
                                             "suitable_for_generation" => true
                                           })
              }
            }
          ]
        }
      end

      before do
        user.update(poster_photo_with_background: photo)
        allow(subject).to receive(:evaluate_poster_photo_suitability).and_return(mock_response)
      end

      it 'calls OpenAI API with correct parameters' do
        expect(subject).to receive(:evaluate_poster_photo_suitability).with(photo.url, user.name)
        subject.perform(user.id)
      end

      it 'stores only suitable_for_generation in UserMetadatum' do
        expect { subject.perform(user.id) }.to change(UserMetadatum, :count).by(2)

        # Verify only suitable_for_generation metadata was created
        expect(UserMetadatum.find_by(user: user, key: Constants.poster_photo_suitability_for_generation_key).value).to eq('true')

        # Verify other metadata was not created
        expect(UserMetadatum.where(user: user).where.not(key: Constants.poster_photo_suitability_for_generation_key).count).to eq(1)
      end

      it 'logs all evaluation data' do
        expect(Rails.logger).to receive(:warn).with(/Poster photo suitability evaluation completed for user #{user.id}.*is_human: true.*single_subject: true.*gender: male.*clear_face: true.*religion_from_name: Hindu.*religion_from_photo: Hindu.*suitable_for_generation: true/)
        subject.perform(user.id)
      end
    end

    context 'when OpenAI API raises an error' do
      before do
        user.update(poster_photo_with_background: photo)
        allow(subject).to receive(:evaluate_poster_photo_suitability).and_raise(StandardError, 'API Error')
      end

      it 'logs the error and re-raises for Sidekiq retry' do
        expect(Rails.logger).to receive(:error).with(/PosterPhotoSuitabilityEvaluatorWorker failed for user #{user.id}/)

        expect { subject.perform(user.id) }.to raise_error(StandardError, 'API Error')
      end

      it 'does not create any UserMetadatum records when error occurs' do
        expect {
          begin
            subject.perform(user.id)
          rescue StandardError
            # Ignore the re-raised error for this test
          end
        }.not_to change(UserMetadatum, :count)
      end
    end

    context 'when OpenAI returns invalid JSON' do
      let(:invalid_response) do
        {
          "choices" => [
            {
              "message" => {
                "content" => "Invalid JSON content"
              }
            }
          ]
        }
      end

      before do
        user.update(poster_photo_with_background: photo)
        allow(subject).to receive(:evaluate_poster_photo_suitability).and_return(invalid_response)
      end

      it 'logs the JSON parsing error and re-raises for Sidekiq retry' do
        expect(Rails.logger).to receive(:error).with(/Failed to parse poster photo suitability evaluation response for user #{user.id}/)

        expect { subject.perform(user.id) }.to raise_error(JSON::ParserError)
      end

      it 'does not create any UserMetadatum records when JSON parsing fails' do
        expect {
          begin
            subject.perform(user.id)
          rescue JSON::ParserError
            # Ignore the re-raised error for this test
          end
        }.not_to change(UserMetadatum, :count)
      end
    end
  end
end
