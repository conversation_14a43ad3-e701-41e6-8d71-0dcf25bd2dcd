# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MediaService::AddAndSaveGreyBgImageWorker, type: :worker do
  let(:user) { create(:user) }
  let(:poster_photo) { create(:photo, user: user) }
  let(:captured_image_response) { { 'cdn_url' => 'https://a-cdn.thecircleapp.in/captured-image.jpg' } }

  before do
    allow(Honeybadger).to receive(:context)
    allow(Honeybadger).to receive(:notify)
    allow(Rails.logger).to receive(:info)
    allow(Rails.logger).to receive(:warn)
    allow(Rails.logger).to receive(:error)
  end

  describe '#perform' do
    context 'when user exists and has poster_photo' do
      before do
        user.update!(poster_photo: poster_photo)
        allow(subject).to receive(:capture_html_as_image).and_return(captured_image_response)
      end

      it 'processes successfully and creates photo with grey background' do
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        expect { subject.perform(user.id) }.to change { Photo.count }.by(1)

        new_photo = Photo.last
        expect(new_photo.user_id).to eq(user.id)
        expect(new_photo.explicit).to be_falsey
        expect(new_photo.service).to eq('aws')
        expect(new_photo.url).to eq(captured_image_response['cdn_url'])
      end

      it 'updates user poster_photo_with_background field' do
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        subject.perform(user.id)
        user.reload
        expect(user.poster_photo_with_background).to eq(Photo.last)
      end

      it 'updates user photo field when it is nil' do
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        expect(user.photo).to be_nil
        subject.perform(user.id)
        user.reload
        expect(user.photo).to eq(Photo.last)
      end

      it 'does not update user photo field when it already exists' do
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        existing_photo = create(:photo, user: user)
        user.update!(photo: existing_photo)

        subject.perform(user.id)
        user.reload
        expect(user.photo).to eq(existing_photo)
        expect(user.poster_photo_with_background).to eq(Photo.last)
      end

      it 'generates correct HTML with grey background' do
        # Mock the user lookup and poster_photo to return a predictable placeholder_url
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        expected_html = <<~HTML
          <div id="outer-container" style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%;">
            <div id="grey-container" style="background-color: #808080; display: inline-block;">
              <img src="https://example.com/photo.jpg" style="max-width: 100%; max-height: 100%; object-fit: contain;" />
            </div>
          </div>
        HTML

        expect(subject).to receive(:capture_html_as_image).with(expected_html, '#grey-container')
        subject.perform(user.id)
      end

      it 'logs processing start and success' do
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Starting processing for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Capturing HTML as image for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Creating Photo record for user #{user.id}")
        expect(Rails.logger).to receive(:info).with(/AddAndSaveGreyBgImageWorker: Created Photo \d+ for user #{user.id}/)
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Updating user fields for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Updated poster_photo_with_background for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Updated photo field for user #{user.id}")
        expect(Rails.logger).to receive(:info).with(/AddAndSaveGreyBgImageWorker: Successfully processed user #{user.id}, created photo \d+/)

        subject.perform(user.id)
      end

      it 'sets Honeybadger context with user_id' do
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        
        expect(Honeybadger).to receive(:context).with({ user_id: user.id })
        subject.perform(user.id)
      end
    end

    context 'when user does not exist' do
      it 'logs warning and exits gracefully' do
        expect(Rails.logger).to receive(:warn).with("AddAndSaveGreyBgImageWorker: User not found with id 999")
        expect { subject.perform(999) }.not_to change { Photo.count }
      end

      it 'does not raise an error' do
        expect { subject.perform(999) }.not_to raise_error
      end
    end

    context 'when user has no poster_photo' do
      it 'logs info message and exits gracefully' do
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: User #{user.id} has no poster_photo, skipping")
        expect { subject.perform(user.id) }.not_to change { Photo.count }
      end

      it 'does not raise an error' do
        expect { subject.perform(user.id) }.not_to raise_error
      end
    end

    context 'when poster_photo has no placeholder_url' do
      before do
        user.update!(poster_photo: poster_photo)
        # Mock User.find_by_id to return a user with a poster_photo that has no placeholder_url
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return(nil)
      end

      it 'logs warning and exits gracefully' do
        expect(Rails.logger).to receive(:warn).with("AddAndSaveGreyBgImageWorker: User #{user.id} poster_photo has no placeholder_url, skipping")
        expect(subject).not_to receive(:capture_html_as_image)
        expect { subject.perform(user.id) }.not_to change { Photo.count }
      end

      it 'does not raise an error' do
        expect(subject).not_to receive(:capture_html_as_image)
        expect { subject.perform(user.id) }.not_to raise_error
      end
    end

    context 'when capture service fails' do
      before do
        user.update!(poster_photo: poster_photo)
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        allow(subject).to receive(:capture_html_as_image).and_return({})
      end

      it 'raises an error with context' do
        expect { subject.perform(user.id) }.to raise_error(/Capture service did not return cdn_url/)
      end

      it 'notifies Honeybadger with context' do
        expect(Honeybadger).to receive(:notify).with(
          an_instance_of(RuntimeError),
          context: { user_id: user.id }
        )

        expect { subject.perform(user.id) }.to raise_error
      end

      it 'logs error message' do
        expect(Rails.logger).to receive(:error).with(/AddAndSaveGreyBgImageWorker: Error processing user #{user.id}/)
        expect { subject.perform(user.id) }.to raise_error
      end
    end

    context 'when Photo creation fails' do
      before do
        user.update!(poster_photo: poster_photo)
        allow(User).to receive(:find_by_id).with(user.id).and_return(user)
        allow(user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        allow(subject).to receive(:capture_html_as_image).and_return(captured_image_response)
        allow_any_instance_of(Photo).to receive(:save!).and_raise(ActiveRecord::RecordInvalid.new(Photo.new))
      end

      it 'raises an error and notifies Honeybadger' do
        expect(Honeybadger).to receive(:notify).with(
          an_instance_of(ActiveRecord::RecordInvalid),
          context: { user_id: user.id }
        )

        expect { subject.perform(user.id) }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end

    context 'when user update fails' do
      before do
        user.update!(poster_photo: poster_photo)
        allow(subject).to receive(:capture_html_as_image).and_return(captured_image_response)
        # Mock User.find_by_id to return a user that will fail on update
        failing_user = user
        allow(User).to receive(:find_by_id).with(user.id).and_return(failing_user)
        allow(failing_user).to receive(:poster_photo).and_return(poster_photo)
        allow(poster_photo).to receive(:placeholder_url).and_return('https://example.com/photo.jpg')
        allow(failing_user).to receive(:update!).and_raise(ActiveRecord::RecordInvalid.new(failing_user))
      end

      it 'raises an error and notifies Honeybadger' do
        expect(Honeybadger).to receive(:notify).with(
          an_instance_of(ActiveRecord::RecordInvalid),
          context: { user_id: user.id }
        )

        expect { subject.perform(user.id) }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
  end

  describe 'Sidekiq configuration' do
    it 'includes required modules' do
      expect(described_class.ancestors).to include(Sidekiq::Worker)
      expect(described_class.ancestors).to include(Sidekiq::Throttled::Worker)
      expect(described_class.ancestors).to include(Sidekiq::AssuredJobs::Worker)
      expect(described_class.ancestors).to include(Capture)
    end

    it 'has correct sidekiq options' do
      expect(described_class.sidekiq_options_hash['queue']).to eq(:critical)
      expect(described_class.sidekiq_options_hash['retry']).to eq(3)
      expect(described_class.sidekiq_options_hash['lock']).to eq(:until_and_while_executing)
      expect(described_class.sidekiq_options_hash['on_conflict']).to eq(:log)
    end

    it 'has throttling configured' do
      # Check that throttling is configured (exact implementation may vary)
      expect(described_class.ancestors).to include(Sidekiq::Throttled::Worker)
    end
  end

  describe 'retries exhausted callback' do
    let(:msg) { { 'args' => [user.id] } }
    let(:exception) { StandardError.new('Test error') }

    it 'notifies Honeybadger with context' do
      expect(Honeybadger).to receive(:notify).with(
        exception,
        context: {
          worker: 'AddAndSaveGreyBgImageWorker',
          user_id: user.id,
          args: [user.id]
        }
      )

      described_class.sidekiq_retries_exhausted_block.call(msg, exception)
    end

    it 'logs error message' do
      expect(Rails.logger).to receive(:error).with("AddAndSaveGreyBgImageWorker retries exhausted for user #{user.id}: Test error")
      described_class.sidekiq_retries_exhausted_block.call(msg, exception)
    end
  end

  describe 'private methods' do
    describe '#generate_html' do
      it 'generates HTML with grey background and centered image' do
        photo_url = 'https://example.com/test.jpg'
        html = subject.send(:generate_html, photo_url)

        expect(html).to include('id="outer-container"')
        expect(html).to include('background-color: #808080')
        expect(html).to include('display: flex')
        expect(html).to include('align-items: center')
        expect(html).to include('justify-content: center')
        expect(html).to include("src=\"#{photo_url}\"")
        expect(html).to include('object-fit: contain')
      end
    end

    describe '#create_photo_from_url' do
      let(:image_url) { 'https://a-cdn.thecircleapp.in/captured.jpg' }

      it 'creates Photo with correct attributes' do
        photo = subject.send(:create_photo_from_url, user, image_url)

        expect(photo.url).to eq(image_url)
        expect(photo.user_id).to eq(user.id)
        expect(photo.explicit).to be_falsey
        expect(photo.service).to eq('aws')
        expect(photo).to be_persisted
      end

      it 'logs Photo creation' do
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Creating Photo record for user #{user.id}")
        expect(Rails.logger).to receive(:info).with(/AddAndSaveGreyBgImageWorker: Created Photo \d+ for user #{user.id}/)

        subject.send(:create_photo_from_url, user, image_url)
      end
    end

    describe '#update_user_photo_fields' do
      let(:new_photo) { create(:photo, user: user) }

      it 'always updates poster_photo_with_background' do
        subject.send(:update_user_photo_fields, user, new_photo)
        user.reload
        expect(user.poster_photo_with_background).to eq(new_photo)
      end

      it 'updates photo field when nil' do
        expect(user.photo).to be_nil
        subject.send(:update_user_photo_fields, user, new_photo)
        user.reload
        expect(user.photo).to eq(new_photo)
      end

      it 'does not update photo field when already present' do
        existing_photo = create(:photo, user: user)
        user.update!(photo: existing_photo)

        subject.send(:update_user_photo_fields, user, new_photo)
        user.reload
        expect(user.photo).to eq(existing_photo)
        expect(user.poster_photo_with_background).to eq(new_photo)
      end

      it 'logs field updates appropriately' do
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Updating user fields for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Updated poster_photo_with_background for user #{user.id}")
        expect(Rails.logger).to receive(:info).with("AddAndSaveGreyBgImageWorker: Updated photo field for user #{user.id}")

        subject.send(:update_user_photo_fields, user, new_photo)
      end
    end
  end
end

