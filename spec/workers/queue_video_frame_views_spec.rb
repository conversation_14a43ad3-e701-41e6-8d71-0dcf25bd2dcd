# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueueVideoFrameViews, type: :worker do
  let(:user_id) { 123 }
  let(:video_frame_id) { 456 }
  let(:timestamp) { Time.current.to_i }
  let(:invalid_id) { 0 }
  let(:invalid_timestamp) { 0 }

  describe 'sidekiq options' do
    it 'is configured with retry option set to 3' do
      expect(described_class.sidekiq_options['retry']).to eq(3)
    end

    it 'uses until_executed lock' do
      expect(described_class.sidekiq_options['lock']).to eq(:until_executed)
    end
  end

  describe '#perform video frame views' do
    let(:worker) { described_class.new }

    before do
      # Clear Redis data before each test
      $redis.del(Constants.video_frame_views_redis_key)
      $redis.del(Constants.video_frame_views_queue_redis_key)
      $redis.del(Constants.user_date_video_frame_views_queue_redis_key(user_id))

      # Stub logger for error tests
      allow(worker).to receive(:logger).and_return(double('logger').as_null_object)
    end

    context 'with blank parameters' do
      it 'returns early when user_id is blank' do
        expect(worker).not_to receive(:logger)
        worker.perform(nil, { video_frame_id => timestamp })
      end

      it 'returns early when video_frame_ids_timestamp is blank' do
        expect(worker).not_to receive(:logger)
        worker.perform(user_id, nil)
      end
    end

    context 'with valid parameters' do
      it 'increments the view count for the video frame' do
        worker.perform(user_id, { video_frame_id => timestamp })
        expect($redis.hget(Constants.video_frame_views_redis_key, video_frame_id.to_s)).to eq('1')
      end

      it 'adds the video frame to user-date queue' do
        worker.perform(user_id, { video_frame_id => timestamp })
        user_queue_key = Constants.user_date_video_frame_views_queue_redis_key(user_id)
        expect($redis.sismember(user_queue_key, video_frame_id.to_s)).to be true
      end

      it 'sets expiration for user-date queue' do
        worker.perform(user_id, { video_frame_id => timestamp })
        user_queue_key = Constants.user_date_video_frame_views_queue_redis_key(user_id)
        expect($redis.ttl(user_queue_key)).to be > 0
      end

      it 'adds the view details to the views queue' do
        worker.perform(user_id, { video_frame_id => timestamp })
        expected_entry = {
          video_frame_id: video_frame_id,
          user_id: user_id,
          viewed_at: Time.zone.at(timestamp).to_datetime.strftime("%Y-%m-%d %H:%M:%S")
        }.to_json

        expect($redis.sismember(Constants.video_frame_views_queue_redis_key, expected_entry)).to be true
      end

      it 'logs debug information' do
        expect(worker.logger).to receive(:debug).with(/QueueVideoFrameViews: #{user_id} with frame ids/)
        worker.perform(user_id, { video_frame_id => timestamp })
      end
    end

    context 'with invalid parameters' do
      it 'logs error when video_frame_id is 0' do
        expect(worker.logger).to receive(:error).with("QueueVideoFrameViews: video_frame_id or timestamp is 0")
        worker.perform(user_id, { invalid_id => timestamp })
      end

      it 'logs error when timestamp is 0' do
        expect(worker.logger).to receive(:error).with("QueueVideoFrameViews: video_frame_id or timestamp is 0")
        worker.perform(user_id, { video_frame_id => invalid_timestamp })
      end

      it 'does not process invalid entries' do
        worker.perform(user_id, { invalid_id => invalid_timestamp })
        expect($redis.hlen(Constants.video_frame_views_redis_key)).to eq(0)
        expect($redis.scard(Constants.video_frame_views_queue_redis_key)).to eq(0)
      end
    end

    context 'with multiple video frames' do
      let(:video_frame_ids_timestamp) do
        {
          100 => Time.current.to_i,
          200 => (Time.current - 1.hour).to_i,
          300 => (Time.current - 2.hours).to_i
        }
      end

      it 'processes all valid entries' do
        worker.perform(user_id, video_frame_ids_timestamp)
        expect($redis.hlen(Constants.video_frame_views_redis_key)).to eq(3)
        expect($redis.scard(Constants.video_frame_views_queue_redis_key)).to eq(3)
      end
    end

    context 'with Honeybadger context' do
      it 'sets Honeybadger context' do
        expect(Honeybadger).to receive(:context).with({
                                                        user_id: user_id,
                                                        video_frame_ids: { video_frame_id => timestamp }
                                                      })
        worker.perform(user_id, { video_frame_id => timestamp })
      end
    end
  end
end
