require 'rails_helper'

RSpec.describe Pyro::CreateTicket, type: :worker do
  let(:worker) { described_class.new }
  let(:user) { FactoryBot.create(:user, name: "Test User", phone: "1234567890") }
  let(:ticket_type) { "technical_support" }
  let(:source) { "mobile_app" }
  let(:data) do
    {
      rm_name: "John Manager",
      layout_status: "completed",
      subscription_status: "active",
      is_user_atleast_paid_once: true,
      badge: "premium"
    }
  end
  let(:current_time) { Time.zone.parse("2024-01-01 10:00:00") }

  before do
    allow(Time.zone).to receive(:now).and_return(current_time)
    allow(Constants).to receive(:get_admin_host).and_return("https://admin.example.com")
  end

  describe 'Sidekiq configuration' do
    it 'is processed in pyro queue' do
      expect(described_class.sidekiq_options_hash["queue"]).to eq(:pyro)
    end

    it 'includes Sidekiq::Worker' do
      expect(described_class.ancestors).to include(Sidekiq::Worker)
    end
  end

  describe '#perform' do
    context 'when user exists' do
      before do
        allow_any_instance_of(User).to receive(:get_subscription_status).and_return("paid")
        allow_any_instance_of(User).to receive(:photo).and_return(double(present?: true, url: "https://example.com/photo.jpg"))
      end

      it 'finds the user and processes the ticket' do
        expect(User).to receive(:find_by).with(id: user.id).and_return(user)
        expect(Pyro::SendApiRequest).to receive(:perform_async).with(kind_of(String))

        worker.perform(user.id, ticket_type, source, data)
      end

      it 'builds correct payload structure' do
        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["tenant_id"]).to eq(PyroApi::TENANT_ID)
          expect(payload["ticket_date"]).to eq(current_time.iso8601)
          expect(payload["user_id"]).to eq(user.id.to_s)
          expect(payload["name"]).to eq(user.name)
          expect(payload["phone"]).to eq("+911234567890")
          expect(payload["source"]).to eq("Mobile app")
          expect(payload["subscription_status"]).to eq("Active")
          expect(payload["atleast_paid_once"]).to eq(true)
          expect(payload["reason"]).to eq("Technical support")
          expect(payload["badge"]).to eq("Premium")
          expect(payload["poster"]).to eq("paid")
          expect(payload["layout_status"]).to eq("Completed")
          expect(payload["rm_name"]).to eq("John Manager")
          expect(payload["praja_dashboard_user_link"]).to match(/https:\/\/admin\.example\.com\/admin\/users\/\w+/)
          expect(payload["display_pic_url"]).to eq("https://example.com/photo.jpg")
        end

        worker.perform(user.id, ticket_type, source, data)
      end

      it 'handles phone number formatting for numbers starting with 91' do
        user.update!(phone: "919876543210")
        
        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["phone"]).to eq("+919876543210")
        end

        worker.perform(user.id, ticket_type, source, data)
      end

      it 'adds +91 prefix for phone numbers not starting with 91' do
        user.update!(phone: "9876543210")
        
        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["phone"]).to eq("+919876543210")
        end

        worker.perform(user.id, ticket_type, source, data)
      end

      it 'handles empty display_pic_url when photo is not present' do
        allow_any_instance_of(User).to receive(:photo).and_return(double(present?: false))

        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["display_pic_url"]).to eq("")
        end

        worker.perform(user.id, ticket_type, source, data)
      end
    end

    context 'when user does not exist' do
      it 'returns early without processing' do
        expect(User).to receive(:find_by).with(id: 999).and_return(nil)
        expect(Pyro::SendApiRequest).not_to receive(:perform_async)

        result = worker.perform(999, ticket_type, source, data)
        expect(result).to be_nil
      end
    end

    context 'with default data values' do
      let(:empty_data) { {} }

      before do
        allow_any_instance_of(User).to receive(:get_subscription_status).and_return("trial")
        allow_any_instance_of(User).to receive(:photo).and_return(double(present?: false))
      end

      it 'uses default values when data is empty' do
        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["tenant_id"]).to eq(PyroApi::TENANT_ID)
          expect(payload["ticket_date"]).to eq(current_time.iso8601)
          expect(payload["user_id"]).to eq(user.id.to_s)
          expect(payload["name"]).to eq(user.name)
          expect(payload["phone"]).to eq("+911234567890")
          expect(payload["source"]).to eq("Mobile app")
          expect(payload["subscription_status"]).to eq("")
          expect(payload["atleast_paid_once"]).to eq(false)
          expect(payload["reason"]).to eq("Technical support")
          expect(payload["badge"]).to eq("")
          expect(payload["poster"]).to eq("trial")
          expect(payload["layout_status"]).to eq("")
          expect(payload["rm_name"]).to eq("")
          expect(payload["praja_dashboard_user_link"]).to match(/https:\/\/admin\.example\.com\/admin\/users\/\w+/)
          expect(payload["display_pic_url"]).to eq("")
        end

        worker.perform(user.id, ticket_type, source, empty_data)
      end
    end

    context 'with partial data' do
      let(:partial_data) do
        {
          rm_name: "Partial Manager",
          subscription_status: "inactive"
        }
      end

      before do
        allow_any_instance_of(User).to receive(:get_subscription_status).and_return("trial")
        allow_any_instance_of(User).to receive(:photo).and_return(double(present?: false))
      end

      it 'uses provided values and defaults for missing ones' do
        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["rm_name"]).to eq("Partial Manager")
          expect(payload["subscription_status"]).to eq("Inactive")
          expect(payload["layout_status"]).to eq("")
          expect(payload["badge"]).to eq("")
          expect(payload["atleast_paid_once"]).to eq(false)
        end

        worker.perform(user.id, ticket_type, source, partial_data)
      end
    end

    context 'string humanization' do
      before do
        allow_any_instance_of(User).to receive(:get_subscription_status).and_return("paid")
        allow_any_instance_of(User).to receive(:photo).and_return(double(present?: false))
      end

      it 'humanizes ticket_type and source' do
        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["reason"]).to eq("Technical support")
          expect(payload["source"]).to eq("Mobile app")
        end

        worker.perform(user.id, "technical_support", "mobile_app", {})
      end

      it 'humanizes data fields' do
        test_data = {
          subscription_status: "premium_active",
          layout_status: "layout_completed",
          badge: "verified_user"
        }

        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["subscription_status"]).to eq("Premium active")
          expect(payload["layout_status"]).to eq("Layout completed")
          expect(payload["badge"]).to eq("Verified user")
        end

        worker.perform(user.id, ticket_type, source, test_data)
      end
    end

    context 'integration with Constants' do
      it 'uses correct admin host for dashboard link' do
        expect(Constants).to receive(:get_admin_host).and_return("https://custom-admin.example.com")
        allow_any_instance_of(User).to receive(:get_subscription_status).and_return("paid")
        allow_any_instance_of(User).to receive(:photo).and_return(double(present?: false))

        expect(Pyro::SendApiRequest).to receive(:perform_async) do |payload_json|
          payload = JSON.parse(payload_json)
          expect(payload["praja_dashboard_user_link"]).to match(/https:\/\/custom-admin\.example\.com\/admin\/users\/\w+/)
        end

        worker.perform(user.id, ticket_type, source, data)
      end
    end
  end

  describe 'integration with Sidekiq' do
    it 'can be enqueued' do
      expect {
        described_class.perform_async(user.id, ticket_type, source, data)
      }.to change(described_class.jobs, :size).by(1)
    end

    it 'stores correct arguments in job' do
      described_class.perform_async(user.id, ticket_type, source, data)
      expect(described_class.jobs.last['args']).to eq([user.id, ticket_type, source, data.stringify_keys])
    end

    it 'uses correct queue' do
      described_class.perform_async(user.id, ticket_type, source, data)
      expect(described_class.jobs.last['queue']).to eq('pyro')
    end
  end
end
