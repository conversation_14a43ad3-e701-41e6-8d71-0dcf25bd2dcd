require 'rails_helper'

RSpec.describe Pyro::SendApiRequest, type: :worker do
  let(:worker) { described_class.new }
  let(:valid_payload) do
    {
      tenant_id: "d6db1158-2212-4d94-bb01-2c28b971d9a9",
      ticket_date: "2024-01-01T10:00:00Z",
      user_id: "123",
      name: "Test User",
      phone: "+911234567890",
      source: "App",
      subscription_status: "Active",
      atleast_paid_once: true,
      reason: "Support",
      badge: "Premium",
      poster: "paid",
      layout_status: "Completed",
      rm_name: "<PERSON>",
      praja_dashboard_user_link: "https://admin.example.com/users/abc123",
      display_pic_url: "https://example.com/photo.jpg"
    }
  end
  let(:payload_json) { valid_payload.to_json }

  describe 'Sidekiq configuration' do
    it 'is processed in pyro queue' do
      expect(described_class.sidekiq_options_hash["queue"]).to eq(:pyro)
    end

    it 'has retry count of 1' do
      expect(described_class.sidekiq_options_hash["retry"]).to eq(1)
    end

    it 'includes Sidekiq::Worker' do
      expect(described_class.ancestors).to include(Sidekiq::Worker)
    end

    it 'includes Sidekiq::Throttled::Worker' do
      expect(described_class.ancestors).to include(Sidekiq::Throttled::Worker)
    end
  end

  describe 'throttling configuration' do
    it 'has throttling enabled' do
      # Check that the class includes throttling functionality
      expect(described_class.ancestors).to include(Sidekiq::Throttled::Worker)
    end
  end

  describe '#perform' do
    let(:start_time) { Time.zone.parse("2024-01-01 10:00:00") }

    before do
      allow(Time.zone).to receive(:now).and_return(start_time, start_time + 2.seconds)
    end

    context 'when API request is successful' do
      before do
        # Mock with string keys since JSON.parse returns string keys
        allow(PyroApi).to receive(:send_api_request).and_return({ "status" => "success" })
      end

      it 'parses JSON payload and calls PyroApi' do
        expect(JSON).to receive(:parse).with(payload_json).and_call_original
        expect(PyroApi).to receive(:send_api_request).with(hash_including("user_id" => "123"))

        worker.perform(payload_json)
      end

      it 'logs timing for successful requests' do
        expect(Rails.logger).to receive(:warn).with("Time taken for Pyro send_api_request: 2.0 seconds")

        worker.perform(payload_json)
      end

      it 'does not raise any errors' do
        expect {
          worker.perform(payload_json)
        }.not_to raise_error
      end
    end

    context 'when API request fails' do
      let(:api_error) { StandardError.new("API request failed") }

      before do
        allow(PyroApi).to receive(:send_api_request).and_raise(api_error)
      end

      it 'logs timing before re-raising the error' do
        expect(Rails.logger).to receive(:warn).with("Time taken for Pyro send_api_request: 2.0 seconds")

        expect {
          worker.perform(payload_json)
        }.to raise_error(api_error)
      end

      it 're-raises the original error' do
        allow(Rails.logger).to receive(:warn)

        expect {
          worker.perform(payload_json)
        }.to raise_error(StandardError, "API request failed")
      end
    end

    context 'when JSON parsing fails' do
      let(:invalid_json) { "invalid json" }

      it 'raises JSON::ParserError' do
        expect {
          worker.perform(invalid_json)
        }.to raise_error(JSON::ParserError)
      end
    end

    context 'with different error types' do
      before do
        allow(JSON).to receive(:parse).with(payload_json).and_return(valid_payload)
      end

      it 'handles network errors' do
        network_error = SocketError.new("Network unreachable")
        allow(PyroApi).to receive(:send_api_request).and_raise(network_error)
        allow(Rails.logger).to receive(:warn)

        expect {
          worker.perform(payload_json)
        }.to raise_error(SocketError, "Network unreachable")
      end

      it 'handles timeout errors' do
        timeout_error = Timeout::Error.new("Request timeout")
        allow(PyroApi).to receive(:send_api_request).and_raise(timeout_error)
        allow(Rails.logger).to receive(:warn)

        expect {
          worker.perform(payload_json)
        }.to raise_error(Timeout::Error, "Request timeout")
      end
    end

    context 'timing calculations' do
      it 'calculates elapsed time correctly' do
        end_time = start_time + 5.5.seconds
        allow(Time.zone).to receive(:now).and_return(start_time, end_time)
        allow(PyroApi).to receive(:send_api_request).and_return({ "status" => "success" })

        expect(Rails.logger).to receive(:warn).with("Time taken for Pyro send_api_request: 5.5 seconds")

        worker.perform(payload_json)
      end

      it 'handles zero elapsed time' do
        allow(Time.zone).to receive(:now).and_return(start_time, start_time)
        allow(PyroApi).to receive(:send_api_request).and_return({ "status" => "success" })

        expect(Rails.logger).to receive(:warn).with("Time taken for Pyro send_api_request: 0.0 seconds")

        worker.perform(payload_json)
      end
    end
  end

  describe 'integration with Sidekiq' do
    it 'can be enqueued' do
      expect {
        described_class.perform_async(payload_json)
      }.to change(described_class.jobs, :size).by(1)
    end

    it 'stores correct arguments in job' do
      described_class.perform_async(payload_json)
      expect(described_class.jobs.last['args']).to eq([payload_json])
    end

    it 'uses correct queue' do
      described_class.perform_async(payload_json)
      expect(described_class.jobs.last['queue']).to eq('pyro')
    end
  end
end
