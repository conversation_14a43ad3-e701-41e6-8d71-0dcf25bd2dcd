require 'rails_helper'

RSpec.describe Transliteration::Text do
  describe 'Should be able to transliterate text' do
    let(:aws_translate_client) { instance_double(Aws::Translate::Client) }
    
    before do
      # Mock AWS credentials
      allow(Rails.application.credentials).to receive(:aws_access_key_id).and_return('test_access_key_id')
      allow(Rails.application.credentials).to receive(:aws_secret_access_key).and_return('test_secret_access_key')
      
      # Mock AWS Translate client
      allow(Aws::Translate::Client).to receive(:new).and_return(aws_translate_client)
    end

    it 'should be able to transliterate text from telugu to english' do
      # Mock the translate_text response
      translate_response = double('translate_response', translated_text: 'telugu')
      allow(aws_translate_client).to receive(:translate_text).and_return(translate_response)

      text = Transliteration::Text.new('తెలుగు')
      expect(text.transliterate('en')).to eq('telugu')
    end
    
    it 'should be able to transliterate text from english to telugu' do
      # Mock the translate_text response
      translate_response = double('translate_response', translated_text: 'ఇంగ్లీష్')
      allow(aws_translate_client).to receive(:translate_text).and_return(translate_response)

      text = Transliteration::Text.new('english')
      expect(text.transliterate('te')).to eq('ఇంగ్లీష్')
    end
  end
end
