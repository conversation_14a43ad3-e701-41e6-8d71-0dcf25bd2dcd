require 'rails_helper'

RSpec.describe Transliteration::Word do
  describe 'Should be able to detect languages' do
    it 'should be able to detect telugu text' do
      text = Transliteration::Word.new('తెలుగు')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_TELUGU)
    end

    it 'should be able to detect english text' do
      text = Transliteration::Word.new('english')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_ENGLISH)
    end

    it 'should be able to detect hindi text' do
      text = Transliteration::Word.new('हिन्दी')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_HINDI)
    end

    it 'should be able to detect tamil text' do
      text = Transliteration::Word.new('தமிழ்')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_TAMIL)
    end

    it 'should be able to detect kannada text' do
      text = Transliteration::Word.new('ಕನ್ನಡ')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_KANNADA)
    end

    it 'should be able to detect malayalam text' do
      text = Transliteration::Word.new('മലയാളം')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_MALAYALAM)
    end

    it 'should be able to detect punjabi text' do
      text = Transliteration::Word.new('ਪੰਜਾਬੀ')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_PUNJABI)
    end

    it 'should be able to detect oriya text' do
      text = Transliteration::Word.new('ଓଡ଼ିଆ')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_ORIYA)
    end

    it 'should be able to detect bengali text' do
      text = Transliteration::Word.new('বাংলা')
      expect(text.language).to eq(Transliteration::LANGUAGE_CODE_BENGALI)
    end
  end

  describe 'Should be able to transliterate text' do
    let(:aws_translate_client) { instance_double(Aws::Translate::Client) }
    
    before do
      # Mock AWS credentials
      allow(Rails.application.credentials).to receive(:aws_access_key_id).and_return('test_access_key_id')
      allow(Rails.application.credentials).to receive(:aws_secret_access_key).and_return('test_secret_access_key')
      
      # Mock AWS Translate client
      allow(Aws::Translate::Client).to receive(:new).and_return(aws_translate_client)
    end

    it 'should be able to transliterate text from telugu to english' do
      # Mock the translate_text response
      translate_response = double('translate_response', translated_text: 'telugu')
      allow(aws_translate_client).to receive(:translate_text).and_return(translate_response)

      text = Transliteration::Word.new('తెలుగు')
      expect(text.transliterate('en')).to eq('telugu')
    end
    
    it 'should be able to transliterate text from english to telugu' do
      # Mock the translate_text response
      translate_response = double('translate_response', translated_text: 'ఇంగ్లిష్')
      allow(aws_translate_client).to receive(:translate_text).and_return(translate_response)

      text = Transliteration::Word.new('english')
      expect(text.transliterate('te')).to eq('ఇంగ్లిష్')
    end
    
    it 'should be able not able to detect hebrew' do
      text = Transliteration::Word.new('מִבְחָן')
      expect(text.language).to eq(nil)
      expect(text.transliterate('en')).to eq('מִבְחָן')
    end
    
    it 'should be able return same text if source language and target language is same' do
      text = Transliteration::Word.new('english')
      expect(text.transliterate('en')).to eq('english')
    end
  end

  describe 'Should be able to handle failure cases' do
    let(:aws_translate_client) { instance_double(Aws::Translate::Client) }
    
    before do
      # Mock AWS credentials
      allow(Rails.application.credentials).to receive(:aws_access_key_id).and_return('test_access_key_id')
      allow(Rails.application.credentials).to receive(:aws_secret_access_key).and_return('test_secret_access_key')
      
      # Mock AWS Translate client
      allow(Aws::Translate::Client).to receive(:new).and_return(aws_translate_client)
    end

    it 'should be able to return same text if the azure api fails' do
      # Mock AWS Translate client to raise an error
      allow(aws_translate_client).to receive(:translate_text).and_raise(Aws::Translate::Errors::ServiceError.new(nil, 'Service error'))
      
      text = Transliteration::Word.new('english')
      expect(text.transliterate('te')).to eq('english')
    end
    
    it 'should be able to return same text if the azure api fails with different response' do
      # Mock AWS Translate client to raise an error
      allow(aws_translate_client).to receive(:translate_text).and_raise(Aws::Translate::Errors::ServiceError.new(nil, 'Different service error'))
      
      text = Transliteration::Word.new('english')
      expect(text.transliterate('te')).to eq('english')
    end
  end
end
