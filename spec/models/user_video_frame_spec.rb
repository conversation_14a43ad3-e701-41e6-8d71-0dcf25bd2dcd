require 'rails_helper'

RSpec.describe UserVideoFrame, type: :model do
  let(:user) { create(:user) }
  let(:video_frame) { create(:video_frame, video_type: 'PORTRAIT') }
  let(:video_frame1) { create(:video_frame, video_type: 'LANDSCAPE') }
  let(:user_video_frame) do
    create(:user_video_frame,
           user: user,
           video_frame: video_frame,
           identity_photo_url: 'https://cdn.test/identity.png'
    )
  end

  describe 'scopes' do
    let!(:active_frame) { create(:user_video_frame, active: true) }
    let!(:inactive_frame) { create(:user_video_frame, active: false) }

    it 'returns only active frames' do
      expect(UserVideoFrame.active).to include(active_frame)
      expect(UserVideoFrame.active).not_to include(inactive_frame)
    end
  end

  describe '#get_video_mode' do
    it 'returns the video type from video_frame' do
      expect(user_video_frame.get_video_mode).to eq('PORTRAIT')
    end
  end

  describe '#get_frame_dimensions' do
    it 'returns dimensions for PORTRAIT mode' do
      expect(user_video_frame.get_frame_dimensions('PORTRAIT')).to eq({ width: 360, height: 550 })
    end

    it 'raises error for unknown mode' do
      expect {
        user_video_frame.get_frame_dimensions('UNKNOWN')
      }.to raise_error(ArgumentError)
    end
  end

  describe '#get_video_creative_elements' do
    let(:source_video) do
      double('Video',
             url: 'https://cdn.video.com/processed.mp4',
             source_url: 'https://cdn.video.com/raw.mp4'
      )
    end

    let(:gradient_circle_id) { 31398 }

    before do
      allow(user).to receive_message_chain(:video_poster_default_photo, :url)
                       .and_return('https://cdn.user.com/photo.jpg')

      allow(UserPosterLayout).to receive(:where).and_return([
                                                              double(video_frame_protocol_photo_url: 'https://cdn.test.com/protocol.png')
                                                            ])
    end

    context 'when mode is PORTRAIT' do
      it 'returns all expected creative elements in order' do
        elements = user_video_frame.get_video_creative_elements(
          source_video: source_video,
          gradient_circle_id: gradient_circle_id
        )

        expect(elements).to be_an(Array)
        expect(elements.size).to eq(4)

        expect(elements[0][:type]).to eq('video')
        expect(elements[1][:url]).to eq('https://cdn.test.com/protocol.png')
        expect(elements[2][:url]).to eq('https://cdn.user.com/photo.jpg')
        expect(elements[3][:url]).to eq(user_video_frame.identity_photo_url)
      end

      context 'when mode is PORTRAIT and the video frame protocol photo url is nil' do
        it 'returns all expected creative elements in order without sending protocol data' do
          allow(UserPosterLayout).to receive(:where).and_return([
                                                                  double(video_frame_protocol_photo_url: nil)
                                                                ])

          elements = user_video_frame.get_video_creative_elements(
            source_video: source_video,
            gradient_circle_id: gradient_circle_id
          )

          expect(elements.size).to eq(3)
          expect(elements[0][:type]).to eq('video')
          expect(elements[1][:url]).to eq('https://cdn.user.com/photo.jpg')
          expect(elements[2][:url]).to eq(user_video_frame.identity_photo_url)
        end
      end

      it 'uses the processed video URL by default' do
        elements = user_video_frame.get_video_creative_elements(
          source_video: source_video,
          gradient_circle_id: gradient_circle_id
        )

        video_element = elements.find { |e| e[:type] == 'video' }
        expect(video_element[:url]).to eq(source_video.url)
      end
    end
  end


  describe 'private methods' do
    describe '#calculate_video_position' do
      it 'calculates correct position for PORTRAIT' do
        result = user_video_frame.send(:calculate_video_position, 'PORTRAIT', { width: 330, height: 550 }, { width: 360, height: 550 })
        expect(result).to eq([0, 0])
      end

      it 'raises error for unknown mode' do
        expect {
          user_video_frame.send(:calculate_video_position, 'INVALID', {}, {})
        }.to raise_error(ArgumentError)
      end
    end

    describe '#calculate_user_photo_position' do
      it 'returns expected values for LANDSCAPE' do
        result = user_video_frame.send(:calculate_user_photo_position, 'LANDSCAPE', { width: 116, height: 130 }, { width: 360, height: 358 })
        expect(result).to eq([0, 228])
      end
    end
  end
end
