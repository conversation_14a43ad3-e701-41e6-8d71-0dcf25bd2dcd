RSpec.shared_examples 'PhoneNormalizer' do
  let(:controller_instance) { described_class.new }

  before do
    allow(controller_instance).to receive(:current_admin_user).and_return(nil)
    # Mock Redis to return false by default (no test users)
    allow($redis).to receive(:sismember).with(Constants.test_agent_partners_redis_key, anything).and_return(false)
  end

  context 'when current admin user ID is 1' do
    let(:admin_user_1) { double('AdminUser', id: 1) }
    
    before do
      allow(controller_instance).to receive(:current_admin_user).and_return(admin_user_1)
      # Mock Redis to return true for admin user ID 1 (making it a test user)
      allow($redis).to receive(:sismember).with(Constants.test_agent_partners_redis_key, '1').and_return(true)
    end
    
    it 'bypasses normalization and returns phone as-is' do
      expect(controller_instance.send(:normalize_phone_number, 'invalid_phone')).to eq('invalid_phone')
      expect(controller_instance.send(:normalize_phone_number, '1234567890')).to eq('1234567890')
    end
  end

  describe '#normalize_phone_number' do
    it 'returns nil for blank input' do
      expect(controller_instance.send(:normalize_phone_number, '')).to be_nil
      expect(controller_instance.send(:normalize_phone_number, nil)).to be_nil
    end

    it 'normalizes 10-digit phone number' do
      expect(controller_instance.send(:normalize_phone_number, '9876543210')).to eq(9876543210)
    end

    it 'removes +91 country code' do
      expect(controller_instance.send(:normalize_phone_number, '+919876543210')).to eq(9876543210)
    end

    it 'removes 91 country code' do
      expect(controller_instance.send(:normalize_phone_number, '919876543210')).to eq(9876543210)
    end

    it 'handles special characters and spaces' do
      expect(controller_instance.send(:normalize_phone_number, '+91 ************')).to eq(9876543210)
      expect(controller_instance.send(:normalize_phone_number, '************')).to eq(9876543210)
      expect(controller_instance.send(:normalize_phone_number, '(*************')).to eq(9876543210)
    end

    it 'returns nil for invalid phone numbers' do
      expect(controller_instance.send(:normalize_phone_number, '1234567890')).to be_nil # starts with 1
      expect(controller_instance.send(:normalize_phone_number, '12345')).to be_nil # too short
      expect(controller_instance.send(:normalize_phone_number, 'abcdefghij')).to be_nil # non-numeric
      expect(controller_instance.send(:normalize_phone_number, 'invalid_phone')).to be_nil # invalid format
    end

    it 'accepts valid mobile numbers starting with 6, 7, 8, 9' do
      expect(controller_instance.send(:normalize_phone_number, '6876543210')).to eq(6876543210)
      expect(controller_instance.send(:normalize_phone_number, '7876543210')).to eq(7876543210)
      expect(controller_instance.send(:normalize_phone_number, '8876543210')).to eq(8876543210)
      expect(controller_instance.send(:normalize_phone_number, '9876543210')).to eq(9876543210)
    end

    it 'returns nil for TelephoneNumber parsing errors' do
      # This tests the rescue block for StandardError
      allow(TelephoneNumber).to receive(:parse).and_raise(StandardError.new('Parse error'))
      expect(controller_instance.send(:normalize_phone_number, '9876543210')).to be_nil
    end
  end
end
