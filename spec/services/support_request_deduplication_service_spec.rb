require 'rails_helper'

RSpec.describe SupportRequestDeduplicationService, type: :service do
  let(:user_id) { 12345 }
  let(:redis_key) { "support_request_cooldown:#{user_id}" }

  before do
    # Clear any existing Redis keys for clean test state
    $redis.del(redis_key)
  end

  after do
    # Cleanup after each test
    $redis.del(redis_key)
  end

  describe '.can_user_submit_request?' do
    context 'when user has never submitted a support request' do
      it 'returns true' do
        expect(SupportRequestDeduplicationService.can_user_submit_request?(user_id)).to be true
      end
    end

    context 'when user has submitted a request and is in cooldown period' do
      before do
        SupportRequestDeduplicationService.record_request(user_id, 'test_reason', 'test_source')
      end

      it 'returns false' do
        expect(SupportRequestDeduplicationService.can_user_submit_request?(user_id)).to be false
      end
    end

    context 'when Redis key has expired' do
      before do
        # Set a key that has already expired
        $redis.set(redis_key, 'test_data')
        $redis.expire(redis_key, -1)  # Expire immediately
      end

      it 'returns true' do
        expect(SupportRequestDeduplicationService.can_user_submit_request?(user_id)).to be true
      end
    end
  end

  describe '.record_request' do
    context 'when user has no existing request' do
      it 'creates a new Redis key with expiration' do
        result = SupportRequestDeduplicationService.record_request(user_id, 'badge_change', 'support_sheet')
        
        expect(result).to be true
        expect($redis.exists(redis_key)).to eq(1)
        
        # Check TTL is approximately 90 minutes (5400 seconds)
        ttl = $redis.ttl(redis_key)
        expect(ttl).to be_between(5390, 5400)
        
        # Check stored data
        data = JSON.parse($redis.get(redis_key), symbolize_names: true)
        expect(data[:user_id]).to eq(user_id)
        expect(data[:reason]).to eq('badge_change')
        expect(data[:source]).to eq('support_sheet')
        expect(data[:timestamp]).to be_present
      end
    end

    context 'when user already has an active request' do
      before do
        SupportRequestDeduplicationService.record_request(user_id, 'first_reason', 'first_source')
      end

      it 'does not update the existing key or extend expiry' do
        original_ttl = $redis.ttl(redis_key)
        original_data = $redis.get(redis_key)
        
        # Try to record another request
        result = SupportRequestDeduplicationService.record_request(user_id, 'second_reason', 'second_source')
        
        expect(result).to be false
        
        # TTL should not be extended
        new_ttl = $redis.ttl(redis_key)
        expect(new_ttl).to be <= original_ttl
        
        # Data should remain unchanged
        expect($redis.get(redis_key)).to eq(original_data)
      end
    end

    it 'uses the hardcoded cooldown period' do
      SupportRequestDeduplicationService.record_request(user_id, 'test_reason', 'test_source')
      ttl = $redis.ttl(redis_key)
      expect(ttl).to be_between(5390, 5400)
    end
  end

  describe '.get_request_info' do
    context 'when user has no active request' do
      it 'returns nil' do
        expect(SupportRequestDeduplicationService.get_request_info(user_id)).to be_nil
      end
    end

    context 'when user has an active request' do
      before do
        SupportRequestDeduplicationService.record_request(user_id, 'feature_request', 'support_sheet')
      end

      it 'returns the request information' do
        info = SupportRequestDeduplicationService.get_request_info(user_id)
        
        expect(info).to be_a(Hash)
        expect(info[:user_id]).to eq(user_id)
        expect(info[:reason]).to eq('feature_request')
        expect(info[:source]).to eq('support_sheet')
        expect(info[:timestamp]).to be_present
      end
    end

    context 'when Redis contains invalid JSON' do
      before do
        $redis.set(redis_key, 'invalid_json')
      end

      it 'returns nil' do
        expect(SupportRequestDeduplicationService.get_request_info(user_id)).to be_nil
      end
    end
  end

  describe 'Redis key pattern' do
    it 'uses consistent key naming' do
      key = SupportRequestDeduplicationService.send(:support_request_key, user_id)
      expect(key).to eq("support_request_cooldown:#{user_id}")
    end
  end
end
