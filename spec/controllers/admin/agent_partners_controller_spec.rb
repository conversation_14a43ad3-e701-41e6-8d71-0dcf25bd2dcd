require 'rails_helper'
require 'shared_examples/phone_normalizer_shared_examples'

RSpec.describe Admin::AgentPartnersController, type: :controller do
  let(:agent_partner_admin) { create(:admin_user, role: :agent_partner) }
  let(:non_agent_partner_admin) { create(:admin_user, role: :admin) }
  let(:existing_user) { create(:user, phone: 9876543210, status: :active) }
  let(:existing_rm_admin) { create(:admin_user, role: :relationship_manager, floww_user_id: 12345) }

  describe 'POST #start_lead_process' do
    context 'when admin user is not an agent partner' do
      before do
        allow(controller).to receive(:authenticate_admin_user!).and_return(true)
        allow(controller).to receive(:current_admin_user).and_return(non_agent_partner_admin)
      end

      it 'returns forbidden status' do
        post :start_lead_process, params: { phone: '9876543210' }
        expect(response).to have_http_status(:forbidden)
        expect(JSON.parse(response.body)['error']).to eq('Forbidden')
      end
    end

    context 'when admin user is an agent partner' do
      before do
        allow(controller).to receive(:authenticate_admin_user!).and_return(true)
        allow(controller).to receive(:current_admin_user).and_return(agent_partner_admin)
      end

      context 'with missing phone number' do
        it 'returns bad request status' do
          post :start_lead_process, params: {}
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)['error']).to eq('Phone number is required')
        end
      end

      context 'with invalid phone number format' do
        it 'returns bad request status for non-numeric phone' do
          post :start_lead_process, params: { phone: 'invalid_phone' }
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)['error']).to eq('Invalid phone number format')
        end

        it 'returns bad request status for short phone number' do
          post :start_lead_process, params: { phone: '12345' }
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)['error']).to eq('Invalid phone number format')
        end

        it 'returns bad request status for phone starting with invalid digit' do
          post :start_lead_process, params: { phone: '1234567890' }
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)['error']).to eq('Invalid phone number format')
        end
      end

      context 'with valid phone number formats' do
        it 'accepts 10-digit phone number starting with 6-9' do
          post :start_lead_process, params: { phone: '9876543210' }
          expect(response).to have_http_status(:ok)
        end

        it 'accepts phone number with +91 country code' do
          post :start_lead_process, params: { phone: '+919876543210' }
          expect(response).to have_http_status(:ok)
        end

        it 'accepts phone number with 91 country code' do
          post :start_lead_process, params: { phone: '919876543210' }
          expect(response).to have_http_status(:ok)
        end

        it 'accepts phone number with spaces and special characters' do
          post :start_lead_process, params: { phone: '+91 ************' }
          expect(response).to have_http_status(:ok)
        end
      end

      context 'when user does not exist' do
        let(:new_phone) { 8765432109 }

        it 'creates a new user with pre_signup status' do
          expect {
            post :start_lead_process, params: { phone: new_phone.to_s }
          }.to change(User, :count).by(1)

          new_user = User.find_by(phone: new_phone)
          expect(new_user).to be_present
          expect(new_user.status).to eq('pre_signup')
        end

        it 'creates a new PremiumPitch with interested status and OABLT_APOutbound lead type' do
          expect {
            post :start_lead_process, params: { phone: new_phone.to_s }
          }.to change(PremiumPitch, :count).by(1)

          new_user = User.find_by(phone: new_phone)
          premium_pitch = PremiumPitch.find_by(user: new_user)
          expect(premium_pitch).to be_present
          expect(premium_pitch.status).to eq('interested')
          expect(premium_pitch.lead_type).to eq('OABLT_APOutbound')
        end

        it 'assigns current admin user as RM' do
          post :start_lead_process, params: { phone: new_phone.to_s }

          new_user = User.find_by(phone: new_phone)
          expect(new_user.get_rm_user).to eq(agent_partner_admin)
        end

        it 'returns success response with user_id and premium_pitch_status' do
          post :start_lead_process, params: { phone: new_phone.to_s }

          expect(response).to have_http_status(:ok)
          response_body = JSON.parse(response.body)
          expect(response_body['success']).to be true
          expect(response_body['user_id']).to be_present
          expect(response_body['premium_pitch_status']).to eq('interested')
        end
      end

      context 'when user exists without PremiumPitch' do
        it 'creates a new PremiumPitch for existing user' do
          expect {
            post :start_lead_process, params: { phone: existing_user.phone.to_s }
          }.to change(PremiumPitch, :count).by(1)

          premium_pitch = PremiumPitch.find_by(user: existing_user)
          expect(premium_pitch).to be_present
          expect(premium_pitch.status).to eq('interested')
          expect(premium_pitch.lead_type).to eq('OABLT_APOutbound')
        end

        it 'assigns current admin user as RM' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          existing_user.reload
          expect(existing_user.get_rm_user).to eq(agent_partner_admin)
        end
      end

      context 'when user exists with PremiumPitch but no RM assigned' do
        let!(:existing_premium_pitch) { create(:premium_pitch, user: existing_user, status: :wait_list, lead_type: :other_type) }

        it 'does not create a new PremiumPitch' do
          expect {
            post :start_lead_process, params: { phone: existing_user.phone.to_s }
          }.not_to change(PremiumPitch, :count)
        end

        it 'updates the existing PremiumPitch status and lead_type' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          existing_premium_pitch.reload
          expect(existing_premium_pitch.status).to eq('interested')
          expect(existing_premium_pitch.lead_type).to eq('OABLT_APOutbound')
        end

        it 'assigns current admin user as RM' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          existing_user.reload
          expect(existing_user.get_rm_user).to eq(agent_partner_admin)
        end

        it 'returns success response with updated premium_pitch_status' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          expect(response).to have_http_status(:ok)
          response_body = JSON.parse(response.body)
          expect(response_body['success']).to be true
          expect(response_body['user_id']).to eq(existing_user.id)
          expect(response_body['premium_pitch_status']).to eq('interested')
        end
      end

      context 'when user exists with PremiumPitch and same RM assigned' do
        let!(:existing_premium_pitch) { create(:premium_pitch, user: existing_user, status: :interested) }

        before do
          existing_user.save_rm_user(rm_user_id: agent_partner_admin.id)
        end

        it 'returns success response without changes' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          expect(response).to have_http_status(:ok)
          response_body = JSON.parse(response.body)
          expect(response_body['success']).to be true
          expect(response_body['user_id']).to eq(existing_user.id)
          expect(response_body['premium_pitch_status']).to eq('interested')
        end
      end

      context 'when user exists with PremiumPitch and different RM assigned' do
        let!(:existing_premium_pitch) { create(:premium_pitch, user: existing_user, status: :interested) }

        before do
          existing_user.save_rm_user(rm_user_id: existing_rm_admin.id)
        end

        it 'returns conflict status' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          expect(response).to have_http_status(:conflict)
          response_body = JSON.parse(response.body)
          expect(response_body['error']).to eq('Lead process already started by another RM')
        end

        it 'does not change the existing RM assignment' do
          post :start_lead_process, params: { phone: existing_user.phone.to_s }

          existing_user.reload
          expect(existing_user.get_rm_user).to eq(existing_rm_admin)
        end
      end

      context 'when database error occurs' do
        before do
          allow(User).to receive(:find_by).and_raise(ActiveRecord::RecordInvalid.new(User.new))
        end

        it 'returns unprocessable entity status' do
          post :start_lead_process, params: { phone: '9876543210' }

          expect(response).to have_http_status(:unprocessable_entity)
          response_body = JSON.parse(response.body)
          expect(response_body['error']).to be_present
        end
      end

      context 'when unexpected error occurs' do
        before do
          allow(User).to receive(:find_by).and_raise(StandardError.new('Unexpected error'))
        end

        it 'returns internal server error status' do
          post :start_lead_process, params: { phone: '9876543210' }

          expect(response).to have_http_status(:internal_server_error)
          response_body = JSON.parse(response.body)
          expect(response_body['error']).to eq('An error occurred while starting the lead process')
        end

        it 'logs the error' do
          expect(Rails.logger).to receive(:error).with('Error starting lead process: Unexpected error')
          post :start_lead_process, params: { phone: '9876543210' }
        end
      end
    end
  end

  include_examples 'PhoneNormalizer'
end
