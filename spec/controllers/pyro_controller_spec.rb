# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PyroC<PERSON>roller, type: :controller do
  describe "POST #send_to_mixpanel" do
    before :each do
      @user = FactoryBot.create(:user)
      @token = Base64.encode64("#{Rails.application.credentials[:pyro][:auth_username]}:#{Rails.application.credentials[:pyro][:auth_secret]}")
      allow(EventTracker).to receive(:perform_async)
    end

    it "should enqueue EventTracker job with valid params" do
      request.headers['Authorization'] = "Bearer #{@token}"
      
      post :send_to_mixpanel, params: { 
        user_id: @user.id, 
        event_name: 'test_event', 
        properties: { key: 'value' } 
      }
      
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['success']).to be_truthy
      expect(EventTracker).to have_received(:perform_async).with(@user.id, 'test_event', { 'key' => 'value' })
    end

    it "should return bad request when event_name is missing" do
      request.headers['Authorization'] = "Bearer #{@token}"
      
      post :send_to_mixpanel, params: { 
        user_id: @user.id, 
        properties: { key: 'value' } 
      }
      
      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)['success']).to be_falsey
      expect(JSON.parse(response.body)['message']).to eq('Event name not present')
    end

    it "should return bad request when user_id is missing" do
      request.headers['Authorization'] = "Bearer #{@token}"
      
      post :send_to_mixpanel, params: { 
        event_name: 'test_event', 
        properties: { key: 'value' } 
      }
      
      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)['success']).to be_falsey
      expect(JSON.parse(response.body)['message']).to eq('User ID not present')
    end

    it "should return not found when user doesn't exist" do
      request.headers['Authorization'] = "Bearer #{@token}"
      
      post :send_to_mixpanel, params: { 
        user_id: 999999, 
        event_name: 'test_event', 
        properties: { key: 'value' } 
      }
      
      expect(response).to have_http_status(:not_found)
      expect(JSON.parse(response.body)['success']).to be_falsey
      expect(JSON.parse(response.body)['message']).to eq('User not found')
    end

    it "should return unauthorized without proper authentication" do
      request.headers['Authorization'] = "Bearer #{Base64.encode64("invalid:credentials")}"
      
      post :send_to_mixpanel, params: { 
        user_id: @user.id, 
        event_name: 'test_event', 
        properties: { key: 'value' } 
      }
      
      expect(response).to have_http_status(:unauthorized)
      expect(JSON.parse(response.body)['message']).to eq('Unauthorized')
    end

    it "should pass properties directly to EventTracker" do
      request.headers['Authorization'] = "Bearer #{@token}"
      
      post :send_to_mixpanel, params: { 
        user_id: @user.id, 
        event_name: 'test_event', 
        properties: { 
          key1: 'value1', 
          key2: 'value2' 
        } 
      }
      
      expect(response).to have_http_status(:ok)
      expect(EventTracker).to have_received(:perform_async).with(@user.id, 'test_event', { 'key1' => 'value1', 'key2' => 'value2' })
    end

    it "should handle missing properties gracefully" do
      request.headers['Authorization'] = "Bearer #{@token}"
      
      post :send_to_mixpanel, params: { 
        user_id: @user.id, 
        event_name: 'test_event'
      }
      
      expect(response).to have_http_status(:ok)
      expect(EventTracker).to have_received(:perform_async).with(@user.id, 'test_event', {})
    end
  end
end