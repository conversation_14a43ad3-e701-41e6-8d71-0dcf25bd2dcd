require 'rails_helper'
require 'webmock/rspec'

RSpec.describe PyroApi, type: :model do
  let(:valid_payload) do
    {
      tenant_id: "d6db1158-2212-4d94-bb01-2c28b971d9a9",
      ticket_date: "2024-01-01T10:00:00Z",
      user_id: "123",
      name: "Test User",
      phone: "+911234567890",
      source: "App",
      subscription_status: "Active",
      atleast_paid_once: true,
      reason: "Support",
      badge: "Premium",
      poster: "paid",
      layout_status: "Completed",
      rm_name: "<PERSON>",
      praja_dashboard_user_link: "https://admin.example.com/users/abc123",
      display_pic_url: "https://example.com/photo.jpg"
    }
  end

  let(:api_url) { "#{PyroApi::BASE_URL}#{PyroApi::WEBHOOK_ENDPOINT}" }
  let(:bearer_token) { "test_bearer_token" }
  let(:webhook_secret) { "test_webhook_secret" }

  before do
    allow(Rails.application.credentials).to receive(:[]).with(:pyro_bearer_token).and_return(bearer_token)
    allow(Rails.application.credentials).to receive(:[]).with(:pyro_webhook_secret).and_return(webhook_secret)
  end

  describe '.send_api_request' do
    context 'when credentials are missing' do
      it 'raises error when bearer token is missing' do
        allow(Rails.application.credentials).to receive(:[]).with(:pyro_bearer_token).and_return(nil)
        
        expect {
          PyroApi.send_api_request(valid_payload)
        }.to raise_error("Pyro bearer token is missing from Rails credentials")
      end

      it 'raises error when webhook secret is missing' do
        allow(Rails.application.credentials).to receive(:[]).with(:pyro_webhook_secret).and_return("")
        
        expect {
          PyroApi.send_api_request(valid_payload)
        }.to raise_error("Pyro webhook secret is missing from Rails credentials")
      end
    end

    context 'when API request is successful' do
      let(:success_response) { { "status" => "success", "ticket_id" => "12345" } }

      before do
        stub_request(:post, api_url)
          .with(
            body: valid_payload.to_json,
            headers: {
              'Authorization' => "Bearer #{bearer_token}",
              'Content-Type' => 'application/json',
              'x-WEBHOOK-SECRET' => webhook_secret
            }
          )
          .to_return(
            status: 200,
            body: success_response.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'returns parsed response for 200 status' do
        result = PyroApi.send_api_request(valid_payload)
        expect(result).to eq(success_response)
      end

      it 'makes request with correct headers and payload' do
        PyroApi.send_api_request(valid_payload)
        
        expect(WebMock).to have_requested(:post, api_url)
          .with(
            body: valid_payload.to_json,
            headers: {
              'Authorization' => "Bearer #{bearer_token}",
              'Content-Type' => 'application/json',
              'x-WEBHOOK-SECRET' => webhook_secret
            }
          )
      end
    end

    context 'when API request returns 201 status' do
      let(:created_response) { { "status" => "created", "ticket_id" => "67890" } }

      before do
        stub_request(:post, api_url)
          .to_return(
            status: 201,
            body: created_response.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'returns parsed response for 201 status' do
        result = PyroApi.send_api_request(valid_payload)
        expect(result).to eq(created_response)
      end
    end

    context 'when API request fails' do
      let(:error_response) { { "error" => "Invalid payload" } }

      before do
        stub_request(:post, api_url)
          .to_return(
            status: 400,
            body: error_response.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'logs error and raises exception for non-success status' do
        expect(Rails.logger).to receive(:error).with(
          "Pyro API request failed with response code: 400 :: body: #{error_response} :: payload: #{valid_payload}"
        )

        expect {
          PyroApi.send_api_request(valid_payload)
        }.to raise_error("Pyro API request failed with response code: 400")
      end
    end

    context 'when response has empty body' do
      before do
        stub_request(:post, api_url)
          .to_return(
            status: 200,
            body: "",
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'returns empty hash for empty response body' do
        result = PyroApi.send_api_request(valid_payload)
        expect(result).to eq({})
      end
    end

    context 'when response body is invalid JSON' do
      before do
        stub_request(:post, api_url)
          .to_return(
            status: 200,
            body: "invalid json",
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'logs parsing error and raises exception' do
        expect(Rails.logger).to receive(:error).with(
          /Pyro API response parsing failed: .* :: response body: invalid json/
        )

        expect {
          PyroApi.send_api_request(valid_payload)
        }.to raise_error("Pyro API response parsing failed")
      end
    end

    context 'when network error occurs' do
      before do
        stub_request(:post, api_url).to_raise(SocketError.new("Network error"))
      end

      it 'allows network errors to bubble up' do
        expect {
          PyroApi.send_api_request(valid_payload)
        }.to raise_error(SocketError, "Network error")
      end
    end
  end

  describe 'constants' do
    it 'has correct BASE_URL' do
      expect(PyroApi::BASE_URL).to eq("https://hihrftwrriygnbrsvlrr.supabase.co/functions/v1")
    end

    it 'has correct WEBHOOK_ENDPOINT' do
      expect(PyroApi::WEBHOOK_ENDPOINT).to eq("/ticket-webhook")
    end

    it 'has correct TENANT_ID' do
      expect(PyroApi::TENANT_ID).to eq("d6db1158-2212-4d94-bb01-2c28b971d9a9")
    end
  end
end
