# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ProcessVideoCreativeViewsBatch, type: :worker do
  let(:time) { (1.minute) }
  let(:scheduled_job) { described_class.perform_at(time) }
  describe 'calling perform' do
    context 'enqueues the workers' do
      it 'it enqueues a job' do
        expect do
          described_class.perform_async
        end.to change(described_class.jobs, :size).by(1)
      end

      it 'args are empty' do
        described_class.perform_async
        expect(described_class.jobs.last['args']).to eq([])
      end
    end
    context 'process the cron' do
      before :each do
        @user = FactoryBot.create(:user)
        video = FactoryBot.create(:video, user: @user)
        @event = FactoryBot.create(:event, priority: :high)
        @video_creative = FactoryBot.create(:video_creative, event: @event, video: video)
      end
      it 'calls the method' do
        redis_mock = instance_double(Redis)
        allow(redis_mock).to receive(:spop).and_return(
          JSON.dump({ "video_creative_id" => @video_creative.id, "user_id" => @user.id }),
          nil
        )

        allow($redis).to receive(:spop) { |key| redis_mock.spop(key) }
        allow(VideoCreativeView).to receive(:import).and_return(nil)

        described_class.new.perform

        expect(VideoCreativeView).to have_received(:import)
      end
    end
  end
end
