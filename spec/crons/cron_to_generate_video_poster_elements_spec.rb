require 'rails_helper'

RSpec.describe CronToGenerateVideoPosterElements, type: :worker do
  let(:redis_key) { Constants.user_poster_layouts_table_last_processed_id_key }
  let(:start_id) { 100 }

  before do
    allow($redis).to receive(:get).with(redis_key).and_return(start_id.to_s)
    allow($redis).to receive(:set)
  end

  describe '#perform' do
    context 'when there are no user_poster_layouts' do
      before do
        allow(UserPosterLayout).to receive(:where).and_return(UserPosterLayout.none)
      end

      it 'returns early without processing' do
        expect($redis).not_to receive(:set)
        expect(Rails.logger).not_to receive(:warn)
        described_class.new.perform
      end
    end

    context 'when user_poster_layouts are present' do
      let(:user) { FactoryBot.create(:user) }
      let!(:layout) { FactoryBot.create(:user_poster_layout, id: start_id + 2 , entity: user) }

      before do
        allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                     .and_return([[layout.id, user.id, 'User']])
      end

      context 'and the user has active video frames' do
        before do
          # Create exactly 3 active video frames with different video_frames to trigger the skip condition
          font1 = create(:font, name_font: "Font1", badge_font: "Font1")
          font2 = create(:font, name_font: "Font2", badge_font: "Font2")
          font3 = create(:font, name_font: "Font3", badge_font: "Font3")

          video_frame1 = create(:video_frame, video_type: 'PORTRAIT', font: font1)
          video_frame2 = create(:video_frame, video_type: 'LANDSCAPE', font: font2)
          video_frame3 = create(:video_frame, video_type: 'SQUARE', font: font3)

          create(:user_video_frame, user: user, video_frame: video_frame1, active: true)
          create(:user_video_frame, user: user, video_frame: video_frame2, active: true)
          create(:user_video_frame, user: user, video_frame: video_frame3, active: true)
          # Reload the user to ensure the association is fresh
          user.reload
          # Mock the User query chain that the cron job uses
          allow(User).to receive_message_chain(:includes, :where, :index_by)
                           .and_return({ user.id => user })
        end

        it 'does not enqueue background jobs' do
          worker_double = double('worker')
          allow(CreateUserVideoFramesWorker).to receive(:set).and_return(worker_double)
          allow(GenerateProtocolImageWorker).to receive(:set).and_return(worker_double)
          expect(worker_double).not_to receive(:perform_async)

          described_class.new.perform
        end
      end

      context 'when user has 3 active video frames' do
        let(:user1) { create(:user) }
        before do
          # Mock the send_trial_enabled_notification method to avoid the unexpected :last message error
          allow_any_instance_of(Metadatum).to receive(:send_trial_enabled_notification)
          create(:user_poster_layout, entity_type: 'User', entity_id: user1.id)

          # Create exactly 3 active video frames with different video_frames
          font1 = create(:font, name_font: "Font1_user1", badge_font: "Font1_user1")
          font2 = create(:font, name_font: "Font2_user1", badge_font: "Font2_user1")
          font3 = create(:font, name_font: "Font3_user1", badge_font: "Font3_user1")

          video_frame1 = create(:video_frame, video_type: 'PORTRAIT', font: font1)
          video_frame2 = create(:video_frame, video_type: 'LANDSCAPE', font: font2)
          video_frame3 = create(:video_frame, video_type: 'SQUARE', font: font3)

          create(:user_video_frame, user: user1, video_frame: video_frame1, active: true)
          create(:user_video_frame, user: user1, video_frame: video_frame2, active: true)
          create(:user_video_frame, user: user1, video_frame: video_frame3, active: true)
          # Reload the user to ensure the association is fresh
          user1.reload
          # Mock the User query chain that the cron job uses
          allow(User).to receive_message_chain(:includes, :where, :index_by)
                           .and_return({ user1.id => user1 })
        end

        it 'does not enqueue background jobs for this user' do
          worker_double = double('worker')
          allow(CreateUserVideoFramesWorker).to receive(:set).and_return(worker_double)
          allow(GenerateProtocolImageWorker).to receive(:set).and_return(worker_double)
          expect(worker_double).not_to receive(:perform_async)

          described_class.new.perform
        end
      end



      context 'and the user has no active video frames' do
        before do
          create(:user_video_frame, user: user, active: false)
          # Mock the User query chain that the cron job uses
          allow(User).to receive_message_chain(:includes, :where, :index_by)
                           .and_return({ user.id => user })
        end

        it 'enqueues both background jobs' do
          expect(CreateUserVideoFramesWorker).to receive_message_chain(:set, :perform_async).with(queue: :low).with(user.id)
          expect(GenerateProtocolImageWorker).to receive_message_chain(:set, :perform_async).with(queue: :low).with(user.id)

          described_class.new.perform
        end
      end
    end

    context 'when user_poster_layout has entity_type other than User' do
      before do
        allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                     .and_return([[101, 999, 'Circle']])
      end

      it 'skips processing such layouts' do
        worker_double = double('worker')
        allow(CreateUserVideoFramesWorker).to receive(:set).and_return(worker_double)
        allow(GenerateProtocolImageWorker).to receive(:set).and_return(worker_double)
        expect(worker_double).not_to receive(:perform_async)
        described_class.new.perform
      end
    end

    context 'when raw_layouts contain mixed entity types' do
      let(:user) { create(:user) }
      let(:circle) { create(:circle) }
      let(:layout1) { create(:user_poster_layout, entity: user) }
      let(:layout2) { create(:user_poster_layout, entity: circle) }

      before do
        allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                     .and_return([
                                                   [layout1.id, user.id, 'User'],
                                                   [layout2.id, circle.id, 'Circle']
                                                 ])
      end

      before do
        create(:user_video_frame, user: user, active: false)
        # Mock the User query chain that the cron job uses
        allow(User).to receive_message_chain(:includes, :where, :index_by)
                         .and_return({ user.id => user })
      end

      it 'processes only User entity_type layouts' do
        expect(CreateUserVideoFramesWorker).to receive_message_chain(:set, :perform_async).with(queue: :low).with(user.id)
        expect(GenerateProtocolImageWorker).to receive_message_chain(:set, :perform_async).with(queue: :low).with(user.id)

        described_class.new.perform
      end
    end

    context 'redis key update' do
      let(:user) { create(:user) }

      before do
        create(:user_poster_layout, id: 300, entity: user)
        allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                     .and_return([[300, user.id, 'User']])
        allow(User).to receive(:includes).and_return(User.where(id: user.id).includes(:user_video_frames))
        allow(CreateUserVideoFramesWorker).to receive(:perform_async)
        allow(GenerateProtocolImageWorker).to receive(:perform_async)
        allow(user).to receive(:user_video_frames).and_return([])
      end

      it 'sets the redis key to the latest processed layout id' do
        expect($redis).to receive(:set).with(redis_key, 300)
        described_class.new.perform
      end
    end
  end
end
