require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe CronToSendWatiMessagesToTrialUsers, type: :worker do
  context 'when users have valid trial start dates and are not self-trial users' do
    it 'adds eligible users to Redis and schedules follow-up workers' do
      user1 = create(:user)
      user2 = create(:user)
      user3 = create(:user)
      user4 = create(:user)

      create(:user_poster_layout, entity: user1, active: true)
      create(:user_poster_layout, entity: user2, active: true)
      create(:user_poster_layout, entity: user3, active: true)
      create(:user_poster_layout, entity: user4, active: true)

      create(:metadatum, entity: user1, key: Constants.user_poster_trial_start_date_key, value: 3.days.ago.to_date.to_s)
      create(:metadatum, entity: user2, key: Constants.user_poster_trial_start_date_key, value: 7.days.ago.to_date.to_s)
      create(:metadatum, entity: user3, key: Constants.user_poster_trial_start_date_key, value: 1.day.ago.to_date.to_s)
      create(:metadatum, entity: user4, key: Constants.user_poster_trial_start_date_key, value: 12.days.ago.to_date.to_s)

      described_class.new.perform

      redis_key_day4 = Constants.wati_redis_key_for_day(4)
      redis_key_day8 = Constants.wati_redis_key_for_day(8)

      expect($redis.smembers(redis_key_day4)).to include(user1.id.to_s)
      expect($redis.smembers(redis_key_day8)).to include(user2.id.to_s)
      expect($redis.smembers(redis_key_day4)).not_to include(user3.id.to_s)
      expect($redis.smembers(redis_key_day8)).not_to include(user4.id.to_s)

      args_list = TriggerDayWiseWatiMessagesForTrialUsers.jobs.map { |j| j['args'] }
      expect(args_list).to include([redis_key_day4, 4], [redis_key_day8, 8])
      expect(TriggerPosterGenerationForWatiTrialUsers.jobs.count).to eq(1)
    end

    it 'sets Redis TTL only once per redis key' do
      user1 = create(:user)
      user2 = create(:user)

      create(:user_poster_layout, entity: user1, active: true)
      create(:user_poster_layout, entity: user2, active: true)

      start_date = 3.days.ago.to_date.to_s
      create(:metadatum, entity: user1, key: Constants.user_poster_trial_start_date_key, value: start_date)
      create(:metadatum, entity: user2, key: Constants.user_poster_trial_start_date_key, value: start_date)

      described_class.new.perform

      redis_key = Constants.wati_redis_key_for_day(4)
      expect($redis.ttl(redis_key)).to be > 0
    end

    it 'schedules only one TriggerDayWiseWatiMessagesForTrialUsers job per redis key' do
      user1 = create(:user)
      user2 = create(:user)

      create(:user_poster_layout, entity: user1, active: true)
      create(:user_poster_layout, entity: user2, active: true)

      date = 3.days.ago.to_date.to_s
      create(:metadatum, entity: user1, key: Constants.user_poster_trial_start_date_key, value: date)
      create(:metadatum, entity: user2, key: Constants.user_poster_trial_start_date_key, value: date)

      described_class.new.perform

      redis_key = Constants.wati_redis_key_for_day(4)
      jobs = TriggerDayWiseWatiMessagesForTrialUsers.jobs.select { |j| j['args'][0] == redis_key }

      expect(jobs.count).to eq(1)
    end
  end

  context 'when user has no user poster layout' do
    it 'skips the user and does not schedule any jobs' do
      user = create(:user)

      create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key, value: 3.days.ago.to_date.to_s)

      described_class.new.perform

      redis_key = Constants.wati_redis_key_for_day(4)
      expect($redis.smembers(redis_key)).not_to include(user.id.to_s)
      expect(TriggerDayWiseWatiMessagesForTrialUsers.jobs.count).to eq(0)
      expect(TriggerPosterGenerationForWatiTrialUsers.jobs.count).to eq(1)
    end
  end

  context 'when metadatum is older than 14 days' do
    it 'skips those records' do
      user = create(:user)
      create(:user_poster_layout, entity: user, active: true)

      create(:metadatum, entity: user, key: Constants.user_poster_trial_start_date_key, value: 18.days.ago.to_date.to_s, created_at: 20.days.ago)

      described_class.new.perform
      expect(TriggerDayWiseWatiMessagesForTrialUsers.jobs.count).to eq(0)
    end
  end
end
