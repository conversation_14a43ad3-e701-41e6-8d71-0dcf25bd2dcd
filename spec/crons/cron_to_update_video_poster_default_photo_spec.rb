require "rails_helper"

RSpec.describe CronToUpdateVideoPosterDefaultPhoto, type: :worker do
  let(:start_id) { 100 }
  let(:poster_photo) { create(:photo) }

  before do
    allow($redis).to receive(:get).with(Constants.user_poster_layouts_table_last_processed_id_key).and_return(start_id.to_s)
    allow($redis).to receive(:set)
  end

  context 'when user has poster_photo and video_poster_default_photo_id is blank' do
    let(:user) do
      create(:user, poster_photo: poster_photo, video_poster_default_photo_id: nil)
    end

    before do
      allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                   .and_return([[start_id + 1, user.id, 'User']])
    end

    it 'updates video_poster_default_photo_type and id correctly' do
      expect_any_instance_of(User).to receive(:update_columns).with(
        video_poster_default_photo_type: poster_photo.class.name,
        video_poster_default_photo_id: poster_photo.id
      )

      described_class.new.perform
    end
  end

  context 'when user already has a video_poster_default_photo_id but not type' do
    let(:user) do
      create(:user, poster_photo: poster_photo, video_poster_default_photo_id: 123, video_poster_default_photo_type: nil)
    end

    before do
      allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                   .and_return([[start_id + 1, user.id, 'User']])
    end

    it 'does not update the video_poster_default_photo fields' do
      expect_any_instance_of(User).to receive(:update_columns)
      described_class.new.perform
    end
  end

  context 'when user has no poster_photo' do
    let(:user) do
      create(:user, poster_photo: nil, video_poster_default_photo_id: nil)
    end

    before do
      allow(UserPosterLayout).to receive_message_chain(:where, :limit, :pluck)
                                   .and_return([[start_id + 1, user.id, 'User']])
    end

    it 'does not update the video_poster_default_photo fields' do
      expect_any_instance_of(User).not_to receive(:update_columns)
      described_class.new.perform
    end
  end
end
