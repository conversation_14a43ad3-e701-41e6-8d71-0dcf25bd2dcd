# Implementation Plan

- [x] 1. Add JavaScript event handler for user selection changes
  - Create event listener for Select2 change events on the user selection dropdown
  - Handle both selection and clearing of user selection
  - Implement proper Select2 callback integration for `#user-role-user-id-select`
  - _Requirements: 1.5, 2.1_

- [x] 2. Implement AJAX communication function
  - Create function to make AJAX call to `get_badge_free_text_for_user` endpoint
  - Handle success and error responses appropriately
  - Parse JSON response to determine badge text status
  - _Requirements: 2.1, 2.2, 2.4_

- [x] 3. Create checkbox visibility management functions
  - Implement `showBadgeTextCheckbox()` function to display the checkbox wrapper
  - Implement `hideBadgeTextCheckbox()` function to hide checkbox and clear its state
  - Ensure proper DOM element targeting using `#badge_free_text_deletion_wrapper`
  - _Requirements: 1.1, 1.2, 1.3, 2.5_

- [x] 4. Add page restriction logic
  - Implement check to ensure functionality only runs on new user role pages
  - Add path validation to prevent execution on edit pages
  - Use `window.location.pathname` to detect current page context
  - _Requirements: 1.4_

- [x] 5. Integrate with existing user role form JavaScript
  - Add the new functionality to the existing user role form JavaScript block
  - Ensure compatibility with existing Select2 initialization and event handlers
  - Place code within the appropriate `$(document).ready()` block for user roles
  - _Requirements: 1.5, 2.1_

- [x] 6. Add error handling and edge cases
  - Handle AJAX request failures gracefully by hiding checkbox
  - Manage cases where user selection is cleared or invalid
  - Implement console logging for debugging purposes
  - _Requirements: 2.4_

- [x] 7. Test user selection workflow
  - Verify checkbox appears when user with badge text is selected
  - Verify checkbox remains hidden when user without badge text is selected
  - Test checkbox behavior when user selection is cleared
  - _Requirements: 1.1, 1.2, 1.3, 1.5_

- [x] 8. Test form submission integration
  - Verify checkbox value is properly submitted when checked
  - Confirm existing form processing logic handles the checkbox parameter
  - Test that badge text deletion occurs when checkbox is checked
  - _Requirements: 3.3, 3.4_

- [x] 9. Validate page restrictions
  - Confirm functionality only works on new user role creation pages
  - Verify checkbox never appears on user role edit pages
  - Test that existing edit functionality remains unaffected
  - _Requirements: 1.4_

- [x] 10. Perform cross-browser compatibility testing
  - Test functionality in different browsers supported by ActiveAdmin
  - Verify Select2 integration works consistently across browsers
  - Confirm AJAX requests and DOM manipulation work properly
  - _Requirements: All requirements_