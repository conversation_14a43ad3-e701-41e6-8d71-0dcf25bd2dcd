# Requirements Document

## Introduction

This feature implements dynamic visibility for the "Delete Badge Text from RM" checkbox in the ActiveAdmin user roles form. The checkbox should only be visible when creating a new user role and the selected user has existing badge free text that can be deleted. This provides administrators with the option to clean up existing badge text when assigning new roles to users.

## Requirements

### Requirement 1

**User Story:** As an admin creating a new user role, I want to see a checkbox to delete existing badge text only when the selected user has badge text, so that I can optionally clean up their existing badge text when assigning the new role.

#### Acceptance Criteria

1. <PERSON><PERSON><PERSON> creating a new user role AND no user is selected THEN the delete badge text checkbox SHALL be hidden
2. <PERSON><PERSON><PERSON> creating a new user role AND a user is selected AND the user has no badge free text THEN the delete badge text checkbox SHALL remain hidden
3. <PERSON><PERSON><PERSON> creating a new user role AND a user is selected AND the user has badge free text THEN the delete badge text checkbox SHALL be shown
4. WH<PERSON> editing an existing user role THEN the delete badge text checkbox SHALL never be shown
5. WHEN the user selection changes THEN the checkbox visibility SHALL update accordingly based on the new user's badge text status

### Requirement 2

**User Story:** As an admin, I want the system to automatically check if a user has badge free text when I select them, so that the interface updates immediately without requiring a page refresh.

#### Acceptance Criteria

1. WHEN a user is selected in the user dropdown THEN the system SHALL make an AJAX call to check if the user has badge free text
2. WHEN the AJAX call succeeds AND the user has badge free text THEN the checkbox container SHALL be shown with appropriate styling
3. WHEN the AJAX call succeeds AND the user has no badge free text THEN the checkbox container SHALL remain hidden
4. WHEN the AJAX call fails THEN the checkbox container SHALL remain hidden and a reload suggestion SHALL be shown to the user
5. WHEN the user selection is cleared THEN the checkbox container SHALL be hidden immediately

### Requirement 3

**User Story:** As an admin, I want clear labeling and instructions for the delete badge text checkbox, so that I understand what action will be performed when I check it.

#### Acceptance Criteria

1. WHEN the delete badge text checkbox is shown THEN it SHALL have the label "Delete Badge Text from RM"
2. WHEN the delete badge text checkbox is shown THEN it SHALL include a hint explaining "Check this box to delete the current badge text from RM when creating this user role."
3. WHEN the checkbox is checked AND the form is submitted THEN the user's existing badge free text SHALL be deleted via the `delete_badge_free_text_from_rm_layout_creation` method
4. WHEN the checkbox is unchecked AND the form is submitted THEN the user's existing badge free text SHALL remain unchanged
5. WHEN the form validation fails THEN the checkbox state SHALL be preserved