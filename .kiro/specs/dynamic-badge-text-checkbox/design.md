# Design Document

## Overview

The dynamic badge text checkbox feature enhances the ActiveAdmin user roles form by conditionally showing a checkbox that allows administrators to delete existing badge free text when creating new user roles. The feature uses AJAX to check if a selected user has badge free text and dynamically shows/hides the checkbox accordingly.

## Architecture

The solution consists of three main components:

1. **Frontend JavaScript**: Handles user selection events, makes AJAX calls, and manages checkbox visibility
2. **Backend API Endpoint**: Existing `get_badge_free_text_for_user` member action that returns user badge text status
3. **Form Integration**: The checkbox is already present in the form but needs dynamic visibility control

## Components and Interfaces

### JavaScript Event Handler

**Purpose**: Monitor user selection changes and manage checkbox visibility

**Key Functions**:
- Listen for changes on the user selection dropdown (`#user-role-user-id-select`)
- Make AJAX calls to check user badge text status
- Show/hide the checkbox container based on response
- Handle loading states and error conditions

**Interface**:
```javascript
// Event listener for user selection changes
$('#user-role-user-id-select').on('change', function() {
    const userId = $(this).val();
    handleUserSelection(userId);
});

// Function to handle user selection and checkbox visibility
function handleUserSelection(userId) {
    if (!userId) {
        hideBadgeTextCheckbox();
        return;
    }
    
    checkUserBadgeText(userId);
}
```

### AJAX Communication

**Purpose**: Communicate with backend to check user badge text status

**Endpoint**: `/admin/user_roles/:user_id/get_badge_free_text_for_user`
**Method**: GET
**Parameters**: `user_id`

**Response Format**:
```json
{
    "success": true,
    "has_badge_free_text": boolean,
    "badge_free_text": string|null
}
```

**Interface**:
```javascript
function checkUserBadgeText(userId) {
    $.ajax({
        url: `/admin/user_roles/${userId}/get_badge_free_text_for_user`,
        method: 'GET',
        data: { user_id: userId },
        success: function(response) {
            if (response.success && response.has_badge_free_text) {
                showBadgeTextCheckbox();
            } else {
                hideBadgeTextCheckbox();
            }
        },
        error: function() {
            hideBadgeTextCheckbox();
        }
    });
}
```

### Checkbox Visibility Management

**Purpose**: Control the display state of the badge text deletion checkbox

**Target Element**: `#badge_free_text_deletion_wrapper`
**Checkbox Element**: `#delete-badge-free-text-checkbox`

**Interface**:
```javascript
function showBadgeTextCheckbox() {
    $('#badge_free_text_deletion_wrapper').show();
}

function hideBadgeTextCheckbox() {
    $('#badge_free_text_deletion_wrapper').hide();
    $('#delete-badge-free-text-checkbox').prop('checked', false);
}
```

## Data Models

### User Badge Text Status

The existing `User` model already has the method `get_badge_free_text_for_rm_layout_creation` which returns the user's badge free text. The `get_badge_free_text_for_user` member action uses this method to determine if the user has badge text.

### Form Data

The checkbox value is already handled by the existing form processing logic:
- Checkbox name: `delete_badge_free_text`
- When checked, the value is '1'
- The controller already handles this parameter in the create action

## Error Handling

### AJAX Request Failures

**Strategy**: Graceful degradation
- If AJAX request fails, hide the checkbox (safe default) & show an suggestion message to reload the page.
- No error messages shown to user (non-critical feature)
- Log errors to console for debugging

### Invalid User Selection

**Strategy**: Input validation
- If no user is selected, immediately hide checkbox
- If invalid user ID is provided, hide checkbox
- Clear checkbox state when hiding

### Backend Errors

**Strategy**: Safe defaults
- If backend returns error response, hide checkbox
- If user doesn't exist, hide checkbox
- If user has no badge text, hide checkbox

## Testing Strategy

### Unit Tests (JavaScript)
**Not required**

### Integration Tests
**Not required**

### Manual Testing

**Test Scenarios**:
1. Create new user role with user who has badge text
2. Create new user role with user who has no badge text
3. Change user selection multiple times
4. Submit form with checkbox checked/unchecked
5. Test on edit page (checkbox should not appear)

## Implementation Notes

### Existing Code Integration

The checkbox HTML and form processing logic already exist in the codebase:
- Form field is already defined with proper styling
- Controller already handles the `delete_badge_free_text` parameter
- Backend method `delete_badge_free_text_from_rm_layout_creation` already exists

### Performance Considerations

- AJAX calls are made only when user selection changes
- No polling or continuous requests
- Minimal DOM manipulation (show/hide single element)
- Debouncing not needed due to dropdown selection nature

### Browser Compatibility

- Uses jQuery (already loaded in ActiveAdmin)
- Standard AJAX and DOM manipulation
- No modern JavaScript features that require polyfills
- Compatible with all browsers supported by ActiveAdmin

## Security Considerations

### Input Validation

- User ID is validated on backend before processing
- AJAX endpoint uses existing authentication/authorization
- No sensitive data exposed in AJAX responses

### CSRF Protection

- AJAX requests use existing Rails CSRF protection
- Form submission uses standard Rails CSRF tokens
- No additional security measures needed