GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_admin_import (5.1.0)
      activeadmin (>= 1.0.0)
      activerecord-import (>= 0.27)
      rchardet (>= 1.6)
      rubyzip (>= 1.2)
    activeadmin (3.2.2)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activeadmin-searchable_select (1.8.0)
      activeadmin (>= 1.x, < 4)
      jquery-rails (>= 3.0, < 5)
      select2-rails (~> 4.0)
    activeadmin_dark_color_scheme (0.1.13)
      activeadmin (>= 2)
      railties (>= 4)
    activeadmin_dynamic_fields (0.8.0)
      activeadmin (>= 2.0, < 4)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activejob-traffic_control (0.1.3)
      activejob (>= 4.2)
      activesupport (>= 4.2)
      suo
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (1.8.1)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.960.0)
    aws-sdk-core (3.201.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-lambda (1.125.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.156.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-translate (1.70.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.0)
    benchmark-ips (2.13.0)
    bigdecimal (3.1.8)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    brpoplpush-redis_script (0.1.3)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      redis (>= 1.0, < 6)
    builder (3.3.0)
    byebug (11.1.3)
    case_transform2 (1.1.1)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.0)
    dalli (3.2.8)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    dead_end (4.0.0)
    declarative (0.0.20)
    derailed_benchmarks (2.1.2)
      benchmark-ips (~> 2)
      dead_end
      get_process_mem (~> 0)
      heapy (~> 0)
      memory_profiler (>= 0, < 2)
      mini_histogram (>= 0.3.0)
      rack (>= 1)
      rack-test
      rake (> 10, < 14)
      ruby-statistics (>= 2.1)
      thor (>= 0.19, < 2)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.1)
    docile (1.4.1)
    drb (2.2.1)
    dry-configurable (1.2.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    ed25519 (1.3.0)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.4.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.10.0)
      faraday-net_http (>= 2.0, < 3.2)
      logger
    faraday-net_http (2.1.0)
    faraday-retry (2.2.1)
      faraday (~> 2.0)
    faraday_middleware-aws-sigv4 (1.0.1)
      aws-sigv4 (~> 1.0)
      faraday (>= 2.0, < 3)
    fastimage (2.3.1)
    fcm (1.0.8)
      faraday (>= 1.0.0, < 3.0)
      googleauth (~> 1)
    ffi (1.17.0-aarch64-linux-gnu)
    ffi (1.17.0-x86_64-linux-gnu)
    firebase_dynamic_link (3.0.0)
      case_transform2 (>= 1.0, < 2.0)
      dry-configurable (>= 1.0, < 2.0)
      faraday (>= 0.16, < 3.0)
      faraday-net_http (>= 1.0, < 3.0)
    font-awesome-sass (6.5.2)
      sassc (~> 2.0)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gapic-common (0.22.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (>= 3.25, < 5.a)
      googleapis-common-protos (~> 1.6)
      googleapis-common-protos-types (~> 1.15)
      googleauth (~> 1.11)
      grpc (~> 1.65)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-core (0.15.1)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-sheets_v4 (0.33.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-core (1.7.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.4.0)
    google-cloud-translate-v3 (0.9.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-vision (1.5.0)
      google-cloud-core (~> 1.6)
      google-cloud-vision-v1 (>= 0.13, < 2.a)
      google-cloud-vision-v1p3beta1 (>= 0.12, < 2.a)
    google-cloud-vision-v1 (1.0.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-vision-v1p3beta1 (0.13.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-protobuf (4.27.5-aarch64-linux)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.27.5-x86_64-linux)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos (1.6.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.15.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.11.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grpc (1.65.2-aarch64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.65.2-x86_64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.1.0)
    hashid-rails (1.4.1)
      activerecord (>= 4.0)
      hashids (~> 1.0)
    hashids (1.0.6)
    hashie (5.0.0)
    heapy (0.2.0)
      thor
    highline (3.1.0)
      reline
    hiredis (0.6.3)
    hiredis-client (0.22.2)
      redis-client (= 0.22.2)
    honeybadger (5.15.4)
    htmlcsstoimage-api (0.1.3)
      addressable (> 2.5.0)
      httparty (> 0.10)
    htmlentities (4.3.4)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    i18n-tasks (1.0.14)
      activesupport (>= 4.0.2)
      ast (>= 2.1.0)
      erubi
      highline (>= 2.0.0)
      i18n
      parser (>= *******)
      rails-i18n
      rainbow (>= 2.2.2, < 4.0)
      terminal-table (>= 1.5.1)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.7.2)
    jwt (2.8.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (3.17.0.3)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.0)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    memory_profiler (1.0.2)
    mini_histogram (0.3.1)
    mini_mime (1.1.5)
    minitest (5.24.1)
    mixpanel-ruby (2.3.0)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.2.0)
    mysql2 (0.5.6)
    nanoid (2.0.0)
    nats-pure (2.4.0)
      concurrent-ruby (~> 1.0)
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.2.3)
    newrelic_rpm (9.19.0)
    nio4r (2.7.3)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.1.2)
      jwt (>= 2.0)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    opensearch-ruby (3.4.0)
      faraday (>= 1.0, < 3)
      multi_json (>= 1.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    paper_trail (15.1.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.25.1)
    parser (3.3.4.0)
      ast (~> 2.4.1)
      racc
    psych (5.1.2)
      stringio
    public_suffix (6.0.1)
    puma (6.4.3)
      nio4r (~> 2.0)
    pundit (2.3.2)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.14)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (1.0.0)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.1)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.9)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.2.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rchardet (1.8.0)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redis (4.8.1)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-prescription (2.6.0)
    regexp_parser (2.9.2)
    reline (0.5.9)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    retriable (3.1.2)
    rexml (3.3.9)
    rotp (6.3.0)
    rspec (3.12.0)
      rspec-core (~> 3.12.0)
      rspec-expectations (~> 3.12.0)
      rspec-mocks (~> 3.12.0)
    rspec-core (3.12.3)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.7)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (6.0.4)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    rspec-sidekiq (5.0.0)
      rspec-core (~> 3.0)
      rspec-expectations (~> 3.0)
      rspec-mocks (~> 3.0)
      sidekiq (>= 5, < 8)
    rspec-support (3.12.2)
    rubocop (1.65.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.3)
      parser (>= *******)
    ruby-progressbar (1.13.0)
    ruby-statistics (4.0.0)
    ruby2_keywords (0.0.5)
    ruby_http_client (3.5.5)
    rubyzip (2.3.2)
    rufus-scheduler (3.9.1)
      fugit (~> 1.1, >= 1.1.6)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    searchkick (5.3.1)
      activemodel (>= 6.1)
      hashie
    securerandom (0.4.0)
    select2-rails (4.0.13)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    shoulda-matchers (5.3.0)
      activesupport (>= 5.2.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    sidekiq-assured-jobs (1.1.0)
      redis (~> 4.0)
      sidekiq (>= 6.0, < 7)
    sidekiq-batch (0.1.9)
      sidekiq (>= 3)
    sidekiq-scheduler (5.0.5)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    sidekiq-throttled (1.4.0)
      concurrent-ruby (>= 1.2.0)
      redis-prescription (~> 2.2)
      sidekiq (>= 6.5)
    sidekiq-unique-jobs (7.1.33)
      brpoplpush-redis_script (> 0.1.1, <= 2.0.0)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      redis (< 5.0)
      sidekiq (>= 5.0, < 7.0)
      thor (>= 0.20, < 3.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    slack-notifier (2.4.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    spring (4.2.1)
    spring-watcher-listen (2.1.0)
      listen (>= 2.7, < 4.0)
      spring (>= 4)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.23.0)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
    sshkit-sudo (0.1.0)
      sshkit (~> 1.8)
    stackprof (0.2.26)
    stringio (3.1.1)
    suo (0.4.0)
      dalli
      msgpack
      redis
    telephone_number (1.4.21)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    thor (1.4.0)
    tilt (2.4.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    twilio-ruby (7.3.6)
      faraday (>= 0.9, < 3.0)
      jwt (>= 1.5, < 3.0)
      nokogiri (>= 1.6, < 2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2024.1)
      tzinfo (>= 1.0.0)
    uber (0.1.0)
    ulid (1.4.0)
    unicode-display_width (2.5.0)
    version_gem (1.1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.8.2)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.17)

PLATFORMS
  aarch64-linux
  x86_64-linux

DEPENDENCIES
  aasm
  active_admin_import
  activeadmin (< 3.3.0)
  activeadmin-searchable_select
  activeadmin_dark_color_scheme
  activeadmin_dynamic_fields
  activejob-traffic_control
  activerecord-import
  aws-sdk-lambda
  aws-sdk-s3 (< 2)
  aws-sdk-translate (~> 1.0)
  bcrypt_pbkdf (>= 1.1.0, < 2.0)
  bootsnap (>= 1.16.0)
  byebug
  caxlsx
  concurrent-ruby
  connection_pool
  database_cleaner
  derailed_benchmarks
  devise
  ed25519 (>= 1.3.0, < 2.0)
  factory_bot_rails (< 7)
  faker
  faraday_middleware-aws-sigv4
  fastimage
  fcm
  firebase_dynamic_link
  font-awesome-sass
  google-apis-sheets_v4
  google-cloud-translate-v3 (~> 0.9.0)
  google-cloud-vision (~> 1.3)
  hashid-rails
  hiredis
  hiredis-client
  honeybadger
  htmlcsstoimage-api
  httparty
  i18n-tasks
  irb
  listen (>= 3.8.0)
  mixpanel-ruby
  mysql2 (>= 0.5.5, < 0.6.0)
  nanoid
  nats-pure
  net-ssh (< 8)
  newrelic_rpm
  nokogiri
  omniauth-google-oauth2
  omniauth-rails_csrf_protection
  opensearch-ruby
  paper_trail
  puma (< 7)
  pundit
  rack-attack
  rack-cors
  rails (~> *******)
  ransack
  redis (~> 4)
  redis-namespace (~> 1.11.0)
  rotp
  rspec (~> 3.12.0)
  rspec-rails (~> 6.0.1)
  rspec-sidekiq
  rubocop
  sassc-rails (= 2.1.2)
  searchkick
  sendgrid-ruby
  shoulda-matchers (~> 5.3.0)
  sidekiq (< 7)
  sidekiq-assured-jobs (~> 1.1.0)
  sidekiq-batch
  sidekiq-scheduler
  sidekiq-throttled
  sidekiq-unique-jobs (< 8)
  simplecov
  slack-notifier
  spring
  spring-watcher-listen (~> 2.1.0)
  sshkit-sudo
  stackprof
  telephone_number
  twilio-ruby
  tzinfo-data
  ulid (~> 1.4)
  unicode-display_width
  webmock (~> 3.18, >= 3.18.1)

RUBY VERSION
   ruby 3.3.7p123

BUNDLED WITH
   2.5.16
