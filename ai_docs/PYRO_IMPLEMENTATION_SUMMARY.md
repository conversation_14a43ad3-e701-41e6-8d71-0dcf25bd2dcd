# Pyro Implementation Summary

## Overview
Successfully implemented a new external service named **Pyro** for support tickets to replace `Zoho::CreateTicket`. The implementation follows the same architectural pattern as the existing Zoho implementation but is adapted for Pyro's webhook-based API.

## Files Created

### 1. `/app/custom/pyro_api.rb`
- **Purpose**: Core API client for Pyro service
- **Key Features**:
  - Handles HTTP requests to Pyro webhook endpoint
  - Bearer token authentication
  - Custom webhook secret header
  - Error handling and logging
  - Fixed tenant ID configuration

### 2. `/app/workers/pyro/send_api_request.rb`
- **Purpose**: Sidekiq worker for asynchronous API requests
- **Key Features**:
  - Rate limiting (5 requests per second)
  - Retry configuration
  - Performance logging
  - Uses dedicated `:pyro` queue

### 3. `/app/workers/pyro/create_ticket.rb`
- **Purpose**: Main worker for creating support tickets
- **Key Features**:
  - Processes user data and ticket information
  - Maps data to Pyro's expected payload structure
  - Handles phone number formatting
  - Determines poster subscription status
  - Builds comprehensive ticket payload

## Key Differences from Zoho Implementation

### 1. **No Contact Creation Required**
- <PERSON><PERSON><PERSON> works directly with `user_id` (no need to create/manage contacts)
- Simplified workflow compared to Zoho's contact management

### 2. **Webhook-Based API**
- Uses POST request to webhook endpoint
- Bearer token + webhook secret authentication
- Single endpoint vs. Zoho's multiple endpoints

### 3. **Enhanced Payload Structure**
- Includes additional fields like `poster`, `atleast_paid_once`
- More detailed user status information
- Direct mapping to support dashboard requirements

## API Configuration

```ruby
# Pyro API Endpoints
BASE_URL = "https://hihrftwrriygnbrsvlrr.supabase.co/functions/v1"
WEBHOOK_ENDPOINT = "/ticket-webhook"
TENANT_ID = "d6db1158-2212-4d94-bb01-2c28b971d9a9"
```

## Updated Usage

### Before (Zoho)
```ruby
Zoho::CreateTicket.perform_async(user.id, reason, source, {
  rm_name: rm_user_name,
  layout_status: layout_status,
  subscription_status: subscription_status,
  is_user_atleast_paid_once: is_user_atleast_paid_once,
  badge: badge,
})
```

### After (Pyro)
```ruby
Pyro::CreateTicket.perform_async(user.id, reason, source, {
  rm_name: rm_user_name,
  layout_status: layout_status,
  subscription_status: subscription_status,
  is_user_atleast_paid_once: is_user_atleast_paid_once,
  badge: badge,
})
```

## Payload Structure

The Pyro payload includes:
- `tenant_id`: Fixed tenant identifier
- `ticket_date`: ISO8601 formatted timestamp
- `user_id`: Internal user ID as string
- `name`: User's display name
- `phone`: Formatted phone number with country code
- `source`: Ticket source (humanized)
- `subscription_status`: User's subscription status
- `atleast_paid_once`: Boolean payment history flag
- `reason`: Ticket type/reason (humanized)
- `badge`: User's badge status
- `poster`: User's poster subscription status ("paid" or "trial")
- `layout_status`: Layout completion status
- `rm_name`: Relationship manager name
- `praja_dashboard_user_link`: Direct link to user dashboard
- `display_pic_url`: User's profile picture URL

## Integration Points

The controller integration remains minimal - only the worker class name changed from `Zoho::CreateTicket` to `Pyro::CreateTicket` in `/app/controllers/users_controller.rb`.

## Benefits

1. **Simplified Architecture**: No contact management overhead
2. **Better Data Mapping**: Direct alignment with support team requirements
3. **Enhanced Monitoring**: Comprehensive payload for better ticket handling
4. **Maintained Compatibility**: Same interface as existing Zoho implementation
5. **Async Processing**: Maintains non-blocking request processing

## Queue Configuration

The implementation uses a dedicated `:pyro` queue for isolation and monitoring. Ensure your Sidekiq configuration includes this queue.