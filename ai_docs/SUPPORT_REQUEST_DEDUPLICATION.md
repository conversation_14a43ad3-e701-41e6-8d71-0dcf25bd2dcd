# Support Request Deduplication

## Overview

This feature implements a Redis-based deduplication mechanism to prevent users from submitting multiple support tickets within a configurable time window (default: 90 minutes). This applies to both Google Sheets exports and Pyro ticket creation.

## How It Works

### Core Logic

1. **First Request**: When a user submits their first support request, it's processed normally and a Redis key is created with an expiration time.

2. **Subsequent Requests**: Before processing any new support request, the system checks if a Redis key exists for the user indicating they're in a cooldown period.

3. **Cooldown Period**: By default, users must wait 90 minutes between support requests. This is configurable via the `SUPPORT_REQUEST_COOLDOWN_MINUTES` environment variable.

4. **Reason Agnostic**: The deduplication works regardless of the reason for the support request. A user cannot submit ANY support request within the cooldown period, even if it's for a different issue.

5. **User Experience**: When a request is blocked due to cooldown, the user still receives a success message to maintain a good user experience.

### Implementation Details

#### Redis Key Structure

```
support_request_cooldown:{user_id}
```

The key stores JSON data with request metadata and automatically expires after the cooldown period.

#### Service Methods

- `SupportRequestDeduplicationService.can_user_submit_request?(user_id)` - Checks if user can submit a new request
- `SupportRequestDeduplicationService.record_request(user_id, reason, source)` - Records a new support request (only if no key exists)
- `SupportRequestDeduplicationService.get_request_info(user_id)` - Returns stored request information

#### Controller Integration

The deduplication logic is integrated into the `log_to_support_requests_sheet` method in `UsersController`, which is called by:

1. `support_callback_request` - API endpoint for app-based support requests
2. `menu_selection` - IVR-based support requests

### Configuration

Set the cooldown period using the environment variable:

```bash
# Set cooldown to 60 minutes instead of default 90
SUPPORT_REQUEST_COOLDOWN_MINUTES=60
```

### Entry Points Protected

1. **App Support Sheet** (`/request-support-callback`)
   - Called via `support_callback_request` → `log_to_support_requests_sheet`

2. **IVR Support** (`/menu-selection`)
   - Called via `menu_selection` → `log_to_support_requests_sheet` when user selects option '1'

### What Gets Blocked

When a user is in the cooldown period:

- ✅ **Google Sheets Export**: `ExportDataToGoogleSheets.perform_async` is **NOT** called
- ✅ **Pyro Ticket Creation**: `Pyro::CreateTicket.perform_async` is **NOT** called  
- ✅ **User Response**: User receives the normal success message (not a cooldown error message)
- ✅ **Redis Key**: No new Redis key is created or TTL extended for duplicate requests

### What Doesn't Get Affected

- Other Google Sheets exports (poster data, RM flow data, etc.) continue to work normally
- Non-support related API endpoints are unaffected
- After the Redis key expires (90 minutes), users can submit new support requests normally
- Redis performance - keys automatically expire and are cleaned up

## Testing

Comprehensive test coverage includes:

### Service Tests (`spec/services/support_request_deduplication_service_spec.rb`)

- First-time request scenarios
- Cooldown period validation  
- Time calculation accuracy
- Redis key creation and expiration
- Duplicate request blocking
- Environment variable configuration

### Controller Tests (`spec/controllers/users_controller_spec.rb`)

- Normal support request flow
- Duplicate request blocking with success messages
- Different reasons within cooldown
- IVR call integration
- Worker enqueueing verification
- Redis key cleanup

### Rake Tasks

- `rake support_request:test` - Comprehensive functionality test
- `rake support_request:reset[USER_ID]` - Clear cooldown for specific user
- `rake support_request:stats` - Show current cooldown statistics  
- `rake support_request:test_redis` - Test Redis connectivity

## Example Usage

```ruby
# Check if user can submit a request
if SupportRequestDeduplicationService.can_user_submit_request?(user.id)
  # Process the support request
  SupportRequestDeduplicationService.record_request(user.id, 'badge_change', 'support_sheet')
  # ... enqueue workers
  return success_message
else
  # User is in cooldown period - still show success to user
  return success_message  # Don't indicate they're being rate limited
end
```

## Deployment

To deploy this feature:

1. Deploy the updated application code (no database migrations required)
2. Ensure Redis is accessible via the `$redis` global variable
3. Optionally set `SUPPORT_REQUEST_COOLDOWN_MINUTES` environment variable

The feature works immediately after deployment - Redis keys are created on-demand for first-time requesters.

## Redis Key Management

- **Automatic Cleanup**: Keys expire automatically after the cooldown period
- **Memory Efficient**: Only stores active cooldowns, not historical data
- **No Maintenance**: No database tables to maintain or migrate
- **Pattern**: `support_request_cooldown:{user_id}`

## Monitoring

Monitor Redis key count with:
```bash
# Count active cooldown keys
redis-cli --eval "return #redis.call('keys', 'support_request_cooldown:*')" 0

# Or use the rake task
rake support_request:stats
```