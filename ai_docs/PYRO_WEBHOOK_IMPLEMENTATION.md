# Pyro Webhook Implementation for Mixpanel Events

## Overview
This implementation adds a new webhook endpoint for Pyro to send triggers for mixpanel events. The implementation follows the same architectural pattern as the existing `FlowwController` but is specifically designed for Pyro service integration.

## Implementation Details

### 1. External Service Controller Update
- Added `pyro` to the `ALLOWED_SERVICES` list in `ExternalServiceController`
- This enables Pyro to authenticate using the same basic auth mechanism as other external services

### 2. PyroController
**File:** `app/controllers/pyro_controller.rb`

Key features:
- Inherits from `ExternalServiceController` for consistent authentication
- Implements `send_to_mixpanel` method following the exact same pattern as FlowwController
- Includes proper validation for required parameters (`user_id`, `event_name`)
- Accepts event properties directly in the `properties` parameter
- Uses `EventTracker.perform_async` to send events to both Mixpanel and Firebase

### 3. Routes Configuration
**File:** `config/routes.rb`

Added new route scope:
```ruby
scope 'pyro', controller: :pyro do
  post :send_to_mixpanel
  match "/send_to_mixpanel", to: "external_service#head_route", via: :head
end
```

### 4. Test Coverage
**File:** `spec/controllers/pyro_controller_spec.rb`

Comprehensive test suite covering:
- Successful event tracking with valid parameters
- Error handling for missing `event_name`
- Error handling for missing `user_id`
- Error handling for non-existent users
- Authentication validation
- Handling of missing properties parameter
- Direct passing of properties to EventTracker

## Authentication
The webhook uses the same authentication mechanism as other external services:

- **Type:** Basic Authentication
- **Username:** Stored in `Rails.application.credentials[:pyro][:auth_username]`
- **Password:** Stored in `Rails.application.credentials[:pyro][:auth_secret]`
- **Header Format:** `Authorization: Bearer <base64_encoded_credentials>`

## API Usage

### Endpoint
`POST /pyro/send_to_mixpanel`

### Request Format
```json
{
  "user_id": 12345,
  "event_name": "user_action_performed",
  "properties": {
    "property1": "value1",
    "property2": "value2",
    "custom_data": {
      "nested": "property"
    }
  }
}
```

### Response Format
**Success (200 OK):**
```json
{
  "success": true
}
```

**Error (400 Bad Request):**
```json
{
  "success": false,
  "message": "Event name not present"
}
```

**Error (404 Not Found):**
```json
{
  "success": false,
  "message": "User not found"
}
```

**Error (401 Unauthorized):**
```json
{
  "message": "Unauthorized"
}
```

## Event Processing
Once the webhook receives a valid request:

1. The event is queued via `EventTracker.perform_async`
2. EventTracker sends the event to both:
   - **Mixpanel** using `$mixpanel_tracker.track`
   - **Firebase** for app analytics (if user has valid device tokens)

## Configuration Requirements
Ensure these credentials are configured in Rails credentials:

```yaml
pyro:
  auth_username: "your_pyro_username"
  auth_secret: "your_pyro_secret"
```

## Testing
Run the test suite with:
```bash
docker exec -it praja-api bundle exec rspec spec/controllers/pyro_controller_spec.rb
```

## Notes
- The implementation maintains consistency with the existing FlowwController pattern
- All validation and error handling follows the same conventions
- Event properties are passed directly from the `properties` parameter to EventTracker
- The webhook is designed to handle high-volume event tracking efficiently through background job processing
- The `properties` parameter is optional - if not provided, an empty hash will be sent to EventTracker