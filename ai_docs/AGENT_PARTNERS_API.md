# Agent Partners API

## Overview

The Agent Partners API provides endpoints for agent partners to manage leads within the Praja system. Agent partners are special admin users with the `agent_partner` role who can start the lead process by phone number.

## Authentication

All endpoints require admin user authentication via Devise session cookies. Additionally, the admin user must have the `agent_partner` role to access these endpoints.

## Endpoints

### POST /admin/agent-partners/start-lead-process

Starts the lead process for a given phone number, creating necessary records and assigning the lead to the current agent partner as the Relationship Manager (RM).

#### Request

**URL:** `POST /admin/agent-partners/start-lead-process`

**Headers:**
- `Content-Type: application/json`
- Valid admin session cookie

**Body:**
```json
{
  "phone": "9876543210"
}
```

**Parameters:**
- `phone` (string, required): The phone number of the lead to start the process for. Accepts various formats:
  - 10-digit number: `9876543210`
  - With country code: `+************` or `************`
  - With formatting: `+91 ************` or `************`

#### Response

**Success (200 OK):**
```json
{
  "success": true,
  "user_id": 12345,
  "premium_pitch_status": "interested"
}
```

**Error Responses:**

**400 Bad Request - Missing phone number:**
```json
{
  "error": "Phone number is required"
}
```

**400 Bad Request - Invalid phone format:**
```json
{
  "error": "Invalid phone number format"
}
```

**403 Forbidden - Not an agent partner:**
```json
{
  "error": "Forbidden"
}
```

**409 Conflict - Lead process already started:**
```json
{
  "error": "Lead process already started by another RM"
}
```

**422 Unprocessable Entity - Database validation error:**
```json
{
  "error": "Validation failed: [specific error message]"
}
```

**500 Internal Server Error - Unexpected error:**
```json
{
  "error": "An error occurred while starting the lead process"
}
```

#### Business Logic

1. **Phone Number Validation**: The phone number is normalized using the TelephoneNumber gem to handle various international formats, then validated to ensure it's a valid Indian mobile number.

2. **User Lookup/Creation**: 
   - If a user with the phone number exists, it's used
   - If no user exists, a new user is created with `pre_signup` status

3. **PremiumPitch Handling**:
   - If no PremiumPitch exists for the user, a new one is created with:
     - `status`: `interested`
     - `lead_type`: `OABLT_Outbound`
   - If a PremiumPitch already exists and no RM is assigned, it's updated with:
     - `status`: `interested`
     - `lead_type`: `OABLT_Outbound`

4. **RM Assignment Conflict Check**:
   - If the user already has an RM assigned and it's not the current agent partner, the request is rejected with a 409 Conflict
   - If the same agent partner is already assigned, the operation succeeds without changes

5. **RM Assignment**: If no RM is assigned, the current agent partner is assigned as the RM for the user

#### Phone Number Formats Supported

The endpoint accepts phone numbers in various formats and normalizes them using the TelephoneNumber gem:

- `9876543210` → `9876543210`
- `+************` → `9876543210`
- `************` → `9876543210`
- `+91 ************` → `9876543210`
- `************` → `9876543210`

Only valid Indian mobile numbers are accepted.

## Error Handling

The endpoint includes comprehensive error handling:

- Input validation for phone number format using TelephoneNumber gem
- Authorization checks for agent partner role
- Conflict detection for existing RM assignments
- Database error handling with appropriate HTTP status codes
- Logging of unexpected errors for debugging

## Security

- Requires valid admin session authentication
- Role-based access control (agent_partner role required)
- Input sanitization and validation using TelephoneNumber gem
- Secure error messages that don't leak sensitive information

## Usage Example

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Cookie: [admin_session_cookie]" \
  -d '{"phone": "9876543210"}' \
  https://api.praja.buzz/admin/agent-partners/start-lead-process
```

## Integration Notes

- The endpoint integrates with existing User, PremiumPitch, and UserMetadatum models
- RM assignment uses the existing UsersConcern methods (`get_rm_user`, `save_rm_user`)
- Phone number validation uses the TelephoneNumber gem for consistency with other parts of the application
- Mixpanel sync is automatically triggered when RM assignment is made
- The endpoint follows Rails conventions and existing codebase patterns 