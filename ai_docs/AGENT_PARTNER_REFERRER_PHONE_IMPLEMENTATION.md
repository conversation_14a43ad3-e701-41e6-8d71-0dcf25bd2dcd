# Agent Partner Referrer Phone Implementation

## Overview

This document outlines the implementation of phone-based referrer lookup functionality for agent partners in the `Admin::RmFlowController.save_layout_as_draft` action.

## Changes Made

### 1. Controller Updates

#### File: `app/controllers/admin/rm_flow_controller.rb`

**Enhanced `save_layout_as_draft` method:**
- Added phone number handling logic for agent partners only
- Implemented phone normalization using TelephoneNumber gem
- Added user lookup by normalized phone number
- Added validation for user existence
- Maintained existing referrer_id flow for non-agent partners

**Used shared phone normalization:**
- Includes `PhoneNormalizer` module which provides `normalize_phone_number(phone)` method
- This eliminates code duplication between AgentPartnersController and RmFlowController

### 2. Code Deduplication

#### File: `app/modules/phone_normalizer.rb`

**Utilized existing shared module:**
- `PhoneNormalizer` module already existed and was included in `AgentPartnersController`
- `RmFlowController` now also includes this module to share the `normalize_phone_number` method
- Eliminated code duplication by using the existing shared implementation
- Updated test files to use shared examples instead of duplicating phone normalization tests

### 3. Internationalization Updates

No additional I18n keys were required. The implementation uses existing error messages for user validation.

### 4. Test Coverage

#### Shared Test Examples: `spec/shared_examples/phone_normalizer_shared_examples.rb`

**Phone normalization tests moved to shared examples:**
- Comprehensive phone normalization testing for various formats
- Error handling for invalid phone numbers
- Test cases for different mobile number prefixes (6, 7, 8, 9)
- TelephoneNumber parsing error handling

#### Controller-Specific Tests: `spec/controllers/admin/rm_flow_controller_spec.rb`

**Added comprehensive test coverage:**
- Agent partner with valid referrer phone (creates UserReferral)
- User not found (404 error)
- Invalid phone format (404 error)
- Referrer user with no active premium layout (successfully creates referral)
- Referrer user with inactive premium layout (successfully creates referral)
- No referrer_phone provided (graceful handling)
- Non-agent partner behavior (ignores referrer_phone, uses referrer_id)
- Includes shared phone normalization examples

#### Updated AgentPartnersController Tests

**Removed duplicate tests:**
- Replaced duplicate phone normalization tests with shared examples
- Maintained controller-specific behavior tests

## Functionality Details

### Phone Number Normalization

The implementation uses the TelephoneNumber gem to normalize phone numbers:

1. Parses phone number with India (:IN) country code
2. Validates it's a mobile number
3. Extracts national number without formatting
4. Strips leading '0' if present
5. Converts to integer

### Agent Partner Flow

1. **Check Agent Partner Role**: Only process `referrer_phone` if `current_admin_user.agent_partner_role?` is true
2. **Normalize Phone**: Use TelephoneNumber gem to normalize the input phone
3. **Find User**: Look up user by normalized phone number
4. **Validate User**: Ensure user exists
5. **Set Referrer ID**: Convert phone lookup result to user ID for existing referral logic

### Error Handling

- **User Not Found (404)**: When phone normalization fails or no user exists with that phone
- **Graceful Fallback**: When no referrer_phone is provided, existing logic continues unchanged

### Non-Agent Partner Behavior

- Regular admin users are unaffected by this change
- `referrer_phone` parameter is ignored for non-agent partners
- Existing `referrer_id` logic continues to work as before

## Technical Implementation

### Key Components

1. **Phone Normalization**: Shared `PhoneNormalizer` module provides consistent normalization logic
2. **User Validation**: Simple user existence check
3. **Error Messages**: Leverages existing I18n infrastructure
4. **Database Integration**: Works with existing UserReferral model and referral logic
5. **Code Reusability**: Module pattern eliminates duplication between controllers

### Security Considerations

- Agent partner role validation prevents unauthorized access to phone lookup feature
- Phone normalization prevents format-based attacks
- User existence validation prevents invalid referrals

### Performance Considerations

- Phone normalization happens only for agent partners with referrer_phone
- Single database query for user lookup by phone
- Minimal overhead for existing non-agent partner flow

## Testing Strategy

The test suite covers:

1. **Happy Path**: Valid phone numbers creating referrals
2. **Format Variations**: Multiple phone number formats
3. **Error Cases**: Invalid phones, missing users
4. **Edge Cases**: Empty phone, non-agent partners, users without layouts
5. **Regression Protection**: Ensures existing functionality remains intact

## Deployment Notes

- Backward compatible: existing API calls continue to work
- New functionality is opt-in (only when referrer_phone is provided)
- No database migrations required
- No additional I18n keys required

## Usage Example

### Agent Partner Request
```json
POST /admin/save-layout-as-draft
{
  "user_id": 123,
  "status": "rm_draft",
  "referrer_phone": "+91 9123456789",
  "badge_free_text": "Test Badge",
  "poster_affiliated_party_id": 456,
  "user_poster_photos": {},
  "protocol_leader_circles": {}
}
```

### Success Response
```json
{
  "message": "Layout draft saved successfully"
}
```

### Error Responses

**User Not Found:**
```json
{
  "message": "User not found"
}
```

## Future Enhancements

1. **Audit Logging**: Track phone-based referral lookups for monitoring
2. **Rate Limiting**: Prevent abuse of phone lookup functionality
3. **Phone Validation**: Additional validation for Indian mobile numbers
4. **Performance Optimization**: Caching for frequent phone lookups