# lib/middleware/sidekiq_custom_css.rb
class SidekiqCustomCss
  INJECT_PATH = '/sidekiq/recurring-jobs' # change if you mounted under a different path

  def initialize(app)
    @app = app
  end

  def call(env)
    # only touch Sidekiq UI requests
    if env['PATH_INFO'].start_with?(INJECT_PATH)
      status, headers, response = @app.call(env)

      body = response.each.inject('') { |buf, part| buf << part }
      # inject your stylesheet link
      body.sub!(%r{</head>}, %(  <link rel="stylesheet" href="/stylesheets/sidekiq_custom.css">\n</head>))
      headers['Content-Length'] = body.bytesize.to_s
      [status, headers, [body]]
    else
      @app.call(env)
    end
  end
end
