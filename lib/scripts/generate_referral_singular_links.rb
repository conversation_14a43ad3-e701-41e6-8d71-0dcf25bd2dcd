# frozen_string_literal: true

user_ids = [1413878]

links_hash = {}

user_ids.each do |user_id|
  deeplink_uri = URI.parse("praja-app://buzz.praja.app/single-step-pay-wall")
  deeplink_uri.query = URI.encode_www_form({referred_by_user_id: user_id})

  link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/hul72')

  link_uri.query = URI.encode_www_form({
                                         _dl: deeplink_uri.to_s,
                                         _ddl: deeplink_uri.to_s,
                                         _p: { referred_by_user_id: user_id, utm_campaign: "WATI_REFERRAL_EXP" }.to_json
                                       })
  link = link_uri.to_s
  link = Singular.shorten_link(link)

  links_hash[user_id] = link
end;0
