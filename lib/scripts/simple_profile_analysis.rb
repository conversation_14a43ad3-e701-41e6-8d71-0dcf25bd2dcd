# Simple Profile Image Evaluation Analysis
# Copy and paste this entire script into Rails console

puts "=" * 80
puts "PROFILE IMAGE EVALUATION ANALYSIS"
puts "=" * 80
puts "Analyzing Admin Medium IDs from 1903 to 1914..."
puts

results = []
worker = ProfileImageEvaluationWorker.new

(1903..1914).each do |admin_medium_id|
  puts "Processing Admin Medium ID: #{admin_medium_id}"

  admin_medium = AdminMedium.find_by(id: admin_medium_id)

  if admin_medium.nil?
    puts "  ❌ Admin Medium not found"
    puts
    next
  end

  if admin_medium.media_type != 'photo'
    puts "  ❌ Not a photo (media_type: #{admin_medium.media_type})"
    puts
    next
  end

  photo_url = admin_medium.url
  puts "  📸 Photo URL: #{photo_url}"

  begin
    # Use the worker's evaluation method
    response = worker.send(:evaluate_profile_image, photo_url)
    content = response.dig("choices", 0, "message", "content")

    if content.present?
      evaluation_data = JSON.parse(content)

      result = {
        admin_medium_id: admin_medium_id,
        photo_url: photo_url,
        total_score: evaluation_data["total_score"],
        age: evaluation_data["age"] || evaluation_data["estimated_age"],
        premium_dressing_score: evaluation_data["premium_dressing_score"],
        premium_dressing_reason: evaluation_data["premium_dressing_reason"],
        photo_quality_score: evaluation_data["photo_quality_score"],
        photo_quality_reason: evaluation_data["photo_quality_reason"],
        gold_status_score: evaluation_data["gold_status_score"],
        gold_status_reason: evaluation_data["gold_status_reason"]
      }

      results << result

      puts "  ✅ EVALUATION RESULTS:"
      puts "     Total Score: #{result[:total_score]}"
      puts "     Estimated Age: #{result[:age]}"
      puts "     Premium Dressing: #{result[:premium_dressing_score]} (#{result[:premium_dressing_reason]})"
      puts "     Photo Quality: #{result[:photo_quality_score]} (#{result[:photo_quality_reason]})"
      puts "     Gold Status: #{result[:gold_status_score]} (#{result[:gold_status_reason]})"
    else
      puts "  ❌ Failed to parse evaluation response"
    end
  rescue StandardError => e
    puts "  ❌ Evaluation failed: #{e.message}"
  end

  puts
end

# Print summary
puts "=" * 80
puts "ANALYSIS SUMMARY"
puts "=" * 80
puts "Total Admin Mediums processed: 12"
puts "Successful evaluations: #{results.count}"
puts "Failed evaluations: #{12 - results.count}"
puts

if results.any?
  puts "DETAILED RESULTS:"
  puts "-" * 80

  results.each do |result|
    puts "ID: #{result[:admin_medium_id]}"
    puts "URL: #{result[:photo_url]}"
    puts "Total Score: #{result[:total_score]}"
    puts "Age: #{result[:age]}"
    puts "Premium Dressing: #{result[:premium_dressing_score]} - #{result[:premium_dressing_reason]}"
    puts "Photo Quality: #{result[:photo_quality_score]} - #{result[:photo_quality_reason]}"
    puts "Gold Status: #{result[:gold_status_score]} - #{result[:gold_status_reason]}"
    puts "-" * 40
  end

  # Calculate averages
  avg_total_score = results.map { |r| r[:total_score] }.sum.to_f / results.count
  avg_age = results.map { |r| r[:age] }.compact.sum.to_f / results.map { |r| r[:age] }.compact.count
  avg_premium_dressing = results.map { |r| r[:premium_dressing_score] }.sum.to_f / results.count
  avg_photo_quality = results.map { |r| r[:photo_quality_score] }.sum.to_f / results.count
  avg_gold_status = results.map { |r| r[:gold_status_score] }.sum.to_f / results.count

  puts
  puts "AVERAGES:"
  puts "Average Total Score: #{avg_total_score.round(2)}"
  puts "Average Age: #{avg_age.round(1)}" if avg_age > 0
  puts "Average Premium Dressing Score: #{avg_premium_dressing.round(2)}"
  puts "Average Photo Quality Score: #{avg_photo_quality.round(2)}"
  puts "Average Gold Status Score: #{avg_gold_status.round(2)}"
end

puts
puts "Analysis completed!"
